plugins {
    id 'com.jfrog.artifactory' apply false
    id 'io.gitlab.arturbosch.detekt'
    id 'io.spring.dependency-management'
    id 'net.researchgate.release'
    id 'org.owasp.dependencycheck'
    id 'org.jetbrains.kotlin.jvm' apply false
    id 'org.jetbrains.kotlin.plugin.allopen' apply false
    id 'org.jetbrains.kotlin.plugin.noarg' apply false
    id 'org.jetbrains.kotlin.plugin.spring' apply false
    id 'org.springframework.boot' apply false
    id 'com.adarshr.test-logger' apply false
    id 'com.palantir.docker' apply false
}

apply from: 'dependency.gradle'

apply plugin: 'net.researchgate.release'

allprojects {

    def jfrogUser = System.getProperty('jfrogUser') != null ? System.getProperty('jfrogUser') : "localUser"
    def jfrogPassword = System.getProperty('jfrogPassword') != null ? System.getProperty('jfrogPassword') : "localPassword"

    repositories {
        mavenCentral()
        google()
        jcenter()
        maven { url "https://repo.spring.io/milestone" }
        maven { url "https://s3-us-west-2.amazonaws.com/dynamodb-local/release" }
        maven {
            url "https://loyalty.jfrog.io/loyalty/libs-release"
            credentials {
                username = "${jfrogUser}"
                password = "${jfrogPassword}"
            }
        }
        maven {
            url "https://loyalty.jfrog.io/loyalty/libs-snapshot"
            credentials {
                username = "${jfrogUser}"
                password = "${jfrogPassword}"
            }
        }
    }
}

subprojects {
    apply plugin: 'kotlin'
    apply plugin: 'io.spring.dependency-management'
    apply plugin: 'jacoco'

    sourceCompatibility = 11

    compileKotlin {
        kotlinOptions {
            freeCompilerArgs = ["-Xjsr305=strict"]
            jvmTarget = JavaVersion.VERSION_11.toString()
        }
    }

    compileTestKotlin {
        kotlinOptions {
            freeCompilerArgs = ["-Xjsr305=strict"]
            jvmTarget = JavaVersion.VERSION_11.toString()
        }
    }

    dependencies {
        implementation(
                'com.loyalty.nova:nova-common',
                'com.loyalty.nova:nova-common-events'
        )

        testImplementation(
                'org.junit.jupiter:junit-jupiter-api',
                'org.springframework.boot:spring-boot-starter-test',
                'org.junit.jupiter:junit-jupiter-params',
                'org.mockito:mockito-core',
                'org.mockito:mockito-junit-jupiter',
                'org.hamcrest:java-hamcrest',
                'org.jetbrains.kotlin:kotlin-test'
        )

        testRuntimeOnly(
                'org.junit.jupiter:junit-jupiter-engine',
                'com.almworks.sqlite4java:sqlite4java'
        )
    }

    dependencyManagement {
        dependencies {
            libraries.each {
                library -> dependency library.value
            }
            dependency("org.springframework.boot:spring-boot-starter-test:${versions.springBootVersion}") {
                exclude 'org.hamcrest:hamcrest-core'
                exclude 'org.hamcrest:hamcrest-library'
            }
            dependency("org.apache.jmeter:ApacheJMeter_core:${versions.jmeterVersion}") {
                exclude 'org.apache.logging.log4j:log4j-slf4j-impl'
            }
            dependency("org.apache.jmeter:ApacheJMeter_http:${versions.jmeterVersion}") {
                exclude 'org.apache.logging.log4j:log4j-slf4j-impl'
            }
            dependency("kg.apc:jmeter-plugins-casutg:${versions.jmeterPluginsCasutgVersion}") {
                exclude 'org.apache.logging.log4j:log4j-slf4j-impl'
            }
        }

        imports {
            mavenBom "org.jetbrains.kotlin:kotlin-bom:${versions.kotlinVersion}"
            mavenBom "org.springframework:spring-framework-bom:${versions.springVersion}"
            mavenBom "org.springframework.boot:spring-boot-dependencies:${versions.springBootVersion}"
            mavenBom "org.springframework.cloud:spring-cloud-function-dependencies:${versions.springCloudVersion}"
        }
    }

    jacoco {
        toolVersion = "0.8.7"
        reportsDir = file("$buildDir/reports")
    }

    jacocoTestReport {
        reports {
            xml.enabled = true
            xml.setDestination(file("${buildDir}/reports/jacoco/report.xml"))
            html.enabled = true
            html.setDestination(file("${buildDir}/reports/jacoco/"))
        }

        // what to exclude from coverage report
        // UI, "noise", generated classes, platform classes, etc.
        def excludes = [
                '**/R.class',
                '**/R$*.class',
                '**/*$ViewInjector*.*',
                '**/BuildConfig.*',
                '**/Manifest*.*',
                '**/*Test*.*',
                '**/*Fragment.*',
                '**/*Activity.*'
        ]
        getClassDirectories().setFrom(fileTree(
                dir: "$buildDir/classes/kotlin/main"
        ))
        def coverageSourceDirs = [
                "src/main/kotlin"
        ]
        getAdditionalSourceDirs().setFrom(files(coverageSourceDirs))
        getSourceDirectories().setFrom(files(coverageSourceDirs))
        getExecutionData().setFrom(files("$buildDir/jacoco/test.exec"))
    }

}

detekt {
    failFast = false
    config = files("detekt.yml")
    input = files("$projectDir")
    filters = ".*/resources/.*,.*/build/.*"
}

release {
    failOnSnapshotDependencies = true
    revertOnFail = true
    git {
        requireBranch = 'master'
    }
}

task build {}

task publish {}

afterReleaseBuild.dependsOn getTasksByName('publish', true)




