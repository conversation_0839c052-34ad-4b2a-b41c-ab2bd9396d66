sonar.projectName=offer-management-api
sonar.projectKey=offer-management-api
sonar.junit.reportPaths=offer-management-api-service/build/test-results/test
sonar.dynamicAnalysis=reuseReports
sonar.tests=offer-management-api-service/src/test
sonar.sources=offer-management-api-service/src/main
sonar.java.binaries=offer-management-api-service/build/classes
sonar.java.libraries=offer-management-api-service/build/libs/app.jar
sonar.java.test.libraries=offer-management-api-service/build/libs/app.jar
sonar.language=java
sonar.java.coveragePlugin=jacoco
sonar.coverage.jacoco.xmlReportPaths=offer-management-api-service/build/reports/jacoco/report.xml