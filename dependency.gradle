ext.versions = [
        'stsVersion'                       : '2.17.84',
        'kinesisSdkVersion'                : '2.1.3',
        'awsKinesisVersion'                : '1.0.3',
        'awsJavaSdkBomVersion'             : '1.12.686',
        'awsLambdaJavaCoreVersion'         : '1.2.0',
        'awsLambdaJavaEventsVersion'       : '2.2.3',
        'awsLambdaJavaLog4jVersion'        : '1.5.1',
        'commonsTextVersion'               : '1.8',
        'cucumberVersion'                  : '4.2.0',
        'dependencyCheckGradleVersion'     : '4.0.2',
        'dependencycheckOWASPVersion'      : '5.2.4',
        'detektVersion'                    : '1.2.0',
        'DynamoDBLocalVersion'             : '1.11.119',
        'flywaydbVersion'                  : '5.2.4',
        'gradleDockerVersion'              : '0.20.1',
        'gradleReleaseVersion'             : '2.6.0',
        'gradleTestLoggerPluginVersion'    : '1.5.0',
        'hamcrestVersion'                  : '2.0.0.0',
        'h2Version'                        : '1.4.199',
        'jacksonDatabindVersion'           : '2.9.7',
        'jacksonVersion'                   : '2.9.7',
        'jfrogVersion'                     : '4.29.3',
        'jmeterPluginsCasutgVersion'       : '2.6',
        'jmeterVersion'                    : '5.0',
        'jsonpathVersion'                  : '2.4.0',
        'junitJupiterVersion'              : '5.2.0',
        'kotlintestVersion'                : '3.3.1',
        'kotlinVersion'                    : '1.5.30',
        'logbackClassicVersion'            : '1.2.3',
        'logbackContrib'                   : '0.1.5',
        'mockitoJunitJupiterVersion'       : '3.6.28',
        'mockitoVersion'                   : '3.6.28',
        'modelmapperVersion'               : '2.3.2',
        'novaCommonEventsVersion'          : '0.0.228',
        'novaCommonTestVersion'            : '0.0.110',
        'novaCommonVersion'                : '0.0.57',
        'opencsvVersion'                   : '5.0',
        'palantirVersion'                  : '0.22.1',
        'postgresqlVersion'                : '42.7.2',
        'reflectionsVersion'               : '0.9.11',
        'shadowVersion'                    : '4.0.3',
        'skyScreamerVersion'               : '1.5.0',
        'springBootVersion'                : '2.4.13',
        'springCloudVersion'               : '3.1.7',
        'springDependencyManagementVersion': '1.1.0',
        'springVersion'                    : '5.3.13',
        'sqlite4javaVersion'               : '1.0.392',
        'swaggerVersion'                   : '3.0.0',
        'testLoggerVersion'                : '1.6.0',
        'typesafeConfigVersion'            : '1.3.3',
        'wiremockVersion'                  : '2.21.0',
        'apacheLog4j'                      : '2.17.1',
        'json'                             : '20210307',
        'snakeYamlVersion'                 : '2.0',
        'protobufVersion'                  : '4.28.2',
        'beanutilsVersion'                 : '1.11.0',
        'nettyVersion'                     : '4.1.80.Final',
]

ext.libraries = [
        'bom'                              : "software.amazon.awssdk:bom:${versions.stsVersion}",
        'sts'                              : "software.amazon.awssdk:sts:${versions.stsVersion}",
        'kinesis'                          : "software.amazon.awssdk:kinesis:${versions.kinesisSdkVersion}",
        'aws-java-sdk-sts'                 : "com.amazonaws:aws-java-sdk-sts:${versions.awsJavaSdkBomVersion}",
        'ApacheJMeter_core'                : "org.apache.jmeter:ApacheJMeter_http:${versions.jmeterVersion}",
        'ApacheJMeter_http'                : "org.apache.jmeter:ApacheJMeter_http:${versions.jmeterVersion}",
        'aws-java-sdk-bom'                 : "com.amazonaws:aws-java-sdk-bom:${versions.awsJavaSdkBomVersion}",
        'aws-java-sdk-core'                : "com.amazonaws:aws-java-sdk-core:${versions.awsJavaSdkBomVersion}",
        'aws-java-sdk-dynamodb'            : "com.amazonaws:aws-java-sdk-dynamodb:${versions.awsJavaSdkBomVersion}",
        'aws-java-sdk-kinesis'             : "com.amazonaws:aws-java-sdk-kinesis:${versions.awsJavaSdkBomVersion}",
        'aws-java-sdk-lambda'              : "com.amazonaws:aws-java-sdk-lambda:${versions.awsJavaSdkBomVersion}",
        'aws-java-sdk-secretsmanager'      : "com.amazonaws:aws-java-sdk-secretsmanager:${versions.awsJavaSdkBomVersion}",
        'aws-lambda-java-core'             : "com.amazonaws:aws-lambda-java-core:${versions.awsLambdaJavaCoreVersion}",
        'aws-lambda-java-events'           : "com.amazonaws:aws-lambda-java-events:${versions.awsLambdaJavaEventsVersion}",
        'cucumber-java8'                   : "io.cucumber:cucumber-java8:${versions.cucumberVersion}",
        'cucumber-junit'                   : "io.cucumber:cucumber-junit:${versions.cucumberVersion}",
        'cucumber-spring'                  : "io.cucumber:cucumber-spring:${versions.cucumberVersion}",
        'dependency-check-gradle'          : "org.owasp:dependency-check-gradle:${versions.dependencyCheckGradleVersion}",
        'detekt-formatting'                : "io.gitlab.arturbosch.detekt:detekt-formatting:${versions.detektVersion}",
        'detekt-gradle-plugin'             : "gradle.plugin.io.gitlab.arturbosch.detekt:detekt-gradle-plugin:${versions.detektVersion}",
        'DynamoDBLocal'                    : "com.amazonaws:DynamoDBLocal:${versions.DynamoDBLocalVersion}",
        'flywaydb'                         : "org.flywaydb:flyway-core:${versions.flywaydbVersion}",
        'gradle-release'                   : "net.researchgate:gradle-release:${versions.gradleReleaseVersion}",
        'gradle-test-logger-plugin '       : "com.adarshr:gradle-test-logger-plugin:${versions.gradleTestLoggerPluginVersion}",
        'gradle-test-logger-plugin'        : "com.adarshr:gradle-test-logger-plugin:${versions.gradleTestLoggerPluginVersion}",
        'jackson-core'                     : "com.fasterxml.jackson.core:jackson-core:${versions.jacksonVersion}",
        'jackson-databind'                 : "com.fasterxml.jackson.core:jackson-databind:${versions.jacksonDatabindVersion}",
        'jackson-dataformat-cbor'          : "com.fasterxml.jackson.dataformat:jackson-dataformat-cbor:${versions.jacksonVersion}",
        'jackson-datatype-joda'            : "com.fasterxml.jackson.datatype:jackson-datatype-joda:${versions.jacksonVersion}",
        'jackson-datatype-jsr310'          : "com.fasterxml.jackson.datatype:jackson-datatype-jsr310:${versions.jacksonVersion}",
        'jackson-module-kotlin'            : "com.fasterxml.jackson.module:jackson-module-kotlin:${versions.jacksonVersion}",
        'java-hamcrest'                    : "org.hamcrest:java-hamcrest:${versions.hamcrestVersion}",
        'jmeter-plugins-casutg'            : "kg.apc:jmeter-plugins-casutg:${versions.jmeterPluginsCasutgVersion}",
        'json-path'                        : "com.jayway.jsonpath:json-path:${versions.jsonpathVersion}",
        'junit-jupiter-api'                : "org.junit.jupiter:junit-jupiter-api:${versions.junitJupiterVersion}",
        'junit-jupiter-engine'             : "org.junit.jupiter:junit-jupiter-engine:${versions.junitJupiterVersion}",
        'junit-jupiter-params'             : "org.junit.jupiter:junit-jupiter-params:${versions.junitJupiterVersion}",
        'kotlin-allopen'                   : "org.jetbrains.kotlin:kotlin-allopen:${versions.kotlinVersion}",
        'kotlin-gradle-plugin'             : "org.jetbrains.kotlin:kotlin-gradle-plugin:${versions.kotlinVersion}",
        'kotlin-reflect'                   : "org.jetbrains.kotlin:kotlin-reflect:${versions.kotlinVersion}",
        'kotlin-stdlib-jdk8'               : "org.jetbrains.kotlin:kotlin-stdlib-jdk8:${versions.kotlinVersion}",
        'kotlin-test'                      : "org.jetbrains.kotlin:kotlin-test:${versions.kotlinVersion}",
        'kotlin-compiler-embeddable'       : "org.jetbrains.kotlin:kotlin-compiler-embeddable:${versions.kotlinVersion}",
        'kotlin-daemon-client'             : "org.jetbrains.kotlin:kotlin-daemon-client:${versions.kotlinVersion}",
        'kotlin-daemon-embeddable'         : "org.jetbrains.kotlin:kotlin-daemon-embeddable:${versions.kotlinVersion}",
        'kotlin-script-runtime'            : "org.jetbrains.kotlin:kotlin-script-runtime:${versions.kotlinVersion}",
        'kotlin-script-util'               : "org.jetbrains.kotlin:kotlin-script-util:${versions.kotlinVersion}",
        'kotlin-scripting-common'          : "org.jetbrains.kotlin:kotlin-scripting-common:${versions.kotlinVersion}",
        'kotlin-scripting-jvm'             : "org.jetbrains.kotlin:kotlin-scripting-jvm:${versions.kotlinVersion}",
        'kotlin-stdlib'                    : "org.jetbrains.kotlin:kotlin-stdlib:${versions.kotlinVersion}",
        'kotlin-stdlib-common'             : "org.jetbrains.kotlin:kotlin-stdlib-common:${versions.kotlinVersion}",
        'kotlin-stdlib-jdk7'               : "org.jetbrains.kotlin:kotlin-stdlib-jdk7:${versions.kotlinVersion}",
        'kotlin-test-annotations-common'   : "org.jetbrains.kotlin:kotlin-test-annotations-common:${versions.kotlinVersion}",
        'kotlin-test-junit5'               : "org.jetbrains.kotlin:kotlin-test-junit5:${versions.kotlinVersion}",
        'kotlin-test-junit'                : "org.jetbrains.kotlin:kotlin-test-junit:${versions.kotlinVersion}",
        'kotlin-test-common'               : "org.jetbrains.kotlin:kotlin-test-common:${versions.kotlinVersion}",
        'kotlintest-extensions-spring'     : "io.kotlintest:kotlintest-extensions-spring:${versions.kotlintestVersion}",
        'libsqlite4java-linux-amd64'       : "com.almworks.sqlite4java:libsqlite4java-linux-amd64:${versions.sqlite4javaVersion}",
        'libsqlite4java-linux-i386'        : "com.almworks.sqlite4java:libsqlite4java-linux-i386:${versions.sqlite4javaVersion}",
        'libsqlite4java-osx'               : "com.almworks.sqlite4java:libsqlite4java-osx:${versions.sqlite4javaVersion}",
        'logback-classic'                  : "ch.qos.logback:logback-classic:${versions.logbackClassicVersion}",
        'logback-jackson'                  : "ch.qos.logback.contrib:logback-jackson:${versions.logbackContrib}",
        'logback-json-classic'             : "ch.qos.logback.contrib:logback-json-classic:${versions.logbackContrib}",
        'log4j-core'                       : "org.apache.logging.log4j:log4j-core:${versions.apacheLog4j}",
        'log4j-api'                        : "org.apache.logging.log4j:log4j-api:${versions.apacheLog4j}",
        'mockito-core'                     : "org.mockito:mockito-core:${versions.mockitoVersion}",
        'mockito-junit-jupiter'            : "org.mockito:mockito-junit-jupiter:${versions.mockitoJunitJupiterVersion}",
        'modelmapper'                      : "org.modelmapper:modelmapper:${versions.modelmapperVersion}",
        'nova-common'                      : "com.loyalty.nova:nova-common:${versions.novaCommonVersion}",
        'nova-common-events'               : "com.loyalty.nova:nova-common-events:${versions.novaCommonEventsVersion}",
        'nova-common-test'                 : "com.loyalty.nova:nova-common-test:${versions.novaCommonTestVersion}",
        'amazon-kinesis-aggregator'        : "com.amazonaws:amazon-kinesis-aggregator:${versions.awsKinesisVersion}",
        'amazon-kinesis-deaggregator'      : "com.amazonaws:amazon-kinesis-deaggregator:${versions.awsKinesisVersion}",
        'aws-lambda-java-log4j2'           : "com.amazonaws:aws-lambda-java-log4j2:${versions.awsLambdaJavaLog4jVersion}",
        'opencsv'                          : "com.opencsv:opencsv:${versions.opencsvVersion}",
        'postgresql'                       : "org.postgresql:postgresql:${versions.postgresqlVersion}",
        'reflections'                      : "org.reflections:reflections:${versions.reflectionsVersion}",
        'shadow'                           : "com.github.jengelman.gradle.plugins:shadow:${versions.shadowVersion}",
        'skyScreamer'                      : "org.skyscreamer:jsonassert:${versions.skyScreamerVersion}",
        'spring-boot-gradle-plugin'        : "org.springframework.boot:spring-boot-gradle-plugin:${versions.springBootVersion}",
        'spring-boot-starter-actuator'     : "org.springframework.boot:spring-boot-starter-actuator:${versions.springBootVersion}",
        'spring-boot-starter-data-jpa'     : "org.springframework.boot:spring-boot-starter-data-jpa:${versions.springBootVersion}",
        'spring-boot-starter-data-rest'    : "org.springframework.boot:spring-boot-starter-data-rest:${versions.springBootVersion}",
        'spring-boot-starter-test'         : "org.springframework.boot:spring-boot-starter-test:${versions.springBootVersion}",
        'spring-boot-starter-web'          : "org.springframework.boot:spring-boot-starter-web:${versions.springBootVersion}",
        'spring-cloud-contract-wiremock'   : "org.springframework.cloud:spring-cloud-contract-wiremock:${versions.springCloudVersion}",
        'spring-cloud-function-adapter-aws': "org.springframework.cloud:spring-cloud-function-adapter-aws:${versions.springCloudVersion}",
        'spring-cloud-function-kotlin'     : "org.springframework.cloud:spring-cloud-function-kotlin:${versions.springCloudVersion}",
        'spring-cloud-starter-function-web': "org.springframework.cloud:spring-cloud-starter-function-web:${versions.springCloudVersion}",
        'spring-context'                   : "org.springframework:spring-context:${versions.springVersion}",
        'springfox-swagger-ui'             : "io.springfox:springfox-swagger-ui:${versions.swaggerVersion}",
        'springfox-swagger2'               : "io.springfox:springfox-swagger2:${versions.swaggerVersion}",
        'sqlite4java'                      : "com.almworks.sqlite4java:sqlite4java:${versions.sqlite4javaVersion}",
        'sqlite4java-win32-x64'            : "com.almworks.sqlite4java:sqlite4java-win32-x64:${versions.sqlite4javaVersion}",
        'sqlite4java-win32-x86'            : "com.almworks.sqlite4java:sqlite4java-win32-x86:${versions.sqlite4javaVersion}",
        'typesafe-config'                  : "com.typesafe:config:${versions.typesafeConfigVersion}",
        'wiremock-jre8'                    : "com.github.tomakehurst:wiremock-jre8:${versions.wiremockVersion}",
        'snakeyaml'                        : "org.yaml:snakeyaml:${versions.snakeYamlVersion}",
        'h2'                               : "com.h2database:h2:${versions.h2Version}",
        'json'                             : "org.json:json:${versions.json}",
        'protobuf'                         : "com.google.protobuf:protobuf-java:${versions.protobufVersion}",
        'beanutils'                        : "commons-beanutils:commons-beanutils:${versions.beanutilsVersion}",
        'netty-codec'                      : "io.netty:netty-codec:${versions.nettyVersion}"
]


