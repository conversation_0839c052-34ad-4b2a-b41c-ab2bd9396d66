Feature: Delete Offer Testcases

  Scenario Outline: Create Offer and Delete Offer by Id
#    Given running WireMock on host <wireMockHost> and port <wireMockPort>
#    Given removing all WireMock mappings
#    Given stabbing ContentGenerationServiceMock for stub <contentGenerationServiceMockStub>
    When calling createOffer with file name Rexall_spend and endpoint <endpoint>
      | path          | value                       |
      | @.displayDate | shiftDisplayDateHours(-240) |
      | @.startDate   | shiftStartDateHours(-200)   |
      | @.endDate     | shiftEndDateHours(24)       |
    Then verifying status code is 200
    And calling DeleteOffers with endpoint <endpoint> and OfferId included
    Then verifying status code is 200
    @local
    Examples:
      | endpoint                                   | wireMockHost | wireMockPort | contentGenerationServiceMockStub |
      | http://localhost:8080/offer-management-api | localhost    | 9090         | ok                               |
    @sole
    Examples:
      | endpoint                                                            | wireMockHost | wireMockPort | contentGenerationServiceMockStub |
      | https://sole-services.dev.api.loyalty.com:3024/offer-management-api | localhost    | 9090         | ok                               |

  Scenario Outline: Create bulk offers then delete all
    When calling createBulkOffer with file name Bulk_Offers and endpoint <endpoint>
      | path                | value                 |
      | $.offers[*].endDate | shiftEndDateHours(24) |
    Then verifying status code is 200
    And calling GetOffers with endpoint <endpoint> with bulkId
    And calling DeleteOffers with endpoint <endpoint> and all OfferIds included
    Then verifying status code is 200
    @local
    Examples:
      | endpoint                                   |
      | http://localhost:8080/offer-management-api |
    @sole
    Examples:
      | endpoint                                                            |
      | https://sole-services.dev.api.loyalty.com:3024/offer-management-api |

  Scenario Outline: Create and publish Offer then prevent Delete Offer by Id
#    When running WireMock on host <wireMockHost> and port <wireMockPort>
#    And removing all WireMock mappings
#    And stabbing AirmilesOfferApiV2Mock for stub <airmilesOfferApiV2MockStub>
#    And stabbing ContentGenerationServiceMock for stub <contentGenerationServiceMockStub>
#    And stabbing OfferFacetsApiMock for stub ok
#    And stabbing PromotionApiMock for stub ok
    When calling createOffer with file name Rexall_spend and endpoint <endpoint>
      | path          | value                       |
      | @.displayDate | shiftDisplayDateHours(-240) |
      | @.startDate   | shiftStartDateHours(-200)   |
      | @.endDate     | shiftEndDateHours(24)       |
    Then verifying status code is 200
    Then calling Publish Offer with endpoint <endpoint>
    Then verifying status code is 204
    And calling DeleteOffers with endpoint <endpoint> and OfferId included
    Then verifying status code is 400

    @local
    Examples:
      | endpoint                                   | wireMockHost | wireMockPort | airmilesOfferApiV2MockStub | contentGenerationServiceMockStub |
      | http://localhost:8080/offer-management-api | localhost    | 9090         | ok                         | ok                               |
    @sole
    Examples:
      | endpoint                                                            | wireMockHost | wireMockPort | airmilesOfferApiV2MockStub | contentGenerationServiceMockStub |
      | https://sole-services.dev.api.loyalty.com:3024/offer-management-api | localhost    | 9090         | ok                         | ok                               |