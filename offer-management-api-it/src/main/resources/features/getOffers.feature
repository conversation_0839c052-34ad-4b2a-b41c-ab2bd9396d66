Feature: Get Offer Testcases

  Scenario Outline: Create Offer and Get Offer by Id
    When calling createOffer with file name <PERSON><PERSON>_spend and endpoint <endpoint>
      | path          | value                       |
      | @.displayDate | shiftDisplayDateHours(-240) |
      | @.startDate   | shiftStartDateHours(-200)   |
      | @.endDate     | shiftEndDateHours(24)       |
    Then verifying status code is 200
    And calling GetOffers with endpoint <endpoint> and Offer Id appended
    And verifying response body paths
      | path                   | op | value                                  |
      | $.content[0].partnerId |    | "3db0b3e0-4134-453e-8381-08e90f2b5141" |
#    @local
#    Examples:
#      | endpoint                                   |
#      | http://localhost:8080/offer-management-api |
    @sole
    Examples:
      | endpoint                                                            |
      | https://sole-services.dev.api.loyalty.com:3024/offer-management-api |


  Scenario Outline: Get Offer (with proxy) -> offerType=buy
    Given All test offers created and published through <endpoint>
    When calling GetOffers with endpoint <endpoint> with input parameter offerType=buy
    Then verifying response body paths
      | path                   | op | value                                  |
      | $.content[0].partnerId |    | "ae078d5c-8096-4283-a91c-5e5ce5dab7b4" |
      | $.content[1].partnerId |    | "87ab4dd0-65b5-41a3-acd2-b97540dc1aff" |
#    @local
#    Examples:
#      | endpoint                                   |
#      | http://localhost:8080/offer-management-api |
    @sole
    Examples:
      | endpoint                                                            |
      | https://sole-services.dev.api.loyalty.com:3024/offer-management-api |


  Scenario Outline: Get Offer -> offerType=buy&offerType=amCashDiscount
    Given All test offers created and published through <endpoint>
    When calling GetOffers with endpoint <endpoint> with input parameter offerType=buy&offerType=amCashDiscount
    Then verifying response body paths
      | path                   | op | value                                  |
      | $.content[0].partnerId |    | "ae078d5c-8096-4283-a91c-5e5ce5dab7b4" |
      | $.content[1].partnerId |    | "6ec2bd1e-b840-48b9-b43e-bc8664a62d8c" |
      | $.content[2].partnerId |    | "87ab4dd0-65b5-41a3-acd2-b97540dc1aff" |
#    @local
#    Examples:
#      | endpoint                                   |
#      | http://localhost:8080/offer-management-api |
    @sole
    Examples:
      | endpoint                                                            |
      | https://sole-services.dev.api.loyalty.com:3024/offer-management-api |


  Scenario Outline: Get Offer -> tags=megaMiles
    Given All test offers created and published through <endpoint>
    When calling GetOffers with endpoint <endpoint> with input parameter tags=4db99143-1768-4f85-a44a-d6fa16011f7b
    Then verifying response body paths
      | path                   | op | value                                  |
      | $.content[0].partnerId |    | "ae078d5c-8096-4283-a91c-5e5ce5dab7b4" |
#    @local
#    Examples:
#      | endpoint                                   |
#      | http://localhost:8080/offer-management-api |
    @sole
    Examples:
      | endpoint                                                            |
      | https://sole-services.dev.api.loyalty.com:3024/offer-management-api |


  Scenario Outline: Get Offer -> awardType=custom
    Given All test offers created and published through <endpoint>
    When calling GetOffers with endpoint <endpoint> with input parameter awardType=custom
    Then verifying response body paths
      | path                   | op | value                                  |
      | $.content[0].partnerId |    | "8dd859c1-55d1-4157-8afc-7607297bdfeb" |
#    @local
#    Examples:
#      | endpoint                                   |
#      | http://localhost:8080/offer-management-api |
    @sole
    Examples:
      | endpoint                                                            |
      | https://sole-services.dev.api.loyalty.com:3024/offer-management-api |


  Scenario Outline: Get Offer -> awardType=custom&awardType=flatMiles
    Given All test offers created and published through <endpoint>
    When calling GetOffers with endpoint <endpoint> with input parameter awardType=custom&awardType=flatMiles
    Then verifying response body paths
      | path                   | op | value                                  |
      | $.content[0].partnerId |    | "8dd859c1-55d1-4157-8afc-7607297bdfeb" |
      | $.content[1].partnerId |    | "70ec7537-dcfe-486d-b83b-78b756e5e4cb" |
      | $.content[2].partnerId |    | "ce7edb67-52e6-4245-9d9c-9f7936bdb370" |
      | $.content[3].partnerId |    | "87ab4dd0-65b5-41a3-acd2-b97540dc1aff" |
      | $.content[4].partnerId |    | "3db0b3e0-4134-453e-8381-08e90f2b5141" |
#    @local
#    Examples:
#      | endpoint                                   |
#      | http://localhost:8080/offer-management-api |
    @sole
    Examples:
      | endpoint                                                            |
      | https://sole-services.dev.api.loyalty.com:3024/offer-management-api |


  Scenario Outline: Get Offer -> qualifier=custom
    Given All test offers created and published through <endpoint>
    When calling GetOffers with endpoint <endpoint> with input parameter qualifier=custom
    Then verifying response body paths
      | path                   | op | value                                  |
      | $.content[0].partnerId |    | "8dd859c1-55d1-4157-8afc-7607297bdfeb" |
#    @local
#    Examples:
#      | endpoint                                   |
#      | http://localhost:8080/offer-management-api |
    @sole
    Examples:
      | endpoint                                                            |
      | https://sole-services.dev.api.loyalty.com:3024/offer-management-api |

  Scenario Outline: Get Offer -> qualifier=custom&qualifier=category
    Given All test offers created and published through <endpoint>
    When calling GetOffers with endpoint <endpoint> with input parameter qualifier=custom&qualifier=category
    Then verifying response body paths
      | path                   | op | value                                  |
      | $.content[0].partnerId |    | "8dd859c1-55d1-4157-8afc-7607297bdfeb" |
      | $.content[1].partnerId |    | "c1b1fa0b-0956-418d-be39-986e406dd780" |
#    @local
#    Examples:
#      | endpoint                                   |
#      | http://localhost:8080/offer-management-api |
    @sole
    Examples:
      | endpoint                                                            |
      | https://sole-services.dev.api.loyalty.com:3024/offer-management-api |


  Scenario Outline: Get Offer -> regions=YT
    Given All test offers created and published through <endpoint>
    When calling GetOffers with endpoint <endpoint> with input parameter regions=YT
    Then verifying response body paths
      | path                   | op | value                                  |
      | $.content[0].partnerId |    | "ae078d5c-8096-4283-a91c-5e5ce5dab7b4" |
      | $.content[1].partnerId |    | "8dd859c1-55d1-4157-8afc-7607297bdfeb" |
      | $.content[2].partnerId |    | "6ec2bd1e-b840-48b9-b43e-bc8664a62d8c" |
      | $.content[3].partnerId |    | "70ec7537-dcfe-486d-b83b-78b756e5e4cb" |
      | $.content[4].partnerId |    | "c1b1fa0b-0956-418d-be39-986e406dd780" |
      | $.content[5].partnerId |    | "ce7edb67-52e6-4245-9d9c-9f7936bdb370" |
#    @local
#    Examples:
#      | endpoint                                   |
#      | http://localhost:8080/offer-management-api |
    @sole
    Examples:
      | endpoint                                                            |
      | https://sole-services.dev.api.loyalty.com:3024/offer-management-api |


  Scenario Outline: Get Offer -> regions=YT&regions=NT
    Given All test offers created and published through <endpoint>
    When calling GetOffers with endpoint <endpoint> with input parameter regions=YT&regions=NT
    Then verifying response body paths
      | path                   | op | value                                  |
      | $.content[0].partnerId |    | "ae078d5c-8096-4283-a91c-5e5ce5dab7b4" |
      | $.content[1].partnerId |    | "8dd859c1-55d1-4157-8afc-7607297bdfeb" |
      | $.content[2].partnerId |    | "6ec2bd1e-b840-48b9-b43e-bc8664a62d8c" |
      | $.content[3].partnerId |    | "70ec7537-dcfe-486d-b83b-78b756e5e4cb" |
      | $.content[4].partnerId |    | "c1b1fa0b-0956-418d-be39-986e406dd780" |
      | $.content[5].partnerId |    | "ce7edb67-52e6-4245-9d9c-9f7936bdb370" |
      | $.content[6].partnerId |    | "3db0b3e0-4134-453e-8381-08e90f2b5141" |
#    @local
#    Examples:
#      | endpoint                                   |
#      | http://localhost:8080/offer-management-api |
    @sole
    Examples:
      | endpoint                                                            |
      | https://sole-services.dev.api.loyalty.com:3024/offer-management-api |


  Scenario Outline: Get Offer -> dateContext=live
    When calling GetOffers with endpoint <endpoint> with input parameter dateContext=live
    Then verifying response body paths
      | path                   | op | value                                  |
      | $.content[0].partnerId |    | "6ec2bd1e-b840-48b9-b43e-bc8664a62d8c" |
      | $.content[1].partnerId |    | "70ec7537-dcfe-486d-b83b-78b756e5e4cb" |
      | $.content[2].partnerId |    | "c1b1fa0b-0956-418d-be39-986e406dd780" |
      | $.content[3].partnerId |    | "ce7edb67-52e6-4245-9d9c-9f7936bdb370" |
      | $.content[4].partnerId |    | "87ab4dd0-65b5-41a3-acd2-b97540dc1aff" |
      | $.content[5].partnerId |    | "3db0b3e0-4134-453e-8381-08e90f2b5141" |
#    @local
#    Examples:
#      | endpoint                                   |
#      | http://localhost:8080/offer-management-api |
    @sole
    Examples:
      | endpoint                                                            |
      | https://sole-services.dev.api.loyalty.com:3024/offer-management-api |


  Scenario Outline: Get Offer -> dateContext=staged
    Given All test offers created and published through <endpoint>
    When calling GetOffers with endpoint <endpoint> with input parameter dateContext=staged
    Then verifying response body paths
      | path                   | op | value                                  |
      | $.content[0].partnerId |    | "ae078d5c-8096-4283-a91c-5e5ce5dab7b4" |
#    @local
#    Examples:
#      | endpoint                                   |
#      | http://localhost:8080/offer-management-api |
    @sole
    Examples:
      | endpoint                                                            |
      | https://sole-services.dev.api.loyalty.com:3024/offer-management-api |


  Scenario Outline: Get Offer -> status=draft
    Given All test offers created and published through <endpoint>
    When calling GetOffers with endpoint <endpoint> with input parameter status=draft
    Then verifying response body paths
      | path                   | op | value                                  |
      | $.content[0].partnerId |    | "8dd859c1-55d1-4157-8afc-7607297bdfeb" |
#    @local
#    Examples:
#      | endpoint                                   |
#      | http://localhost:8080/offer-management-api |
    @sole
    Examples:
      | endpoint                                                            |
      | https://sole-services.dev.api.loyalty.com:3024/offer-management-api |


  Scenario Outline: Get Offer -> status=published
    Given All test offers created and published through <endpoint>
    When calling GetOffers with endpoint <endpoint> with input parameter status=published
    Then verifying response body paths
      | path                   | op | value                                  |
      | $.content[0].partnerId |    | "ae078d5c-8096-4283-a91c-5e5ce5dab7b4" |
      | $.content[1].partnerId |    | "6ec2bd1e-b840-48b9-b43e-bc8664a62d8c" |
      | $.content[2].partnerId |    | "70ec7537-dcfe-486d-b83b-78b756e5e4cb" |
      | $.content[3].partnerId |    | "c1b1fa0b-0956-418d-be39-986e406dd780" |
      | $.content[4].partnerId |    | "ce7edb67-52e6-4245-9d9c-9f7936bdb370" |
      | $.content[5].partnerId |    | "87ab4dd0-65b5-41a3-acd2-b97540dc1aff" |
      | $.content[6].partnerId |    | "3db0b3e0-4134-453e-8381-08e90f2b5141" |
#    @local
#    Examples:
#      | endpoint                                   |
#      | http://localhost:8080/offer-management-api |
    @sole
    Examples:
      | endpoint                                                            |
      | https://sole-services.dev.api.loyalty.com:3024/offer-management-api |


  Scenario Outline: Get Offer -> status=published&status=draft
    Given All test offers created and published through <endpoint>
    When calling GetOffers with endpoint <endpoint> with input parameter status=published&status=draft
    Then verifying response body paths
      | path                   | op | value                                  |
      | $.content[0].partnerId |    | "ae078d5c-8096-4283-a91c-5e5ce5dab7b4" |
      | $.content[1].partnerId |    | "8dd859c1-55d1-4157-8afc-7607297bdfeb" |
      | $.content[2].partnerId |    | "6ec2bd1e-b840-48b9-b43e-bc8664a62d8c" |
      | $.content[3].partnerId |    | "70ec7537-dcfe-486d-b83b-78b756e5e4cb" |
      | $.content[4].partnerId |    | "c1b1fa0b-0956-418d-be39-986e406dd780" |
      | $.content[5].partnerId |    | "ce7edb67-52e6-4245-9d9c-9f7936bdb370" |
      | $.content[6].partnerId |    | "87ab4dd0-65b5-41a3-acd2-b97540dc1aff" |
      | $.content[7].partnerId |    | "3db0b3e0-4134-453e-8381-08e90f2b5141" |
#    @local
#    Examples:
#      | endpoint                                   |
#      | http://localhost:8080/offer-management-api |
    @sole
    Examples:
      | endpoint                                                            |
      | https://sole-services.dev.api.loyalty.com:3024/offer-management-api |


  Scenario Outline: Get Offer -> active=false
    Given All test offers created and published through <endpoint>
    When calling GetOffers with endpoint <endpoint> with input parameter active=false&issuanceCode=CPR4343
    Then verifying response body paths
      | path                   | op | value                                  |
      | $.content[0].partnerId |    | "3db0b3e0-4134-453e-8381-08e90f2b5141" |
#    @local
#    Examples:
#      | endpoint                                   |
#      | http://localhost:8080/offer-management-api |
    @sole
    Examples:
      | endpoint                                                            |
      | https://sole-services.dev.api.loyalty.com:3024/offer-management-api |


  Scenario Outline: Get Offer -> massOffer=false
  Given All test offers created and published through <endpoint>
    When calling GetOffers with endpoint <endpoint> with input parameter massOffer=false
    Then verifying response body paths
      | path                   | op | value                                  |
      | $.content[0].partnerId |    | "ce7edb67-52e6-4245-9d9c-9f7936bdb370" |
      | $.content[1].partnerId |    | "87ab4dd0-65b5-41a3-acd2-b97540dc1aff" |
      | $.content[2].partnerId |    | "3db0b3e0-4134-453e-8381-08e90f2b5141" |
#    @local
#    Examples:
#      | endpoint                                   |
#      | http://localhost:8080/offer-management-api |
    @sole
    Examples:
      | endpoint                                                            |
      | https://sole-services.dev.api.loyalty.com:3024/offer-management-api |


  Scenario Outline: Get Offer -> offerCategory1=CatOneTargeted
   Given All test offers created and published through <endpoint>
    When calling GetOffers with endpoint <endpoint> with input parameter offerCategory1=1ba636e3-799d-44c3-9c84-42a3df6430b0
    Then verifying response body paths
      | path                   | op | value                                  |
      | $.content[0].partnerId |    | "ce7edb67-52e6-4245-9d9c-9f7936bdb370" |
      | $.content[1].partnerId |    | "87ab4dd0-65b5-41a3-acd2-b97540dc1aff" |
      | $.content[2].partnerId |    | "3db0b3e0-4134-453e-8381-08e90f2b5141" |
#    @local
#    Examples:
#      | endpoint                                   |
#      | http://localhost:8080/offer-management-api |
    @sole
    Examples:
      | endpoint                                                            |
      | https://sole-services.dev.api.loyalty.com:3024/offer-management-api |


  Scenario Outline: Get Offer -> onlyEmptyCategory=true
    Given All test offers created and published through <endpoint>
    When calling GetOffers with endpoint <endpoint> with input parameter onlyEmptyCategory=true
    Then verifying response body paths
      | path                   | op | value                                  |
      | $.content[0].partnerId |    | "ae078d5c-8096-4283-a91c-5e5ce5dab7b4" |
      | $.content[1].partnerId |    | "8dd859c1-55d1-4157-8afc-7607297bdfeb" |
      | $.content[2].partnerId |    | "6ec2bd1e-b840-48b9-b43e-bc8664a62d8c" |
      | $.content[3].partnerId |    | "70ec7537-dcfe-486d-b83b-78b756e5e4cb" |
      | $.content[4].partnerId |    | "c1b1fa0b-0956-418d-be39-986e406dd780" |
#    @local
#    Examples:
#      | endpoint                                   |
#      | http://localhost:8080/offer-management-api |
    @sole
    Examples:
      | endpoint                                                            |
      | https://sole-services.dev.api.loyalty.com:3024/offer-management-api |


  Scenario Outline: Get Offer -> onlyEmptyCategory=false
    Given All test offers created and published through <endpoint>
    When calling GetOffers with endpoint <endpoint> with input parameter onlyEmptyCategory=false
    Then verifying response body paths
      | path                   | op | value                                  |
      | $.content[0].partnerId |    | "ae078d5c-8096-4283-a91c-5e5ce5dab7b4" |
      | $.content[1].partnerId |    | "8dd859c1-55d1-4157-8afc-7607297bdfeb" |
      | $.content[2].partnerId |    | "6ec2bd1e-b840-48b9-b43e-bc8664a62d8c" |
      | $.content[3].partnerId |    | "70ec7537-dcfe-486d-b83b-78b756e5e4cb" |
      | $.content[4].partnerId |    | "c1b1fa0b-0956-418d-be39-986e406dd780" |
      | $.content[5].partnerId |    | "ce7edb67-52e6-4245-9d9c-9f7936bdb370" |
      | $.content[6].partnerId |    | "87ab4dd0-65b5-41a3-acd2-b97540dc1aff" |
      | $.content[7].partnerId |    | "3db0b3e0-4134-453e-8381-08e90f2b5141" |
#    @local
#    Examples:
#      | endpoint                                   |
#      | http://localhost:8080/offer-management-api |
    @sole
    Examples:
      | endpoint                                                            |
      | https://sole-services.dev.api.loyalty.com:3024/offer-management-api |


  Scenario Outline: Get Offer -> hasIssuanceCode=false
    Given All test offers created and published through <endpoint>
    When calling GetOffers with endpoint <endpoint> with input parameter hasIssuanceCode=false
    Then verifying response body paths
      | path                   | op | value                                  |
      | $.content[0].partnerId |    | "ae078d5c-8096-4283-a91c-5e5ce5dab7b4" |
      | $.content[1].partnerId |    | "8dd859c1-55d1-4157-8afc-7607297bdfeb" |
      | $.content[2].partnerId |    | "6ec2bd1e-b840-48b9-b43e-bc8664a62d8c" |
      | $.content[3].partnerId |    | "c1b1fa0b-0956-418d-be39-986e406dd780" |
      | $.content[4].partnerId |    | "87ab4dd0-65b5-41a3-acd2-b97540dc1aff" |
      | $.content[5].partnerId |    | "3db0b3e0-4134-453e-8381-08e90f2b5141" |
#    @local
#    Examples:
#      | endpoint                                   |
#      | http://localhost:8080/offer-management-api |
    @sole
    Examples:
      | endpoint                                                            |
      | https://sole-services.dev.api.loyalty.com:3024/offer-management-api |


  Scenario Outline: Get Offer -> hasIssuanceCode=true
    Given All test offers created and published through <endpoint>
    When calling GetOffers with endpoint <endpoint> with input parameter hasIssuanceCode=true
    Then verifying response body paths
      | path                   | op | value                                  |
      | $.content[0].partnerId |    | "70ec7537-dcfe-486d-b83b-78b756e5e4cb" |
      | $.content[1].partnerId |    | "ce7edb67-52e6-4245-9d9c-9f7936bdb370" |
#    @local
#    Examples:
#      | endpoint                                   |
#      | http://localhost:8080/offer-management-api |
    @sole
    Examples:
      | endpoint                                                            |
      | https://sole-services.dev.api.loyalty.com:3024/offer-management-api |


  Scenario Outline: Get Offer -> campaignCode=23451
    Given All test offers created and published through <endpoint>
    When calling GetOffers with endpoint <endpoint> with input parameter campaignCode=23451
    Then verifying response body paths
      | path                   | op | value                                  |
      | $.content[0].partnerId |    | "8dd859c1-55d1-4157-8afc-7607297bdfeb" |
#    @local
#    Examples:
#      | endpoint                                   |
#      | http://localhost:8080/offer-management-api |
    @sole
    Examples:
      | endpoint                                                            |
      | https://sole-services.dev.api.loyalty.com:3024/offer-management-api |


  Scenario Outline: Get Offer -> campaignCode=23451&campaignCode=adidas
    Given All test offers created and published through <endpoint>
    When calling GetOffers with endpoint <endpoint> with input parameter campaignCode=23451&campaignCode=adidas
    Then verifying response body paths
      | path                   | op | value                                  |
      | $.content[0].partnerId |    | "ae078d5c-8096-4283-a91c-5e5ce5dab7b4" |
      | $.content[1].partnerId |    | "8dd859c1-55d1-4157-8afc-7607297bdfeb" |
#    @local
#    Examples:
#      | endpoint                                   |
#      | http://localhost:8080/offer-management-api |
    @sole
    Examples:
      | endpoint                                                            |
      | https://sole-services.dev.api.loyalty.com:3024/offer-management-api |


  Scenario Outline: Get Offer -> mechanismType=couponCode
    Given All test offers created and published through <endpoint>
    When calling GetOffers with endpoint <endpoint> with input parameter mechanismType=couponCode
    Then verifying response body paths
      | path                   | op | value                                  |
      | $.content[0].partnerId |    | "ce7edb67-52e6-4245-9d9c-9f7936bdb370" |
#    @local
#    Examples:
#      | endpoint                                   |
#      | http://localhost:8080/offer-management-api |
    @sole
    Examples:
      | endpoint                                                            |
      | https://sole-services.dev.api.loyalty.com:3024/offer-management-api |


  Scenario Outline: Get Offer -> mechanismType=couponCode&mechanismType=plu
    Given All test offers created and published through <endpoint>
    When calling GetOffers with endpoint <endpoint> with input parameter mechanismType=couponCode&mechanismType=plu
    Then verifying response body paths
      | path                   | op | value                                  |
      | $.content[0].partnerId |    | "70ec7537-dcfe-486d-b83b-78b756e5e4cb" |
      | $.content[1].partnerId |    | "ce7edb67-52e6-4245-9d9c-9f7936bdb370" |
#    @local
#    Examples:
#      | endpoint                                   |
#      | http://localhost:8080/offer-management-api |
    @sole
    Examples:
      | endpoint                                                            |
      | https://sole-services.dev.api.loyalty.com:3024/offer-management-api |


  Scenario Outline: Get Offer -> qualifier=fuel
    Given All test offers created and published through <endpoint>
    When calling GetOffers with endpoint <endpoint> with input parameter qualifier=fuel
    Then verifying response body paths
      | path                                     | op | value                                  |
      | $.content[0].partnerId                   |    | "3db0b3e0-4134-453e-8381-08e90f2b5141" |
      | $.content[0].qualifier                   |    | fuel                                   |
      | $.content[0].tiers[0].qualifierFrequency |    | 1                                      |
#    @local
#    Examples:
#      | endpoint                                   |
#      | http://localhost:8080/offer-management-api |
    @sole
    Examples:
      | endpoint                                                            |
      | https://sole-services.dev.api.loyalty.com:3024/offer-management-api |

# Needs refactor to work in sole, investigate how to get an offer ID
  Scenario Outline: Get Offer by Id 224b76ae-791c-4f1a-8eec-6633f9e22332
    Given All test offers created and published through <endpoint>
    When calling GetOffers with endpoint <endpoint> and offerId 224b76ae-791c-4f1a-8eec-6633f9e22332
    Then verifying response body paths
      | path                          | op | value                                  |
      | $.partnerId                   |    | "3db0b3e0-4134-453e-8381-08e90f2b5141" |
      | $.qualifier                   |    | fuel                                   |
      | $.tiers[0].qualifierFrequency |    | 1                                      |
    @local
    Examples:
      | endpoint                                   |
      | http://localhost:8080/offer-management-api |
#    @sole
#    Examples:
#      | endpoint                                                            |
#      | https://sole-services.dev.api.loyalty.com:3024/offer-management-api |


  Scenario Outline: Create offer with qualifier Fuel and qualifierFrequency 1 and Get Offer by Id
    When calling createOffer with qualifier Fuel and qualifierFrequency 1 <endpoint>
      | path                          | value                       |
      | $.qualifier                   | fuel                        |
      | $.tiers[0].qualifierFrequency | 1                           |
      | @.displayDate                 | shiftDisplayDateHours(-240) |
      | @.startDate                   | shiftStartDateHours(-200)   |
      | @.endDate                     | shiftEndDateHours(24)       |
    Then verifying status code is 200
    And calling GetOffers with endpoint <endpoint> and Offer Id appended
    And verifying response body paths
      | path                                     | op | value                                  |
      | $.content[0].partnerId                   |    | "3db0b3e0-4134-453e-8381-08e90f2b5141" |
      | $.content[0].qualifier                   |    | fuel                                   |
      | $.content[0].tiers[0].qualifierFrequency |    | 1                                      |
#    @local
#    Examples:
#      | endpoint                                   |
#      | http://localhost:8080/offer-management-api |
    @sole
    Examples:
      | endpoint                                                            |
      | https://sole-services.dev.api.loyalty.com:3024/offer-management-api |

# Needs refactor to work in sole, probably deprecated
  Scenario Outline: Get Offer -> offerCategory2=279ecbd8-b81b-4d46-9726-8fdd4dee8af8
    Given All test offers created and published through <endpoint>
    When calling GetOffers with endpoint <endpoint> with input parameter offerCategory2=279ecbd8-b81b-4d46-9726-8fdd4dee8af8
    Then verifying response body paths
      | path                   | op | value                                  |
      | $.content[0].partnerId |    | "87ab4dd0-65b5-41a3-acd2-b97540dc1aff" |
    @local
    Examples:
      | endpoint                                   |
      | http://localhost:8080/offer-management-api |
#    @sole
#    Examples:
#      | endpoint                                                            |
#      | https://sole-services.dev.api.loyalty.com:3024/offer-management-api |

# Needs refactor to work in sole, probably deprecated
  Scenario Outline: Get Offer -> offerCategory3=9c64462c-bb2f-4df9-ad9d-02e01d87ce2b
     Given All test offers created and published through <endpoint>
     When calling GetOffers with endpoint <endpoint> with input parameter offerCategory3=9c64462c-bb2f-4df9-ad9d-02e01d87ce2b
     Then verifying response body paths
      | path                   | op | value                                  |
      | $.content[0].partnerId |    | "87ab4dd0-65b5-41a3-acd2-b97540dc1aff" |
    @local
    Examples:
      | endpoint                                   |
      | http://localhost:8080/offer-management-api |
#    @sole
#    Examples:
#      | endpoint                                                            |
#      | https://sole-services.dev.api.loyalty.com:3024/offer-management-api |

# Needs refactor to work in sole
  Scenario Outline: Get Offer -> dateContext=expired
     Given calling GetOffers with endpoint <endpoint> with input parameter dateContext=expired
     Then verifying response body paths
      | path                   | op | value                                  |
      | $.content[0].partnerId |    | "afa8539d-583f-4a60-8b23-73e6b6b6319d" |
    @local
    Examples:
      | endpoint                                   |
      | http://localhost:8080/offer-management-api |
#    @sole
#    Examples:
#      | endpoint                                                            |
#      | https://sole-services.dev.api.loyalty.com:3024/offer-management-api |