Feature: Create CardLinked Offer Testcases

  Scenario Outline: Create offer with Program Type = CardLinkedOffers with NonBmoMastercard, BmoMastercard, BmoDebit.
    When calling createOffer with file name CardLinked_Offer and endpoint <endpoint>
      | path          | value                                                   |
      | @.displayDate | shiftDisplayDateHours(24)                               |
      | @.startDate   | shiftStartDateHours(24)                                 |
      | @.endDate     | shiftEndDateHours(48)                                   |
      | @.cardType    | cardTypeList("NonBmoMastercard,BmoMastercard,BmoDebit") |
    Then verifying status code is 200
    And verifying response body paths
      | path        | op | value                                   |
      | $.partnerId |    | "ffdfc361-38c9-44e6-bc9c-afb097a3b76d"  |
      | $.cardType  |    | NonBmoMastercard,BmoMastercard,BmoDebit |
    Then calling Publish Offer with endpoint <endpoint>
    Then verifying status code is 204
#    @local
#    Examples:
#      | endpoint                                   |
#      | http://localhost:8080/offer-management-api |

    @sole
    Examples:
      | endpoint                                                            |
      | https://sole-services.dev.api.loyalty.com:3024/offer-management-api |

  Scenario Outline: Create offer with Program Type = CardLinkedOffers with BmoDebit.
    When calling createOffer with file name CardLinked_Offer and endpoint <endpoint>
      | path          | value                     |
      | @.displayDate | shiftDisplayDateHours(24) |
      | @.startDate   | shiftStartDateHours(24)   |
      | @.endDate     | shiftEndDateHours(48)     |
      | @.cardType    | cardTypeList("BmoDebit")  |
    Then verifying status code is 200
    And verifying response body paths
      | path        | op | value                                  |
      | $.partnerId |    | "ffdfc361-38c9-44e6-bc9c-afb097a3b76d" |
      | $.cardType  |    | BmoDebit                               |
    Then calling Publish Offer with endpoint <endpoint>
    Then verifying status code is 204
#    @local
#    Examples:
#      | endpoint                                   |
#      | http://localhost:8080/offer-management-api |

    @sole
    Examples:
      | endpoint                                                            |
      | https://sole-services.dev.api.loyalty.com:3024/offer-management-api |

  Scenario Outline: Create offer with Program Type = CardLinkedOffers with BmoMastercard, BmoDebit.
    When calling createOffer with file name CardLinked_Offer and endpoint <endpoint>
      | path          | value                                  |
      | @.displayDate | shiftDisplayDateHours(24)              |
      | @.startDate   | shiftStartDateHours(24)                |
      | @.endDate     | shiftEndDateHours(48)                  |
      | @.cardType    | cardTypeList("BmoMastercard,BmoDebit") |
    Then verifying status code is 400
    And verifying response body paths
      | path      | op | value                                                                                                                                                 |
      | $.error   |    | "Operation not possible"                                                                                                                              |
      | $.status  |    |                                                                                                                                                   400 |
      | $.message |    | "Invalid cardType combination, Only allowed: (Non-BMO Mastercard + BMO Mastercard + BMO Debit), (Non-BMO Mastercard + BMO Mastercard), (BMO Debit))." |
#    @local
#    Examples:
#      | endpoint                                   |
#      | http://localhost:8080/offer-management-api |

    @sole
    Examples:
      | endpoint                                                            |
      | https://sole-services.dev.api.loyalty.com:3024/offer-management-api |

  Scenario Outline: Create offer with Program Type = CardLinkedOffers with NonBmoMastercard, BmoDebit.
    When calling createOffer with file name CardLinked_Offer and endpoint <endpoint>
      | path          | value                                     |
      | @.displayDate | shiftDisplayDateHours(24)                 |
      | @.startDate   | shiftStartDateHours(24)                   |
      | @.endDate     | shiftEndDateHours(48)                     |
      | @.cardType    | cardTypeList("NonBmoMastercard,BmoDebit") |
    Then verifying status code is 400
    And verifying response body paths
      | path      | op | value                                                                                                                                                 |
      | $.error   |    | "Operation not possible"                                                                                                                              |
      | $.status  |    |                                                                                                                                                   400 |
      | $.message |    | "Invalid cardType combination, Only allowed: (Non-BMO Mastercard + BMO Mastercard + BMO Debit), (Non-BMO Mastercard + BMO Mastercard), (BMO Debit))." |
#    @local
#    Examples:
#      | endpoint                                   |
#      | http://localhost:8080/offer-management-api |

    @sole
    Examples:
      | endpoint                                                            |
      | https://sole-services.dev.api.loyalty.com:3024/offer-management-api |

  Scenario Outline: Create offer with Program Type = CardLinkedOffers with NonBmoMastercard.
    When calling createOffer with file name CardLinked_Offer and endpoint <endpoint>
      | path          | value                            |
      | @.displayDate | shiftDisplayDateHours(24)        |
      | @.startDate   | shiftStartDateHours(24)          |
      | @.endDate     | shiftEndDateHours(48)            |
      | @.cardType    | cardTypeList("NonBmoMastercard") |
    Then verifying status code is 400
    And verifying response body paths
      | path      | op | value                                                                                                                                                 |
      | $.error   |    | "Operation not possible"                                                                                                                              |
      | $.status  |    |                                                                                                                                                   400 |
      | $.message |    | "Invalid cardType combination, Only allowed: (Non-BMO Mastercard + BMO Mastercard + BMO Debit), (Non-BMO Mastercard + BMO Mastercard), (BMO Debit))." |
#    @local
#    Examples:
#      | endpoint                                   |
#      | http://localhost:8080/offer-management-api |

    @sole
    Examples:
      | endpoint                                                            |
      | https://sole-services.dev.api.loyalty.com:3024/offer-management-api |

  Scenario Outline: Create offer with Program Type = CardLinkedOffers with BmoMastercard.
    When calling createOffer with file name CardLinked_Offer and endpoint <endpoint>
      | path          | value                         |
      | @.displayDate | shiftDisplayDateHours(24)     |
      | @.startDate   | shiftStartDateHours(24)       |
      | @.endDate     | shiftEndDateHours(48)         |
      | @.cardType    | cardTypeList("BmoMastercard") |
    Then verifying status code is 400
    And verifying response body paths
      | path      | op | value                                                                                                                                                 |
      | $.error   |    | "Operation not possible"                                                                                                                              |
      | $.status  |    |                                                                                                                                                   400 |
      | $.message |    | "Invalid cardType combination, Only allowed: (Non-BMO Mastercard + BMO Mastercard + BMO Debit), (Non-BMO Mastercard + BMO Mastercard), (BMO Debit))." |
#    @local
#    Examples:
#      | endpoint                                   |
#      | http://localhost:8080/offer-management-api |

    @sole
    Examples:
      | endpoint                                                            |
      | https://sole-services.dev.api.loyalty.com:3024/offer-management-api |

  Scenario Outline: Create offer with Program Type different to CardLinkedOffers (traditionalcore).
    When calling createOffer with file name Sample_Offer_1 and endpoint <endpoint>
      | path          | value                     |
      | @.displayDate | shiftDisplayDateHours(24) |
      | @.startDate   | shiftStartDateHours(24)   |
      | @.endDate     | shiftEndDateHours(48)     |
    Then verifying status code is 200
    And verifying response body paths
      | path          | op | value                                  |
      | $.partnerId   |    | "87ab4dd0-65b5-41a3-acd2-b97540dc1aff" |
      | $.programType |    | "traditionalcore"                      |
      | $.cardType    |    |                                        |
    Then calling Publish Offer with endpoint <endpoint>
    Then verifying status code is 204
#    @local
#    Examples:
#      | endpoint                                   |
#      | http://localhost:8080/offer-management-api |

    @sole
    Examples:
      | endpoint                                                            |
      | https://sole-services.dev.api.loyalty.com:3024/offer-management-api |

  Scenario Outline: Create offer with Program Type different to CardLinkedOffers (airmilesshops).
    When calling createOffer with file name Sample_Offer_9 and endpoint <endpoint>
      | path          | value                     |
      | @.displayDate | shiftDisplayDateHours(24) |
      | @.startDate   | shiftStartDateHours(24)   |
      | @.endDate     | shiftEndDateHours(48)     |
    Then verifying status code is 200
    And verifying response body paths
      | path          | op | value                                  |
      | $.partnerId   |    | "87ab4dd0-65b5-41a3-acd2-b97540dc1aff" |
      | $.programType |    | "airmilesshops"                        |
      | $.cardType    |    |                                        |
    Then calling Publish Offer with endpoint <endpoint>
    Then verifying status code is 204
#    @local
#    Examples:
#      | endpoint                                   |
#      | http://localhost:8080/offer-management-api |

    @sole
    Examples:
      | endpoint                                                            |
      | https://sole-services.dev.api.loyalty.com:3024/offer-management-api |

  Scenario Outline: Editing a Card Linked Offer in Published state.
    When calling createOffer with file name CardLinked_Offer and endpoint <endpoint>
      | path          | value                     |
      | @.displayDate | shiftDisplayDateHours(24) |
      | @.startDate   | shiftStartDateHours(24)   |
      | @.endDate     | shiftEndDateHours(48)     |
      | @.cardType    | cardTypeList("BmoDebit")  |
    Then verifying status code is 200
    Then calling Publish Offer with endpoint <endpoint>
    Then verifying status code is 204
    And verifying response body paths
      | path        | value                                  |
      | $.partnerId | "ffdfc361-38c9-44e6-bc9c-afb097a3b76d" |
      | $.cardType  | BmoDebit                               |
    When calling putOffer with offerId with endpoint <endpoint>
      | path       | value                                                   |
      | @.cardType | cardTypeList("NonBmoMastercard,BmoMastercard,BmoDebit") |
    Then verifying status code is 200
    And verifying response body paths
      | path        | value                                   |
      | $.partnerId | "ffdfc361-38c9-44e6-bc9c-afb097a3b76d"  |
      | $.cardType  | NonBmoMastercard,BmoMastercard,BmoDebit |
#    @local
#    Examples:
#      | endpoint                                   |
#      | http://localhost:8080/offer-management-api |

    @sole
    Examples:
      | endpoint                                                            |
      | https://sole-services.dev.api.loyalty.com:3024/offer-management-api |

  Scenario Outline: Editing a Card Linked Offer in Draft state.
    When calling createOffer with file name CardLinked_Offer and endpoint <endpoint>
      | path          | value                     |
      | @.displayDate | shiftDisplayDateHours(24) |
      | @.startDate   | shiftStartDateHours(24)   |
      | @.endDate     | shiftEndDateHours(48)     |
      | @.cardType    | cardTypeList("BmoDebit")  |
    Then verifying status code is 200
    And verifying response body paths
      | path        | value                                  |
      | $.partnerId | "ffdfc361-38c9-44e6-bc9c-afb097a3b76d" |
      | $.cardType  | BmoDebit                               |
    When calling putOffer with offerId with endpoint <endpoint>
      | path       | value                                                   |
      | @.cardType | cardTypeList("NonBmoMastercard,BmoMastercard,BmoDebit") |
    Then verifying status code is 200
    And verifying response body paths
      | path        | value                                   |
      | $.partnerId | "ffdfc361-38c9-44e6-bc9c-afb097a3b76d"  |
      | $.cardType  | NonBmoMastercard,BmoMastercard,BmoDebit |
#    @local
#    Examples:
#      | endpoint                                   |
#      | http://localhost:8080/offer-management-api |

    @sole
    Examples:
      | endpoint                                                            |
      | https://sole-services.dev.api.loyalty.com:3024/offer-management-api |

  Scenario Outline: Create offer with Program Type different to CardLinkedOffers and try to send a carType (traditionalcore).
    When calling createOffer with file name Sample_Offer_1 and endpoint <endpoint>
      | path          | value                                          |
      | @.displayDate | shiftDisplayDateHours(24)                      |
      | @.startDate   | shiftStartDateHours(24)                        |
      | @.endDate     | shiftEndDateHours(48)                          |
      | @.cardType    | cardTypeList("NonBmoMastercard,BmoMastercard") |
    Then verifying status code is 400
    And verifying response body paths
      | path      | op | value                                                                        |
      | $.error   |    | "Operation not possible"                                                     |
      | $.status  |    |                                                                          400 |
      | $.message |    | "cardType should be null or empty when programType is not CardLinkedOffers." |
#    @local
#    Examples:
#      | endpoint                                   |
#      | http://localhost:8080/offer-management-api |

    @sole
    Examples:
      | endpoint                                                            |
      | https://sole-services.dev.api.loyalty.com:3024/offer-management-api |

  Scenario Outline: Editing a Offer - Change to Card Linked Offer (traditionalcore).
    When calling createOffer with file name Sample_Offer_1 and endpoint <endpoint>
      | path          | value                     |
      | @.displayDate | shiftDisplayDateHours(24) |
      | @.startDate   | shiftStartDateHours(24)   |
      | @.endDate     | shiftEndDateHours(48)     |
    Then verifying status code is 200
    And verifying response body paths
      | path          | op | value                                  |
      | $.partnerId   |    | "87ab4dd0-65b5-41a3-acd2-b97540dc1aff" |
      | $.programType |    | "traditionalcore"                      |
      | $.cardType    |    |                                        |
    When calling putOffer with offerId with endpoint <endpoint>
      | path          | value                    |
      | @.programType | "cardlinked"             |
      | @.cardType    | cardTypeList("BmoDebit") |
    Then verifying status code is 200
    And verifying response body paths
      | path          | op | value        |
      | $.programType |    | "cardlinked" |
      | $.cardType    |    | BmoDebit     |
    Then calling Publish Offer with endpoint <endpoint>
    Then verifying status code is 204
#    @local
#    Examples:
#      | endpoint                                   |
#      | http://localhost:8080/offer-management-api |

    @sole
    Examples:
      | endpoint                                                            |
      | https://sole-services.dev.api.loyalty.com:3024/offer-management-api |

  Scenario Outline: Editing a Card Linked Offer - Change to another Program Type (traditionalcore).
    When calling createOffer with file name CardLinked_Offer and endpoint <endpoint>
      | path          | value                                                   |
      | @.displayDate | shiftDisplayDateHours(24)                               |
      | @.startDate   | shiftStartDateHours(24)                                 |
      | @.endDate     | shiftEndDateHours(48)                                   |
      | @.cardType    | cardTypeList("NonBmoMastercard,BmoMastercard,BmoDebit") |
    Then verifying status code is 200
    And verifying response body paths
      | path        | op | value                                   |
      | $.partnerId |    | "ffdfc361-38c9-44e6-bc9c-afb097a3b76d"  |
      | $.cardType  |    | NonBmoMastercard,BmoMastercard,BmoDebit |
    When calling putOffer with offerId with endpoint <endpoint>
      | path          | value             |
      | @.programType | "traditionalcore" |
      | $.cardType    |                   |
    Then verifying status code is 200
    And verifying response body paths
      | path          | op | value             |
      | $.programType |    | "traditionalcore" |
      | $.cardType    |    |                   |
#    @local
#    Examples:
#      | endpoint                                   |
#      | http://localhost:8080/offer-management-api |

    @sole
    Examples:
      | endpoint                                                            |
      | https://sole-services.dev.api.loyalty.com:3024/offer-management-api |

  Scenario Outline: Create offer with Program Type different to CardLinkedOffers and try to send a carType (airmilesshops).
    When calling createOffer with file name Sample_Offer_9 and endpoint <endpoint>
      | path          | value                                          |
      | @.displayDate | shiftDisplayDateHours(24)                      |
      | @.startDate   | shiftStartDateHours(24)                        |
      | @.endDate     | shiftEndDateHours(48)                          |
      | @.cardType    | cardTypeList("NonBmoMastercard,BmoMastercard") |
    Then verifying status code is 400
    And verifying response body paths
      | path      | op | value                                                                        |
      | $.error   |    | "Operation not possible"                                                     |
      | $.status  |    |                                                                          400 |
      | $.message |    | "cardType should be null or empty when programType is not CardLinkedOffers." |
#    @local
#    Examples:
#      | endpoint                                   |
#      | http://localhost:8080/offer-management-api |

    @sole
    Examples:
      | endpoint                                                            |
      | https://sole-services.dev.api.loyalty.com:3024/offer-management-api |

  Scenario Outline: Editing a Card Linked Offer - Change to another Program Type (airmilesshops).
    When calling createOffer with file name CardLinked_Offer and endpoint <endpoint>
      | path          | value                                                   |
      | @.displayDate | shiftDisplayDateHours(24)                               |
      | @.startDate   | shiftStartDateHours(24)                                 |
      | @.endDate     | shiftEndDateHours(48)                                   |
      | @.cardType    | cardTypeList("NonBmoMastercard,BmoMastercard,BmoDebit") |
    Then verifying status code is 200
    And verifying response body paths
      | path          | op | value                                   |
      | $.programType |    | "cardlinked"                            |
      | $.cardType    |    | NonBmoMastercard,BmoMastercard,BmoDebit |
    When calling putOffer with offerId with endpoint <endpoint>
      | path          | value           |
      | @.programType | "airmilesshops" |
      | $.cardType    |                 |
    Then verifying status code is 200
    And verifying response body paths
      | path          | op | value           |
      | $.programType |    | "airmilesshops" |
      | $.cardType    |    |                 |
#    @local
#    Examples:
#      | endpoint                                   |
#      | http://localhost:8080/offer-management-api |

    @sole
    Examples:
      | endpoint                                                            |
      | https://sole-services.dev.api.loyalty.com:3024/offer-management-api |

  Scenario Outline: Editing a Offer - Change to Card Linked Offer (airmilesshops).
    When calling createOffer with file name Sample_Offer_9 and endpoint <endpoint>
      | path          | value                     |
      | @.displayDate | shiftDisplayDateHours(24) |
      | @.startDate   | shiftStartDateHours(24)   |
      | @.endDate     | shiftEndDateHours(48)     |
    Then verifying status code is 200
    And verifying response body paths
      | path          | op | value                                  |
      | $.partnerId   |    | "87ab4dd0-65b5-41a3-acd2-b97540dc1aff" |
      | $.programType |    | "airmilesshops"                        |
      | $.cardType    |    |                                        |
    When calling putOffer with offerId with endpoint <endpoint>
      | path          | value                                                   |
      | @.programType | "cardlinked"                                            |
      | @.cardType    | cardTypeList("NonBmoMastercard,BmoMastercard,BmoDebit") |
    Then verifying status code is 200
    And verifying response body paths
      | path          | op | value                                   |
      | $.programType |    | "cardlinked"                            |
      | $.cardType    |    | NonBmoMastercard,BmoMastercard,BmoDebit |
    Then calling Publish Offer with endpoint <endpoint>
    Then verifying status code is 204
#    @local
#    Examples:
#      | endpoint                                   |
#      | http://localhost:8080/offer-management-api |

    @sole
    Examples:
      | endpoint                                                            |
      | https://sole-services.dev.api.loyalty.com:3024/offer-management-api |
