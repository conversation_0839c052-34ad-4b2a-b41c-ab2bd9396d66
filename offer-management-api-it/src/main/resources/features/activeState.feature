Feature: Disable and Enable Offer Testcases

  Scenario Outline: Create Offer and Disable Offer by Id
    When calling createOffer with file name Sample_Offer_7 and endpoint <endpoint>
      | path          | value                       |
      | @.displayDate | shiftDisplayDateHours(-240) |
      | @.startDate   | shiftStartDateHours(-200)   |
      | @.endDate     | shiftEndDateHours(24)       |
    Then verifying status code is 200
    And calling DisableOffer with endpoint <endpoint> and OfferId included
    Then verifying status code is 500
#    @local
#    Examples:
#      | endpoint                                   |
#      | http://localhost:8080/offer-management-api |
    @sole
    Examples:
      | endpoint                                                            |
      | https://sole-services.dev.api.loyalty.com:3024/offer-management-api |


  Scenario Outline: Create, Publish Offer, Disable to Get Disabled Offer by Id
    When calling createOffer with file name Sample_Offer_7 and endpoint <endpoint>
      | path          | value                       |
      | @.displayDate | shiftDisplayDateHours(-240) |
      | @.startDate   | shiftStartDateHours(-200)   |
      | @.endDate     | shiftEndDateHours(24)       |
    Then verifying status code is 200
    Then calling Publish Offer with endpoint <endpoint>
    Then verifying status code is 204
    Then calling DisableOffer with endpoint <endpoint> and OfferId included
    Then verifying status code is 200
    When calling GetOffers with endpoint <endpoint> with input parameter active=false
    And verifying response body paths
      | path                   | op | value                                  |
      | $.content[0].partnerId |    | "afa8539d-583f-4a60-8b23-73e6b6b6319d" |
#    @local
#    Examples:
#      | endpoint                                   |
#      | http://localhost:8080/offer-management-api |
    @sole
    Examples:
      | endpoint                                                            |
      | https://sole-services.dev.api.loyalty.com:3024/offer-management-api |


  Scenario Outline: Create, Publish, Disable and Enable to Get Enabled Offers by Id
    When calling createOffer with file name Sample_Offer_7 and endpoint <endpoint>
      | path          | value                       |
      | @.displayDate | shiftDisplayDateHours(-240) |
      | @.startDate   | shiftStartDateHours(-200)   |
      | @.endDate     | shiftEndDateHours(24)       |
    Then verifying status code is 200
    Then calling Publish Offer with endpoint <endpoint>
    Then verifying status code is 204
    Then calling DisableOffer with endpoint <endpoint> and OfferId included
    Then verifying status code is 200
    And calling EnableOffer with endpoint <endpoint> and OfferId included
    Then verifying status code is 200
    When calling GetOffers with endpoint <endpoint> with input parameter active=true
    And verifying response body paths
      | path                   | op | value                                  |
      | $.content[0].partnerId |    | "afa8539d-583f-4a60-8b23-73e6b6b6319d" |
#    @local
#    Examples:
#      | endpoint                                   |
#      | http://localhost:8080/offer-management-api |
    @sole
    Examples:
      | endpoint                                                            |
      | https://sole-services.dev.api.loyalty.com:3024/offer-management-api |


  Scenario Outline: Create Offer and Enable Offer by Id
    When calling createOffer with file name Sample_Offer_7 and endpoint <endpoint>
      | path          | value                       |
      | @.displayDate | shiftDisplayDateHours(-240) |
      | @.startDate   | shiftStartDateHours(-200)   |
      | @.endDate     | shiftEndDateHours(24)       |
    Then verifying status code is 200
    And calling EnableOffer with endpoint <endpoint> and OfferId included
    Then verifying status code is 500
#    @local
#    Examples:
#      | endpoint                                   |
#      | http://localhost:8080/offer-management-api |
    @sole
    Examples:
      | endpoint                                                            |
      | https://sole-services.dev.api.loyalty.com:3024/offer-management-api |