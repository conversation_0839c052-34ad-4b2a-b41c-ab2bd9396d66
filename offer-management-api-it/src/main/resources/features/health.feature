Feature: Health Controller

  Scenario Outline: Call Health
    When calling health endpoint <endpoint>
    Then verifying status code is 200

    @local
    Examples:
      | endpoint                                   |
      | http://localhost:8080/offer-management-api |

    @sole
    Examples:
      | endpoint                                                            |
      | https://sole-services.dev.api.loyalty.com:3024/offer-management-api |

  @local
  Scenario: Health Performance Benchmarking
    When executing JMeter plan
      | scheme                | http                         |
      | host                  | localhost                    |
      | port                  | 8080                         |
      | path                  | /offer-management-api/health |
      | targetConcurrency     | 2                            |
      | rampUpTime            | 0                            |
      | rampUpStepCount       | 0                            |
      | holdTargetRateTime    | 2                            |
      | assertionResponseCode | 200                          |
    Then verify that number of requests raised higher than 300
    Then verify that run average response time less than 10 ms
    Then verify that error count is not greater than 0

  @sole
  Scenario: Health Performance Benchmarking
    When executing JMeter plan
      | scheme                | https                             |
      | host                  | sole-services.dev.api.loyalty.com |
      | port                  | 3024                              |
      | path                  | /offer-management-api/health      |
      | targetConcurrency     | 2                                 |
      | rampUpTime            | 0                                 |
      | rampUpStepCount       | 0                                 |
      | holdTargetRateTime    | 2                                 |
      | assertionResponseCode | 200                               |
    Then verify that number of requests raised higher than 10
    Then verify that run average response time less than 50 ms
    Then verify that error count is not greater than 0
