Feature: Get Offers Count Testcases

  Scenario Outline: Get Offers Count -> campaignCode=APPLE
    Given running WireMock on host <wireMockHost> and port <wireMockPort>
    And removing all WireMock mappings
    And stabbing AirmilesOfferApiV2Mock for stub <airmilesOfferApiV2MockStub>
    And stabbing ContentGenerationServiceMock for stub <contentGenerationServiceMockStub>
    And stabbing OfferFacetsApiMock for stub ok
    And stabbing PromotionApiMock for stub ok
    When calling GetOffersCounts with endpoint <endpoint> with input parameter campaignCode=apple
    And store total count from response
    When calling createOffer with file name Sample_Offer_3 and endpoint <endpoint>
      | path           | value                     |
      | @.campaignCode | "LCBO5"                   |
      | @.displayDate  | shiftDisplayDateHours(36) |
      | @.startDate    | shiftStartDateHours(48)   |
      | @.endDate      | shiftEndDateHours(56)     |
    Then verifying status code is 200
    When calling createOffer with file name Sample_Offer_6 and endpoint <endpoint>
      | path           | value                     |
      | @.campaignCode | "APPLE"                   |
      | @.displayDate  | shiftDisplayDateHours(36) |
      | @.startDate    | shiftStartDateHours(48)   |
      | @.endDate      | shiftEndDateHours(56)     |
    Then verifying status code is 200
    When calling createOffer with file name Sample_Offer_8 and endpoint <endpoint>
      | path           | value                     |
      | @.campaignCode | "88888"                   |
      | @.displayDate  | shiftDisplayDateHours(36) |
      | @.startDate    | shiftStartDateHours(48)   |
      | @.endDate      | shiftEndDateHours(56)     |
    Then verifying status code is 200
    When calling GetOffersCounts with endpoint <endpoint> with input parameter campaignCode=apple
    Then verify total count increase
    When calling GetOffers with endpoint <endpoint> with input parameter campaignCode=lcbo5&campaignCode=apple&campaignCode=88888
    Then verifying status code is 200
    And calling DeleteOffers with endpoint <endpoint> and all OfferIds included
    Then verifying status code is 200
    @local
    Examples:
      | endpoint                                   | wireMockHost | wireMockPort | airmilesOfferApiV2MockStub | contentGenerationServiceMockStub |
      | http://localhost:8080/offer-management-api | localhost    | 9090         | ok                         | ok                               |
    @sole
    Examples:
      | endpoint                                                            | wireMockHost | wireMockPort | airmilesOfferApiV2MockStub | contentGenerationServiceMockStub |
      | https://sole-services.dev.api.loyalty.com:3024/offer-management-api | localhost    | 9090         | ok                         | ok                               |

  Scenario Outline: Get Offers Count -> campaignCode=lcbo5&campaignCode=apple
    When running WireMock on host <wireMockHost> and port <wireMockPort>
    And removing all WireMock mappings
    And stabbing AirmilesOfferApiV2Mock for stub <airmilesOfferApiV2MockStub>
    And stabbing ContentGenerationServiceMock for stub <contentGenerationServiceMockStub>
    And stabbing OfferFacetsApiMock for stub ok
    And stabbing PromotionApiMock for stub ok
    When calling GetOffersCounts with endpoint <endpoint> with input parameter campaignCode=lcbo5&campaignCode=apple
    And store total count from response
    When calling createOffer with file name Sample_Offer_3 and endpoint <endpoint>
      | path           | value                     |
      | @.campaignCode | "LCBO5"                   |
      | @.displayDate  | shiftDisplayDateHours(36) |
      | @.startDate    | shiftStartDateHours(48)   |
      | @.endDate      | shiftEndDateHours(56)     |
    Then verifying status code is 200
    When calling createOffer with file name Sample_Offer_6 and endpoint <endpoint>
      | path           | value                     |
      | @.campaignCode | "APPLE"                   |
      | @.displayDate  | shiftDisplayDateHours(36) |
      | @.startDate    | shiftStartDateHours(48)   |
      | @.endDate      | shiftEndDateHours(56)     |
    Then verifying status code is 200
    When calling createOffer with file name Sample_Offer_8 and endpoint <endpoint>
      | path           | value                     |
      | @.campaignCode | "88888"                   |
      | @.displayDate  | shiftDisplayDateHours(36) |
      | @.startDate    | shiftStartDateHours(48)   |
      | @.endDate      | shiftEndDateHours(56)     |
    Then verifying status code is 200
    When calling GetOffersCounts with endpoint <endpoint> with input parameter campaignCode=lcbo5&campaignCode=apple
    Then verify total count increase
    When calling GetOffers with endpoint <endpoint> with input parameter campaignCode=lcbo5&campaignCode=apple&campaignCode=88888
    Then verifying status code is 200
    And calling DeleteOffers with endpoint <endpoint> and all OfferIds included
    Then verifying status code is 200
    @local
    Examples:
      | endpoint                                   | wireMockHost | wireMockPort | airmilesOfferApiV2MockStub | contentGenerationServiceMockStub |
      | http://localhost:8080/offer-management-api | localhost    | 9090         | ok                         | ok                               |
    @sole
    Examples:
      | endpoint                                                            | wireMockHost | wireMockPort | airmilesOfferApiV2MockStub | contentGenerationServiceMockStub |
      | https://sole-services.dev.api.loyalty.com:3024/offer-management-api | localhost    | 9090         | ok                         | ok                               |
