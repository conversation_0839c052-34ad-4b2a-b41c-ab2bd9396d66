Feature: Creating and Publishing Offer Testcases for multisku

  Scenario Outline: Create offer with valid payload for multisku
    When calling createOffer with file name <PERSON><PERSON>_multi and endpoint <endpoint>
      | path          | value                       |
      | @.displayDate | shiftDisplayDateHours(-240) |
      | @.startDate   | shiftStartDateHours(-200)   |
      | @.endDate     | shiftEndDateHours(24)       |
    Then verifying status code is 200
    And verifying response body paths
      | path                           | op    | value                                         |
      | $.partnerId                    |       | "3db0b3e0-4134-453e-8381-08e90f2b5141"        |
      | $.offerType                    |       | "spend"                                       |
      | $.tiers[0].qualifierFrequency  |       | 1                                             |
      | $.tiers[0].content[0].skus[0]  |       | "qfgh1"                                       |
    Then calling Publish Offer with endpoint <endpoint>
    Then verifying status code is 204

    @local
    Examples:
      | endpoint                                   |
      | http://localhost:8080/offer-management-api |
    @sole
    Examples:
      | endpoint                                                            |
      | https://sole-services.dev.api.loyalty.com:3024/offer-management-api |