Feature: Create Offer Testcases

  Scenario Outline: Create offer with valid payload
    When calling createOffer with file name Rexall_spend and endpoint <endpoint>
      | path          | value                       |
      | @.displayDate | shiftDisplayDateHours(-240) |
      | @.startDate   | shiftStartDateHours(-200)   |
      | @.endDate     | shiftEndDateHours(24)       |
    Then verifying status code is 200
    @local
    Examples:
      | endpoint                                   |
      | http://localhost:8080/offer-management-api |
    @sole
    Examples:
      | endpoint                                                            |
      | https://sole-services.dev.api.loyalty.com:3024/offer-management-api |

  Scenario Outline: Create bulk offers
    When calling createBulkOffer with file name Bulk_Offers and endpoint <endpoint>
      | path                | value                 |
      | $.offers[*].endDate | shiftEndDateHours(24) |
    Then verifying status code is 200
    @local
    Examples:
      | endpoint                                   |
      | http://localhost:8080/offer-management-api |
    @sole
    Examples:
      | endpoint                                                            |
      | https://sole-services.dev.api.loyalty.com:3024/offer-management-api |

  Scenario Outline: Try to create offer with misspelled offerType
    When calling createOffer with file name Rexall_spend and endpoint <endpoint>
      | path        | value                 |
      | @.offerType | "spends"              |
      | @.endDate   | shiftEndDateHours(24) |
    Then verifying status code is 400
    And verifying response body paths
      | path     | op | value              |
      | $.error  |    | "Operation Failed" |
      | $.status |    | 400                |
    @local
    Examples:
      | endpoint                                   |
      | http://localhost:8080/offer-management-api |
    @sole
    Examples:
      | endpoint                                                            |
      | https://sole-services.dev.api.loyalty.com:3024/offer-management-api |

  Scenario Outline: Try to create offer with end date before start date
    When calling createOffer with file name Rexall_spend and endpoint <endpoint>
      | path          | value                     |
      | @.displayDate | shiftDisplayDateHours(24) |
      | @.startDate   | shiftStartDateHours(48)   |
      | @.endDate     | shiftEndDateHours(40)     |
    Then verifying status code is 400
    And verifying response body paths
      | path      | op | value                                   |
      | $.error   |    | "Operation not possible"                |
      | $.status  |    | 400                                     |
      | $.message |    | "End date cannot be before start date." |
    @local
    Examples:
      | endpoint                                   |
      | http://localhost:8080/offer-management-api |
    @sole
    Examples:
      | endpoint                                                            |
      | https://sole-services.dev.api.loyalty.com:3024/offer-management-api |

  Scenario Outline: Try to create offer with start date before display date
    When calling createOffer with file name Rexall_spend and endpoint <endpoint>
      | path          | value                     |
      | @.displayDate | shiftDisplayDateHours(36) |
      | @.startDate   | shiftStartDateHours(24)   |
      | @.endDate     | shiftEndDateHours(48)     |
    Then verifying status code is 400
    And verifying response body paths
      | path      | op | value                                       |
      | $.error   |    | "Operation not possible"                    |
      | $.status  |    | 400                                         |
      | $.message |    | "Start date cannot be before display date." |
    @local
    Examples:
      | endpoint                                   |
      | http://localhost:8080/offer-management-api |
    @sole
    Examples:
      | endpoint                                                            |
      | https://sole-services.dev.api.loyalty.com:3024/offer-management-api |

  Scenario Outline: Try to create offer with end date before display date
    When calling createOffer with file name Rexall_spend and endpoint <endpoint>
      | path          | value                     |
      | @.displayDate | shiftDisplayDateHours(56) |
      | @.startDate   | shiftStartDateHours(56)   |
      | @.endDate     | shiftEndDateHours(48)     |
    Then verifying status code is 400
    And verifying response body paths
      | path      | op | value                                     |
      | $.error   |    | "Operation not possible"                  |
      | $.status  |    | 400                                       |
      | $.message |    | "End date cannot be before display date." |
    @local
    Examples:
      | endpoint                                   |
      | http://localhost:8080/offer-management-api |
    @sole
    Examples:
      | endpoint                                                            |
      | https://sole-services.dev.api.loyalty.com:3024/offer-management-api |

  Scenario Outline: Try to create offer with end date before current date
    When calling createOffer with file name Rexall_spend and endpoint <endpoint>
      | path          | value                      |
      | @.displayDate | shiftDisplayDateHours(-48) |
      | @.startDate   | shiftStartDateHours(-24)   |
      | @.endDate     | shiftEndDateHours(-12)     |
    Then verifying status code is 400
    And verifying response body paths
      | path      | op | value                                     |
      | $.error   |    | "Operation not possible"                  |
      | $.status  |    | 400                                       |
      | $.message |    | "End date cannot be before current date." |
    @local
    Examples:
      | endpoint                                   |
      | http://localhost:8080/offer-management-api |
    @sole
    Examples:
      | endpoint                                                            |
      | https://sole-services.dev.api.loyalty.com:3024/offer-management-api |

  Scenario Outline: Try to create bulk offers with end date before start date
    When calling createBulkOffer with file name Bulk_Offers and endpoint <endpoint>
      | path                    | value                     |
      | $.offers[0].displayDate | shiftDisplayDateHours(48) |
      | $.offers[0].startDate   | shiftStartDateHours(56)   |
      | $.offers[0].endDate     | shiftEndDateHours(50)     |
    Then verifying status code is 400
    And verifying response body paths
      | path      | op | value                                   |
      | $.error   |    | "Operation not possible"                |
      | $.status  |    | 400                                     |
      | $.message |    | "End date cannot be before start date." |
    @local
    Examples:
      | endpoint                                   |
      | http://localhost:8080/offer-management-api |
    @sole
    Examples:
      | endpoint                                                            |
      | https://sole-services.dev.api.loyalty.com:3024/offer-management-api |

  Scenario Outline: Try to create bulk offers with start date before display date
    When calling createBulkOffer with file name Bulk_Offers and endpoint <endpoint>
      | path                    | value                     |
      | $.offers[0].displayDate | shiftDisplayDateHours(36) |
      | $.offers[0].startDate   | shiftStartDateHours(24)   |
      | $.offers[0].endDate     | shiftEndDateHours(48)     |
    Then verifying status code is 400
    And verifying response body paths
      | path      | op | value                                       |
      | $.error   |    | "Operation not possible"                    |
      | $.status  |    | 400                                         |
      | $.message |    | "Start date cannot be before display date." |
    @local
    Examples:
      | endpoint                                   |
      | http://localhost:8080/offer-management-api |
    @sole
    Examples:
      | endpoint                                                            |
      | https://sole-services.dev.api.loyalty.com:3024/offer-management-api |

  Scenario Outline: Try to create bulk offers with end date before display date
    When calling createBulkOffer with file name Bulk_Offers and endpoint <endpoint>
      | path                    | value                     |
      | $.offers[0].displayDate | shiftDisplayDateHours(56) |
      | $.offers[0].startDate   | shiftStartDateHours(56)   |
      | $.offers[0].endDate     | shiftEndDateHours(48)     |
    Then verifying status code is 400
    And verifying response body paths
      | path      | op | value                                     |
      | $.error   |    | "Operation not possible"                  |
      | $.status  |    | 400                                       |
      | $.message |    | "End date cannot be before display date." |
    @local
    Examples:
      | endpoint                                   |
      | http://localhost:8080/offer-management-api |
    @sole
    Examples:
      | endpoint                                                            |
      | https://sole-services.dev.api.loyalty.com:3024/offer-management-api |



  Scenario Outline: Create offer with qualifier Fuel and qualifierFrequency 1
    When calling createOffer with qualifier Fuel and qualifierFrequency 1 <endpoint>
      | path                           | value                       |
      | $.qualifier                    | fuel                        |
      | $.tiers[0].qualifierFrequency  | 1                           |
      | @.displayDate                  | shiftDisplayDateHours(-240) |
      | @.startDate                    | shiftStartDateHours(-200)   |
      | @.endDate                      | shiftEndDateHours(24)       |
    Then verifying status code is 200
    And verifying response body paths
      | path                          | op       | value              |
      | $.qualifier                   |          | fuel               |
      | $.tiers[0].qualifierFrequency |          | 1                  |
    @local
    Examples:
      | endpoint                                   |
      | http://localhost:8080/offer-management-api |
    @sole
    Examples:
      | endpoint                                                            |
      | https://sole-services.dev.api.loyalty.com:3024/offer-management-api |
