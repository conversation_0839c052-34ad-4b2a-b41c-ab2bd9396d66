Feature: Update Offer Testcases

  Scenario Outline: Update offer with valid payload
    When calling createOffer with file name Rexall_spend and endpoint <endpoint>
      | path          | value                     |
      | @.displayDate | shiftDisplayDateHours(36) |
      | @.startDate   | shiftStartDateHours(48)   |
      | @.endDate     | shiftEndDateHours(56)     |
    Then verifying status code is 200
    When calling putOffer with offerId with endpoint <endpoint>
      | path          | value                     |
      | @.displayDate | shiftDisplayDateHours(36) |
      | @.startDate   | shiftStartDateHours(48)   |
      | @.endDate     | shiftEndDateHours(56)     |
    Then verifying status code is 200
    @local
    Examples:
      | endpoint                                   |
      | http://localhost:8080/offer-management-api |
    @sole
    Examples:
      | endpoint                                                            |
      | https://sole-services.dev.api.loyalty.com:3024/offer-management-api |

  Scenario Outline: Try to update offer with end date before start date
    When calling createOffer with file name Rexall_spend and endpoint <endpoint>
      | path          | value                     |
      | @.displayDate | shiftDisplayDateHours(36) |
      | @.startDate   | shiftStartDateHours(48)   |
      | @.endDate     | shiftEndDateHours(56)     |
    Then verifying status code is 200
    When calling putOffer with offerId with endpoint <endpoint>
      | path          | value                     |
      | @.displayDate | shiftDisplayDateHours(48) |
      | @.startDate   | shiftStartDateHours(56)   |
      | @.endDate     | shiftEndDateHours(50)     |
    Then verifying status code is 400
    And verifying response body paths
      | path      | op | value                                   |
      | $.error   |    | "Operation not possible"                |
      | $.status  |    | 400                                     |
      | $.message |    | "End date cannot be before start date." |
    @local
    Examples:
      | endpoint                                   |
      | http://localhost:8080/offer-management-api |
    @sole
    Examples:
      | endpoint                                                            |
      | https://sole-services.dev.api.loyalty.com:3024/offer-management-api |

  Scenario Outline: Try to update offer with start date before display date
    When calling createOffer with file name Rexall_spend and endpoint <endpoint>
      | path          | value                     |
      | @.displayDate | shiftDisplayDateHours(36) |
      | @.startDate   | shiftStartDateHours(48)   |
      | @.endDate     | shiftEndDateHours(56)     |
    Then verifying status code is 200
    When calling putOffer with offerId with endpoint <endpoint>
      | path          | value                     |
      | @.displayDate | shiftDisplayDateHours(36) |
      | @.startDate   | shiftStartDateHours(24)   |
      | @.endDate     | shiftEndDateHours(48)     |
    Then verifying status code is 400
    And verifying response body paths
      | path      | op | value                                       |
      | $.error   |    | "Operation not possible"                    |
      | $.status  |    | 400                                         |
      | $.message |    | "Start date cannot be before display date." |
    @local
    Examples:
      | endpoint                                   |
      | http://localhost:8080/offer-management-api |
    @sole
    Examples:
      | endpoint                                                            |
      | https://sole-services.dev.api.loyalty.com:3024/offer-management-api |

  Scenario Outline: Try to update offer with end date before display date
    When calling createOffer with file name Rexall_spend and endpoint <endpoint>
      | path          | value                     |
      | @.displayDate | shiftDisplayDateHours(36) |
      | @.startDate   | shiftStartDateHours(48)   |
      | @.endDate     | shiftEndDateHours(56)     |
    Then verifying status code is 200
    When calling putOffer with offerId with endpoint <endpoint>
      | path          | value                     |
      | @.displayDate | shiftDisplayDateHours(56) |
      | @.startDate   | shiftStartDateHours(56)   |
      | @.endDate     | shiftEndDateHours(48)     |
    Then verifying status code is 400
    And verifying response body paths
      | path      | op | value                                     |
      | $.error   |    | "Operation not possible"                  |
      | $.status  |    | 400                                       |
      | $.message |    | "End date cannot be before display date." |
    @local
    Examples:
      | endpoint                                   |
      | http://localhost:8080/offer-management-api |
    @sole
    Examples:
      | endpoint                                                            |
      | https://sole-services.dev.api.loyalty.com:3024/offer-management-api |

  Scenario Outline: Try to update offer with end date before current date
    When calling createOffer with file name Rexall_spend and endpoint <endpoint>
      | path          | value                     |
      | @.displayDate | shiftDisplayDateHours(36) |
      | @.startDate   | shiftStartDateHours(48)   |
      | @.endDate     | shiftEndDateHours(56)     |
    Then verifying status code is 200
    When calling putOffer with offerId with endpoint <endpoint>
      | path          | value                      |
      | @.displayDate | shiftDisplayDateHours(-48) |
      | @.startDate   | shiftStartDateHours(-24)   |
      | @.endDate     | shiftEndDateHours(-12)     |
    Then verifying status code is 400
    And verifying response body paths
      | path      | op | value                                     |
      | $.error   |    | "Operation not possible"                  |
      | $.status  |    | 400                                       |
      | $.message |    | "End date cannot be before current date." |
    @local
    Examples:
      | endpoint                                   |
      | http://localhost:8080/offer-management-api |
    @sole
    Examples:
      | endpoint                                                            |
      | https://sole-services.dev.api.loyalty.com:3024/offer-management-api |

  Scenario Outline: Try to update bulk offers with end date before start date
    When calling createBulkOffer with file name Bulk_Offers and endpoint <endpoint>
      | path                | value                 |
      | $.offers[*].endDate | shiftEndDateHours(24) |
    Then verifying status code is 200
    When calling updateBulkOffer with file name Bulk_Offers and endpoint <endpoint>
      | path                    | value                     |
      | $.offers[0].displayDate | shiftDisplayDateHours(24) |
      | $.offers[0].startDate   | shiftStartDateHours(48)   |
      | $.offers[0].endDate     | shiftEndDateHours(40)     |
    Then verifying status code is 400
    And verifying response body paths
      | path      | op | value                                   |
      | $.error   |    | "Operation not possible"                |
      | $.status  |    | 400                                     |
      | $.message |    | "End date cannot be before start date." |
    @local
    Examples:
      | endpoint                                   |
      | http://localhost:8080/offer-management-api |
    @sole
    Examples:
      | endpoint                                                            |
      | https://sole-services.dev.api.loyalty.com:3024/offer-management-api |

  Scenario Outline: Try to update bulk offers with start date before display date
    When calling createBulkOffer with file name Bulk_Offers and endpoint <endpoint>
      | path                | value                 |
      | $.offers[*].endDate | shiftEndDateHours(24) |
    Then verifying status code is 200
    When calling updateBulkOffer with file name Bulk_Offers and endpoint <endpoint>
      | path                    | value                     |
      | $.offers[0].displayDate | shiftDisplayDateHours(36) |
      | $.offers[0].startDate   | shiftStartDateHours(24)   |
      | $.offers[0].endDate     | shiftEndDateHours(48)     |
    Then verifying status code is 400
    And verifying response body paths
      | path      | op | value                                       |
      | $.error   |    | "Operation not possible"                    |
      | $.status  |    | 400                                         |
      | $.message |    | "Start date cannot be before display date." |
    @local
    Examples:
      | endpoint                                   |
      | http://localhost:8080/offer-management-api |
    @sole
    Examples:
      | endpoint                                                            |
      | https://sole-services.dev.api.loyalty.com:3024/offer-management-api |

  Scenario Outline: Try to update bulk offers with end date before display date
    When calling createBulkOffer with file name Bulk_Offers and endpoint <endpoint>
      | path                | value                 |
      | $.offers[*].endDate | shiftEndDateHours(24) |
    Then verifying status code is 200
    When calling updateBulkOffer with file name Bulk_Offers and endpoint <endpoint>
      | path                    | value                     |
      | $.offers[0].displayDate | shiftDisplayDateHours(56) |
      | $.offers[0].startDate   | shiftStartDateHours(56)   |
      | $.offers[0].endDate     | shiftEndDateHours(48)     |
    Then verifying status code is 400
    And verifying response body paths
      | path      | op | value                                     |
      | $.error   |    | "Operation not possible"                  |
      | $.status  |    | 400                                       |
      | $.message |    | "End date cannot be before display date." |
    @local
    Examples:
      | endpoint                                   |
      | http://localhost:8080/offer-management-api |
    @sole
    Examples:
      | endpoint                                                            |
      | https://sole-services.dev.api.loyalty.com:3024/offer-management-api |