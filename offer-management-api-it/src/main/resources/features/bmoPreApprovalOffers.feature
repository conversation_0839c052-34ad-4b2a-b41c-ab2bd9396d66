Feature: Create BMO pre-approval Offer Testcases

  Scenario Outline: Create offer for BMO Bank of Montreal with Program Type = BMO Pre-approval and Mechanism = optIn.
    When calling createOffer with file name BMO_PreApproval_Offer and endpoint <endpoint>
      | path          | value                      |
      | @.displayDate | shiftDisplayDateHours(24)  |
      | @.startDate   | shiftStartDateHours(24)    |
      | @.endDate     | shiftEndDateHours(48)      |
    Then verifying status code is 200
    And  verifying response body paths
      | path                      | op | value                                   |
      | $.partnerId               |    | "22a2cdfd-ff82-45f6-bc94-c14a3a533922"  |
    Then calling Publish Offer with endpoint <endpoint>
    Then verifying status code is 204
#    @local
#    Examples:
#      | endpoint                                   |
#      | http://localhost:8080/offer-management-api |
     @sole
     Examples:
       | endpoint                                                            |
       | https://sole-services.dev.api.loyalty.com:3024/offer-management-api |

  Scenario Outline: Create offer for any other partner with Program Type = BMO Pre-approval.
    When calling createOffer with file name Sample_Offer_3 and endpoint <endpoint>
      | path          | value                       |
      | @.displayDate | shiftDisplayDateHours(24)   |
      | @.startDate   | shiftStartDateHours(24)     |
      | @.endDate     | shiftEndDateHours(48)       |
      | @.programType | "bmopreapp"                 |
    Then verifying status code is 400
    And verifying response body paths
      | path      | op | value                                                                        |
      | $.error   |    | "Operation not possible"                                                     |
      | $.status  |    | 400                                                                          |
      | $.message |    | "Program Type BMO Pre-approval only allowed with partner Bank of Montreal."  |
#    @local
#    Examples:
#      | endpoint                                   |
#      | http://localhost:8080/offer-management-api |
     @sole
     Examples:
       | endpoint                                                            |
       | https://sole-services.dev.api.loyalty.com:3024/offer-management-api |

  Scenario Outline: Create offer for BMO Bank of Montreal with Program Type = BMO Pre-approval and invalid mechanism.
    When calling createOffer with file name BMO_PreApproval_Offer and endpoint <endpoint>
      | path                          | value                      |
      | @.displayDate                 | shiftDisplayDateHours(24)  |
      | @.startDate                   | shiftStartDateHours(24)    |
      | @.endDate                     | shiftEndDateHours(48)      |
      | @.mechanisms[0].mechanismType | "noAction"                 |
    Then verifying status code is 400
    And  verifying response body paths
      | path      | op | value                                                          |
      | $.error   |    | "Operation not possible"                                       |
      | $.status  |    | 400                                                            |
      | $.message |    | "Program Type BMO Pre-approval must have optIn as mechanism."  |
#    @local
#    Examples:
#      | endpoint                                   |
#      | http://localhost:8080/offer-management-api |
     @sole
     Examples:
       | endpoint                                                            |
       | https://sole-services.dev.api.loyalty.com:3024/offer-management-api |

  Scenario Outline: Editing a BMO pre-approved Offer in Draft state to other Program Type.
    When calling createOffer with file name BMO_PreApproval_Offer and endpoint <endpoint>
      | path          | value                     |
      | @.displayDate | shiftDisplayDateHours(24) |
      | @.startDate   | shiftStartDateHours(24)   |
      | @.endDate     | shiftEndDateHours(48)     |
    Then verifying status code is 200
    And verifying response body paths
      | path                      | op | value                                   |
      | $.partnerId               |    | "22a2cdfd-ff82-45f6-bc94-c14a3a533922"  |
    When calling putOffer with offerId with endpoint <endpoint>
      | path                          | value                      |
      | @.programType                 | "traditionalcore"          |
      | @.mechanisms[0].mechanismType | "noAction"                 |
    Then verifying status code is 200
    And verifying response body paths
      | path          | value                                   |
      | $.partnerId   | "22a2cdfd-ff82-45f6-bc94-c14a3a533922"  |
      | $.programType | "traditionalcore"                       |
#    @local
#    Examples:
#      | endpoint                                   |
#      | http://localhost:8080/offer-management-api |
     @sole
     Examples:
       | endpoint                                                            |
       | https://sole-services.dev.api.loyalty.com:3024/offer-management-api |

  Scenario Outline: Editing an Offer in Draft state to Program Type = BMO pre-approved.
    When calling createOffer with file name BMO_PreApproval_Offer and endpoint <endpoint>
      | path                          | value                     |
      | @.displayDate                 | shiftDisplayDateHours(24) |
      | @.startDate                   | shiftStartDateHours(24)   |
      | @.endDate                     | shiftEndDateHours(48)     |
      | @.programType                 | "traditionalcore"         |
      | @.mechanisms[0].mechanismType | "noAction"                |
    Then verifying status code is 200
    And verifying response body paths
      | path                      | op | value                                   |
      | $.partnerId               |    | "22a2cdfd-ff82-45f6-bc94-c14a3a533922"  |
    When calling putOffer with offerId with endpoint <endpoint>
      | path                          | value       |
      | @.programType                 | "bmopreapp" |
      | @.mechanisms[0].mechanismType | "optIn"     |
    Then verifying status code is 200
    And verifying response body paths
      | path          | value                                   |
      | $.partnerId   | "22a2cdfd-ff82-45f6-bc94-c14a3a533922"  |
      | $.programType | "bmopreapp"                             |
#    @local
#    Examples:
#      | endpoint                                   |
#      | http://localhost:8080/offer-management-api |
     @sole
     Examples:
       | endpoint                                                            |
       | https://sole-services.dev.api.loyalty.com:3024/offer-management-api |

  Scenario Outline: Editing an Offer in Draft state to Program Type = BMO pre-approved but mechanism different to optIn.
    When calling createOffer with file name BMO_PreApproval_Offer and endpoint <endpoint>
      | path                          | value                     |
      | @.displayDate                 | shiftDisplayDateHours(24) |
      | @.startDate                   | shiftStartDateHours(24)   |
      | @.endDate                     | shiftEndDateHours(48)     |
      | @.programType                 | "traditionalcore"         |
      | @.mechanisms[0].mechanismType | "noAction"                |
    Then verifying status code is 200
    And verifying response body paths
      | path                      | op | value                                   |
      | $.partnerId               |    | "22a2cdfd-ff82-45f6-bc94-c14a3a533922"  |
    When calling putOffer with offerId with endpoint <endpoint>
      | path                          | value       |
      | @.programType                 | "bmopreapp" |
    Then verifying status code is 400
    And  verifying response body paths
      | path      | op | value                                                          |
      | $.error   |    | "Operation not possible"                                       |
      | $.status  |    | 400                                                            |
      | $.message |    | "Program Type BMO Pre-approval must have optIn as mechanism."  |
#    @local
#    Examples:
#      | endpoint                                   |
#      | http://localhost:8080/offer-management-api |
     @sole
     Examples:
       | endpoint                                                            |
       | https://sole-services.dev.api.loyalty.com:3024/offer-management-api |