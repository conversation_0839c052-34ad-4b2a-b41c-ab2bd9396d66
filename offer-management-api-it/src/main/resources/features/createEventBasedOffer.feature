Feature:Testcases for Event based offers

  Scenario Outline: Create and publish offer with valid payload for eventbased offer.
    When calling createOffer with file name Event_based and endpoint <endpoint>
      | path                     | value                   |
      | @.firstQualificationDate | shiftStartDateHours(24) |
      | @.lastQualificationDate  | shiftEndDateHours(48)   |
    Then verifying status code is 200
    And verifying response body paths
      | path                      | op | value                                  |
      | $.partnerId               |    | "22a2cdfd-ff82-45f6-bc94-c14a3a533922" |
      | $.eligibilityDuration     |    | 5                                      |
      | $.eligibilityDurationUnit |    | "days"                                 |
    Then calling Publish Offer with endpoint <endpoint>
    Then verifying status code is 204
#    @local
#    Examples:
#      | endpoint                                   |
#      | http://localhost:8080/offer-management-api |
    @sole
    Examples:
      | endpoint                                                            |
      | https://sole-services.dev.api.loyalty.com:3024/offer-management-api |


  Scenario Outline: Verify user is not able to create offer when mass offer is true and displayDate, startDate and endDate are missing.
    When calling createOffer with file name Event_Mass_true and endpoint <endpoint>
      | path                     | value                   |
      | @.firstQualificationDate | shiftStartDateHours(24) |
      | @.lastQualificationDate  | shiftEndDateHours(48)   |
    Then verifying status code is 400
    And verifying response body paths
      | path      | op | value                                                                                                                                        |
      | $.message |    | "[#/eventBasedOffer: , #: required key [displayDate] not found, #: required key [startDate] not found, #: required key [endDate] not found]" |
#    @local
#    Examples:
#      | endpoint                                   |
#      | http://localhost:8080/offer-management-api |
    @sole
    Examples:
      | endpoint                                                            |
      | https://sole-services.dev.api.loyalty.com:3024/offer-management-api |


  Scenario Outline: Verify user is not able to create offer when event based offer is true and eligibilityDuration, firstQualificationDate and lastQualificationDate are missing.
    When calling createOffer with file name Event_Mass_false and endpoint <endpoint>
      | path          | value                     |
      | @.displayDate | shiftDisplayDateHours(24) |
      | @.startDate   | shiftStartDateHours(24)   |
      | @.endDate     | shiftEndDateHours(48)     |
    Then verifying status code is 400
    And verifying response body paths
      | path      | op | value                                                                                                                                                                                                           |
      | $.message |    | "[#: required key [eligibilityDuration] not found, #: required key [firstQualificationDate] not found, #: required key [lastQualificationDate] not found, #: required key [eligibilityDurationUnit] not found]" |
#    @local
#    Examples:
#      | endpoint                                   |
#      | http://localhost:8080/offer-management-api |
    @sole
    Examples:
      | endpoint                                                            |
      | https://sole-services.dev.api.loyalty.com:3024/offer-management-api |


  Scenario Outline: Update a draft event based offer to an eventbased value as false.
    When calling createOffer with file name Event_based and endpoint <endpoint>
      | path                     | value                   |
      | @.firstQualificationDate | shiftStartDateHours(24) |
      | @.lastQualificationDate  | shiftEndDateHours(48)   |
    Then verifying status code is 200
    When calling putOffer with offerId with endpoint <endpoint>
      | path              | value |
      | @.eventBasedOffer | false |
    Then verifying status code is 400
    And verifying response body paths
      | path      | op | value                                                                                                                                          |
      | $.message |    | "First qualification date, last qualification date, eligibility duration and eligibility duration unit are valid just for event based offers." |
#    @local
#    Examples:
#      | endpoint                                   |
#      | http://localhost:8080/offer-management-api |
    @sole
    Examples:
      | endpoint                                                            |
      | https://sole-services.dev.api.loyalty.com:3024/offer-management-api |


#  Scenario Outline: Update a draft Mass offer to eventbased offer.
#    When calling createOffer with file name Regular_Mass_true and endpoint <endpoint>
#      | path          | value                     |
#      | @.displayDate | shiftDisplayDateHours(24) |
#      | @.startDate   | shiftStartDateHours(24)   |
#      | @.endDate     | shiftEndDateHours(48)     |
#    Then verifying status code is 200
#    When calling putOffer with offerId with endpoint <endpoint>
#      | path              | value |
#      | @.eventBasedOffer | true  |
#      | @.massOffer       | false |
#    Then verifying status code is 400
#    And verifying response body paths
#      | path      | op | value                                                                                                                                                                                                                                                                                                                                                                   |
#      | $.message |    | "[#: required key [eligibilityDuration] not found, #: required key [firstQualificationDate] not found, #: required key [lastQualificationDate] not found, #: required key [eligibilityDurationUnit] not found, #: required key [firstQualificationDate] not found, #: required key [lastQualificationDate] not found, #: required key [eligibilityDuration] not found]" |
#    @local
#    Examples:
#      | endpoint                                   |
#      | http://localhost:8080/offer-management-api |
    @sole
    Examples:
      | endpoint                                                            |
      | https://sole-services.dev.api.loyalty.com:3024/offer-management-api |


#  Scenario Outline: Update a published Mass offer to eventbased offer.
#    When calling createOffer with file name Regular_Mass_true and endpoint <endpoint>
#      | path          | value                     |
#      | @.displayDate | shiftDisplayDateHours(24) |
#      | @.startDate   | shiftStartDateHours(24)   |
#      | @.endDate     | shiftEndDateHours(48)     |
#    Then verifying status code is 200
#    Then calling Publish Offer with endpoint <endpoint>
#    Then verifying status code is 204
#    When calling putOffer with offerId with endpoint <endpoint>
#      | path              | value |
#      | @.eventBasedOffer | true  |
#      | @.massOffer       | false |
#    Then verifying status code is 400
#    And verifying response body paths
#      | path      | op | value                                                                                                                                                                                                                                                                                                                                                                   |
#      | $.message |    | "[#: required key [eligibilityDuration] not found, #: required key [firstQualificationDate] not found, #: required key [lastQualificationDate] not found, #: required key [eligibilityDurationUnit] not found, #: required key [firstQualificationDate] not found, #: required key [lastQualificationDate] not found, #: required key [eligibilityDuration] not found]" |
#    @local
#    Examples:
#      | endpoint                                   |
#      | http://localhost:8080/offer-management-api |
    @sole
    Examples:
      | endpoint                                                            |
      | https://sole-services.dev.api.loyalty.com:3024/offer-management-api |


  Scenario Outline: Update a published event based offer to eventBasedOffer = false
    When calling createOffer with file name Event_based and endpoint <endpoint>
      | path                     | value                   |
      | @.firstQualificationDate | shiftStartDateHours(24) |
      | @.lastQualificationDate  | shiftEndDateHours(48)   |
    Then verifying status code is 200
    Then calling Publish Offer with endpoint <endpoint>
    Then verifying status code is 204
    When calling putOffer with offerId with endpoint <endpoint>
      | path              | value |
      | @.eventBasedOffer | false |
    And verifying response body paths
      | path      | op | value                                                                                                                                           |
      | $.message |    | "First qualification date, last qualification date, eligibility duration and eligibility duration unit are valid just for event based offers." |
#    @local
#    Examples:
#      | endpoint                                   |
#      | http://localhost:8080/offer-management-api |
    @sole
    Examples:
      | endpoint                                                            |
      | https://sole-services.dev.api.loyalty.com:3024/offer-management-api |