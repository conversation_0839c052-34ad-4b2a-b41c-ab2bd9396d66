Feature: Offer Facets Endpoints

  # happy path of using facets is actually covered thru all publish cases IF they have the fields
  Scenario Outline: Create and publish offer with HAPPY facet endpoints
    When running WireMock on host <wireMockHost> and port <wireMockPort>
    And removing all WireMock mappings
    And DynamoDB on url <dynamoDbUrl>, region <awsRegion> is accessible and client is initialized
    And all records removed from DynamoDB tables
      | <dynamoDbEventStoreTable> |
    And stabbing AirmilesOfferApiV2Mock for stub ok
    And stabbing ContentGenerationServiceMock for stub ok
    And stabbing OfferFacetsApiMock for stub ok
    And stabbing PromotionApiMock for stub ok
    When calling createOffer with file name offer_for_facets and endpoint <endpoint>
      | path          | value                       |
      | @.displayDate | shiftDisplayDateHours(-240) |
      | @.startDate   | shiftStartDateHours(-200)   |
      | @.endDate     | shiftEndDateHours(24)       |
    Then verifying status code is 200
    Then calling Publish Offer with endpoint <endpoint>
    Then verifying status code is 204
#    Then verify table <dynamoDbEventStoreTable> has the locale labels

    @local
    Examples:
      | endpoint                                   | awsRegion | dynamoDbUrl           | dynamoDbEventStoreTable                | wireMockHost | wireMockPort |
      | http://localhost:8080/offer-management-api | us-east-1 | http://localhost:8000 | local-nova-event-bus-offer-event-store | localhost    | 9090         |
    @sole
    Examples:
      | endpoint                                                            | awsRegion | dynamoDbUrl                              | dynamoDbEventStoreTable               | wireMockHost | wireMockPort |
      | https://sole-services.dev.api.loyalty.com:3024/offer-management-api | us-east-1 | https://dynamodb.us-east-1.amazonaws.com | sole-offer-management-api-event-store | localhost    | 9090         |

  Scenario Outline: Failure on publish offer because of categories
    When running WireMock on host <wireMockHost> and port <wireMockPort>
    And removing all WireMock mappings
    And DynamoDB on url <dynamoDbUrl>, region <awsRegion> is accessible and client is initialized
    And stabbing AirmilesOfferApiV2Mock for stub ok
    And stabbing ContentGenerationServiceMock for stub ok
    And stabbing OfferFacetsApiMock for stub notFound
    And stabbing PromotionApiMock for stub ok
    When calling createOffer with file name offer_for_facets and endpoint <endpoint>
      | path          | value                       |
      | @.displayDate | shiftDisplayDateHours(-240) |
      | @.startDate   | shiftStartDateHours(-200)   |
      | @.endDate     | shiftEndDateHours(24)       |
    Then verifying status code is 200
    Then calling Publish Offer with endpoint <endpoint>
    Then verifying status code is 400
    @local
    Examples:
      | endpoint                                   | awsRegion | dynamoDbUrl           | wireMockHost | wireMockPort |
      | http://localhost:8080/offer-management-api | us-east-1 | http://localhost:8000 | localhost    | 9090         |
#    @sole
#    Examples:
#      | endpoint                                                            | awsRegion | dynamoDbUrl                              | wireMockHost                          | wireMockPort |
#      | https://sole-services.dev.api.loyalty.com:3024/offer-management-api | us-east-1 | https://dynamodb.us-east-1.amazonaws.com | wire-mock-service.dev.api.loyalty.com | 8080         |

  Scenario Outline: Failure on publish offer because of promotions
    When running WireMock on host <wireMockHost> and port <wireMockPort>
    And removing all WireMock mappings
    And DynamoDB on url <dynamoDbUrl>, region <awsRegion> is accessible and client is initialized
    And stabbing AirmilesOfferApiV2Mock for stub ok
    And stabbing ContentGenerationServiceMock for stub ok
    And stabbing OfferFacetsApiMock for stub ok
    And stabbing PromotionApiMock for stub internalServerError
    When calling createOffer with file name offer_for_facets and endpoint <endpoint>
      | path          | value                       |
      | @.displayDate | shiftDisplayDateHours(-240) |
      | @.startDate   | shiftStartDateHours(-200)   |
      | @.endDate     | shiftEndDateHours(24)       |
    Then verifying status code is 200
    Then calling Publish Offer with endpoint <endpoint>
    Then verifying status code is 400

    @local
    Examples:
      | endpoint                                   | awsRegion | dynamoDbUrl           | wireMockHost | wireMockPort |
      | http://localhost:8080/offer-management-api | us-east-1 | http://localhost:8000 | localhost    | 9090         |
#    @sole
#    Examples:
#      | endpoint                                                            | awsRegion | dynamoDbUrl                              | wireMockHost                          | wireMockPort |
#      | https://sole-services.dev.api.loyalty.com:3024/offer-management-api | us-east-1 | https://dynamodb.us-east-1.amazonaws.com | wire-mock-service.dev.api.loyalty.com | 8080         |
