Feature: Editing a disabled offer

  Scenario Outline: Create, publish, and disable an offer. Attempt to edit the disabled offer.
    When running WireMock on host <wireMockHost> and port <wireMockPort>
    And removing all WireMock mappings
    And stabbing AirmilesOfferApiV2Mock for stub <airmilesOfferApiV2MockStub>
    And stabbing ContentGenerationServiceMock for stub <contentGenerationServiceMockStub>
    And stabbing OfferFacetsApiMock for stub ok
    And stabbing PromotionApiMock for stub ok
    When calling createOffer with file name Rex<PERSON>_spend and endpoint <endpoint>
      | path          | value                       |
      | @.displayDate | shiftDisplayDateHours(-240) |
      | @.startDate   | shiftStartDateHours(-200)   |
      | @.endDate     | shiftEndDateHours(24)       |
    Then verifying status code is 200
    And verifying response body paths
      | path                           | op    | value                                         |
      | $.partnerId                    |       | "3db0b3e0-4134-453e-8381-08e90f2b5141"        |
      | $.offerType                    |       | "spend"                                       |
      | $.baseCashRedemption           |       | 95                                            |
      | $.tiers[2].qualifierLong.en-US |       | "Spend $30+ on almost anything in-store*"     |
      | $.legalText.en-US              | regex | ".*Valid at participating Rexall locations.*" |
    Then calling Publish Offer with endpoint <endpoint>
    Then verifying status code is 204
    When calling Disable Offer with offerId with endpoint <endpoint>
    Then verifying status code is 200
    And verifying response body paths
      | path                           | op    | value                                         |
      | $.partnerId                    |       | "3db0b3e0-4134-453e-8381-08e90f2b5141"        |
      | $.offerType                    |       | "spend"                                       |
      | $.baseCashRedemption           |       | 95                                            |
      | $.tiers[2].qualifierLong.en-US |       | "Spend $30+ on almost anything in-store*"     |
      | $.legalText.en-US              | regex | ".*Valid at participating Rexall locations.*" |
    When calling putOffer with offerId with endpoint <endpoint>
      | path | value |
    Then verifying status code is 200

    @local
    Examples:
      | endpoint                                   | awsRegion | dynamoDbUrl           | dynamoDbEventStoreTable                | wireMockHost | wireMockPort | airmilesOfferApiV2MockStub | contentGenerationServiceMockStub |
      | http://localhost:8080/offer-management-api | us-east-1 | http://localhost:8000 | local-nova-event-bus-offer-event-store | localhost    | 9090         | ok                         | ok                               |

    @sole
    Examples:
      | endpoint                                                            | awsRegion | dynamoDbUrl                              | dynamoDbEventStoreTable               | wireMockHost | wireMockPort | airmilesOfferApiV2MockStub | contentGenerationServiceMockStub |
      | https://sole-services.dev.api.loyalty.com:3024/offer-management-api | us-east-1 | https://dynamodb.us-east-1.amazonaws.com | sole-offer-management-api-event-store | localhost    | 9090         | ok                         | ok                               |
