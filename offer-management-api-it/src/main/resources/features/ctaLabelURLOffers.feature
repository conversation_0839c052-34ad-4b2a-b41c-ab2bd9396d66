Feature: Create ctaLabel & ctaURL Offer Testcases

  Scenario Outline: Create offer with ctaLabel & ctaURL for english and french with programtype=traditionalcore and mechanism=noAction.
    When calling createOffer with file name ctaLabelURL_Offer and endpoint <endpoint>
      | path             | value                     |
      | @.displayDate    | shiftDisplayDateHours(24) |
      | @.startDate      | shiftStartDateHours(24)   |
      | @.endDate        | shiftEndDateHours(48)     |
    Then verifying status code is 200
    And verifying response body paths
      | path             | op | value                         |
      | $.ctaLabel.en-US |    | "english label"               |
      | $.ctaLabel.fr-CA |    | "française"                   |
      | $.ctaUrl.en-US   |    | "https://testCTA.com/English" |
      | $.ctaUrl.fr-CA   |    | "https://testCTA.com/French"  |
    Then calling Publish Offer with endpoint <endpoint>
    Then verifying status code is 204
#    @local
#    Examples:
#      | endpoint                                   |
#      | http://localhost:8080/offer-management-api |
    @sole
    Examples:
      | endpoint                                                            |
      | https://sole-services.dev.api.loyalty.com:3024/offer-management-api |


  Scenario Outline: Create offer with ctaLabel & ctaURL for english and french with programtype=airmilesshops and mechanism=optin.
    When calling createOffer with file name ctaLabelURL_Offer and endpoint <endpoint>
      | path                          | value                     |
      | @.displayDate                 | shiftDisplayDateHours(24) |
      | @.startDate                   | shiftStartDateHours(24)   |
      | @.endDate                     | shiftEndDateHours(48)     |
      | @.programType                 | "airmilesshops"           |
      | @.mechanisms[0].mechanismType | "optIn"                   |
    Then verifying status code is 200
    And verifying response body paths
      | path                          | op | value                         |
      | $.ctaLabel.en-US              |    | "english label"               |
      | $.ctaLabel.fr-CA              |    | "française"                   |
      | $.ctaUrl.en-US                |    | "https://testCTA.com/English" |
      | $.ctaUrl.fr-CA                |    | "https://testCTA.com/French"  |
      | $.programType                 |    | "airmilesshops"               |
      | $.mechanisms[0].mechanismType |    | "optIn"                       |
    Then calling Publish Offer with endpoint <endpoint>
    Then verifying status code is 204
#    @local
#    Examples:
#      | endpoint                                   |
#      | http://localhost:8080/offer-management-api |
    @sole
    Examples:
      | endpoint                                                            |
      | https://sole-services.dev.api.loyalty.com:3024/offer-management-api |


  Scenario Outline: Create offer with ctaLabel & ctaURL for english and french with programtype=cardlinked with cardtype=NonBmoMastercard,BmoMastercard,BmoDebit and mechanism=noAction.
    When calling createOffer with file name ctaLabelURL_Offer and endpoint <endpoint>
      | path          | value                                                   |
      | @.displayDate | shiftDisplayDateHours(24)                               |
      | @.startDate   | shiftStartDateHours(24)                                 |
      | @.endDate     | shiftEndDateHours(48)                                   |
      | @.programType | "cardlinked"                                            |
      | @.cardType    | cardTypeList("NonBmoMastercard,BmoMastercard,BmoDebit") |
    Then verifying status code is 200
    And verifying response body paths
      | path             | op | value                                   |
      | $.ctaLabel.en-US |    | "english label"                         |
      | $.ctaLabel.fr-CA |    | "française"                             |
      | $.ctaUrl.en-US   |    | "https://testCTA.com/English"           |
      | $.ctaUrl.fr-CA   |    | "https://testCTA.com/French"            |
      | $.cardType       |    | NonBmoMastercard,BmoMastercard,BmoDebit |
    Then calling Publish Offer with endpoint <endpoint>
    Then verifying status code is 204
#    @local
#    Examples:
#      | endpoint                                   |
#      | http://localhost:8080/offer-management-api |
    @sole
    Examples:
      | endpoint                                                            |
      | https://sole-services.dev.api.loyalty.com:3024/offer-management-api |


  Scenario Outline: Create offer with ctaLabel & ctaURL for english and french with programtype=cardlinked with cardtype=NonBmomastercard,bmomastercard and mechanism=optin.
    When calling createOffer with file name ctaLabelURL_Offer and endpoint <endpoint>
      | path                          | value                                          |
      | @.displayDate                 | shiftDisplayDateHours(24)                      |
      | @.startDate                   | shiftStartDateHours(24)                        |
      | @.endDate                     | shiftEndDateHours(48)                          |
      | @.programType                 | "cardlinked"                                   |
      | @.cardType                    | cardTypeList("NonBmoMastercard,BmoMastercard") |
      | @.mechanisms[0].mechanismType | "optIn"                                        |
    Then verifying status code is 200
    And verifying response body paths
      | path                          | op | value                          |
      | $.ctaLabel.en-US              |    | "english label"                |
      | $.ctaLabel.fr-CA              |    | "française"                    |
      | $.ctaUrl.en-US                |    | "https://testCTA.com/English"  |
      | $.ctaUrl.fr-CA                |    | "https://testCTA.com/French"   |
      | $.programType                 |    | "cardlinked"                   |
      | $.mechanisms[0].mechanismType |    | "optIn"                        |
      | $.cardType                    |    | NonBmoMastercard,BmoMastercard |
    Then calling Publish Offer with endpoint <endpoint>
    Then verifying status code is 204
#    @local
#    Examples:
#      | endpoint                                   |
#      | http://localhost:8080/offer-management-api |
    @sole
    Examples:
      | endpoint                                                            |
      | https://sole-services.dev.api.loyalty.com:3024/offer-management-api |


  Scenario Outline: Create offer with ctaLabel & ctaURL for english and french with programtype=cardlinked with cardtype=BmoDebit and mechanism=noAction.
    When calling createOffer with file name ctaLabelURL_Offer and endpoint <endpoint>
      | path          | value                     |
      | @.displayDate | shiftDisplayDateHours(24) |
      | @.startDate   | shiftStartDateHours(24)   |
      | @.endDate     | shiftEndDateHours(48)     |
      | @.programType | "cardlinked"              |
      | @.cardType    | cardTypeList("BmoDebit")  |
    Then verifying status code is 200
    And verifying response body paths
      | path             | op | value                         |
      | $.ctaLabel.en-US |    | "english label"               |
      | $.ctaLabel.fr-CA |    | "française"                   |
      | $.ctaUrl.en-US   |    | "https://testCTA.com/English" |
      | $.ctaUrl.fr-CA   |    | "https://testCTA.com/French"  |
      | $.cardType       |    | BmoDebit                      |
    Then calling Publish Offer with endpoint <endpoint>
    Then verifying status code is 204
#    @local
#    Examples:
#      | endpoint                                   |
#      | http://localhost:8080/offer-management-api |
    @sole
    Examples:
      | endpoint                                                            |
      | https://sole-services.dev.api.loyalty.com:3024/offer-management-api |


  Scenario Outline: Create offer with ctaLabel & ctaURL for just english with programtype=traditionalcore and mechanism=optin.
    When calling createOffer with file name ctaLabelURL_Offer and endpoint <endpoint>
      | path                          | value                         |
      | @.displayDate                 | shiftDisplayDateHours(24)     |
      | @.startDate                   | shiftStartDateHours(24)       |
      | @.endDate                     | shiftEndDateHours(48)         |
      | @.programType                 | "traditionalcore"             |
      | @.mechanisms[0].mechanismType | "optIn"                       |
      | @.ctaLabel.en-US              | "english label"               |
      | @.ctaUrl.en-US                | "https://testCTA.com/English" |
      | @.ctaLabel.fr-CA              | ""                            |
      | @.ctaUrl.fr-CA                | ""                            |
    Then verifying status code is 200
    And verifying response body paths
      | path                          | op | value                         |
      | $.ctaLabel.en-US              |    | "english label"               |
      | $.ctaLabel.fr-CA              |    | "english label"               |
      | $.ctaUrl.en-US                |    | "https://testCTA.com/English" |
      | $.ctaUrl.fr-CA                |    | "https://testCTA.com/English" |
      | $.programType                 |    | "traditionalcore"             |
      | $.mechanisms[0].mechanismType |    | "optIn"                       |
    Then calling Publish Offer with endpoint <endpoint>
    Then verifying status code is 204
#    @local
#    Examples:
#      | endpoint                                   |
#      | http://localhost:8080/offer-management-api |
    @sole
    Examples:
      | endpoint                                                            |
      | https://sole-services.dev.api.loyalty.com:3024/offer-management-api |


  Scenario Outline: Create offer with ctaLabel & ctaURL blank with programType=airmilesshops and mechanism=noAction.
    When calling createOffer with file name Sample_Offer_1 and endpoint <endpoint>
      | path             | value                     |
      | @.displayDate    | shiftDisplayDateHours(24) |
      | @.startDate      | shiftStartDateHours(24)   |
      | @.endDate        | shiftEndDateHours(48)     |
      | @.programType    | "airmilesshops"           |
    Then verifying status code is 200
    And verifying response body paths
      | path             | op | value                                  |
      | $.partnerId      |    | "87ab4dd0-65b5-41a3-acd2-b97540dc1aff" |
    Then calling Publish Offer with endpoint <endpoint>
    Then verifying status code is 204
#    @local
#    Examples:
#      | endpoint                                   |
#      | http://localhost:8080/offer-management-api |
    @sole
    Examples:
      | endpoint                                                            |
      | https://sole-services.dev.api.loyalty.com:3024/offer-management-api |


  Scenario Outline: Create offer with blank ctaLabel & ctaURL for english and french with programtype=cardlinked with cardtype=NonBmomastercard,bmomastercard and mechanism=noAction.
    When calling createOffer with file name Sample_Offer_1 and endpoint <endpoint>
      | path                          | value                                          |
      | @.displayDate                 | shiftDisplayDateHours(24)                      |
      | @.startDate                   | shiftStartDateHours(24)                        |
      | @.endDate                     | shiftEndDateHours(48)                          |
      | @.programType                 | "cardlinked"                                   |
      | @.cardType                    | cardTypeList("NonBmoMastercard,BmoMastercard") |
      | @.mechanisms[0].mechanismType | "noAction"                                     |
    Then verifying status code is 200
    And verifying response body paths
      | path                          | op | value                          |
      | $.programType                 |    | "cardlinked"                   |
      | $.mechanisms[0].mechanismType |    | "noAction"                     |
      | $.cardType                    |    | NonBmoMastercard,BmoMastercard |
    Then calling Publish Offer with endpoint <endpoint>
    Then verifying status code is 204
#    @local
#    Examples:
#      | endpoint                                   |
#      | http://localhost:8080/offer-management-api |
    @sole
    Examples:
      | endpoint                                                            |
      | https://sole-services.dev.api.loyalty.com:3024/offer-management-api |


  Scenario Outline: Editing offer in Draft state with different ctaLabel & ctaURL for english and french with programtype=traditional and mechanism=noAction.
    When calling createOffer with file name ctaLabelURL_Offer and endpoint <endpoint>
      | path          | value                     |
      | @.displayDate | shiftDisplayDateHours(24) |
      | @.startDate   | shiftStartDateHours(24)   |
      | @.endDate     | shiftEndDateHours(48)     |
    Then verifying status code is 200
    And verifying response body paths
      | path        | value                                  |
      | $.partnerId | "ffdfc361-38c9-44e6-bc9c-afb097a3b76d" |
    When calling putOffer with offerId with endpoint <endpoint>
      | path             | value                    |
      | @.ctaLabel.en-US | "Shop Now!"              |
      | @.ctaLabel.fr-CA | "Magasiner Maintenant!"  |
      | @.ctaUrl.en-US   | "https://www.google.com" |
      | @.ctaUrl.fr-CA   | "https://www.google.com" |
    Then verifying status code is 200
    And verifying response body paths
      | path             | value                                  |
      | $.partnerId      | "ffdfc361-38c9-44e6-bc9c-afb097a3b76d" |
      | $.ctaLabel.en-US | "Shop Now!"                            |
      | $.ctaLabel.fr-CA | "Magasiner Maintenant!"                |
      | $.ctaUrl.en-US   | "https://www.google.com"               |
      | $.ctaUrl.fr-CA   | "https://www.google.com"               |
#    @local
#    Examples:
#      | endpoint                                   |
#      | http://localhost:8080/offer-management-api |
    @sole
    Examples:
      | endpoint                                                            |
      | https://sole-services.dev.api.loyalty.com:3024/offer-management-api |


  Scenario Outline: Editing offer in Published state with different ctaLabel & ctaURL for english and french with programtype=traditional and mechanism=noAction.
    When calling createOffer with file name ctaLabelURL_Offer and endpoint <endpoint>
      | path          | value                     |
      | @.displayDate | shiftDisplayDateHours(24) |
      | @.startDate   | shiftStartDateHours(24)   |
      | @.endDate     | shiftEndDateHours(48)     |
    Then verifying status code is 200
    Then calling Publish Offer with endpoint <endpoint>
    Then verifying status code is 204
    And verifying response body paths
      | path        | value                                  |
      | $.partnerId | "ffdfc361-38c9-44e6-bc9c-afb097a3b76d" |
    When calling putOffer with offerId with endpoint <endpoint>
      | path             | value                             |
      | @.ctaLabel.en-US | "english label test"              |
      | @.ctaLabel.fr-CA | "française test"                  |
      | @.ctaUrl.en-US   | "https://testCTA.com/Englishtest" |
      | @.ctaUrl.fr-CA   | "https://testCTA.com/Frenchtest"  |
    Then verifying status code is 200
    And verifying response body paths
      | path                          | value                             |
      | $.ctaLabel.en-US              | "english label test"              |
      | $.ctaLabel.fr-CA              | "française test"                  |
      | $.ctaUrl.en-US                | "https://testCTA.com/Englishtest" |
      | $.ctaUrl.fr-CA                | "https://testCTA.com/Frenchtest"  |
      | $.programType                 | "traditionalcore"                 |
      | $.mechanisms[0].mechanismType | "noAction"                        |
    Then calling Publish Offer with endpoint <endpoint>
    Then verifying status code is 204
#    @local
#    Examples:
#      | endpoint                                   |
#      | http://localhost:8080/offer-management-api |
    @sole
    Examples:
      | endpoint                                                            |
      | https://sole-services.dev.api.loyalty.com:3024/offer-management-api |


  Scenario Outline: Create offer with only ctaURL values present in english and french field.
    When calling createOffer with file name ctaLabelURLOnly_Offer and endpoint <endpoint>
      | path             | value                         |
      | @.displayDate    | shiftDisplayDateHours(24)     |
      | @.startDate      | shiftStartDateHours(24)       |
      | @.endDate        | shiftEndDateHours(48)         |
    Then verifying status code is 400
    And verifying response body paths
      | path      | op | value                                            |
      | $.error   |    | "Operation not possible"                         |
      | $.status  |    | 400                                              |
      | $.message |    | "ctaLabel and ctaUrl fields are both mandatory." |
#    @local
#    Examples:
#      | endpoint                                   |
#      | http://localhost:8080/offer-management-api |
    @sole
    Examples:
      | endpoint                                                            |
      | https://sole-services.dev.api.loyalty.com:3024/offer-management-api |


  Scenario Outline: Create offer with only ctalabel values present in english and french field.
    When calling createOffer with file name ctaLabelOnly_Offer and endpoint <endpoint>
      | path          | value                     |
      | @.displayDate | shiftDisplayDateHours(24) |
      | @.startDate   | shiftStartDateHours(24)   |
      | @.endDate     | shiftEndDateHours(48)     |
    Then verifying status code is 400
    And verifying response body paths
      | path      | op | value                                            |
      | $.error   |    | "Operation not possible"                         |
      | $.status  |    | 400                                              |
      | $.message |    | "ctaLabel and ctaUrl fields are both mandatory." |
#    @local
#    Examples:
#      | endpoint                                   |
#      | http://localhost:8080/offer-management-api |
    @sole
    Examples:
      | endpoint                                                            |
      | https://sole-services.dev.api.loyalty.com:3024/offer-management-api |