package com.loyalty.offermanagement.integration

import com.loyalty.nova.common.test.dynamodb.DynamoDBClient
import com.loyalty.nova.common.test.integration.RestCallContext
import com.loyalty.nova.common.test.integration.StepDefs
import com.loyalty.nova.common.test.performance.JMeterRunner
import com.loyalty.offermanagement.testutils.OfferManagementDynamoDBLocalFixture
import org.springframework.boot.test.context.SpringBootTest

@SpringBootTest(classes = [ResponseContext::class, RestCallContext::class, OfferManagementDynamoDBLocalFixture::class, DynamoDBClient::class, JMeterRunner::class])
class ConfigLoaderStepDefs : StepDefs

