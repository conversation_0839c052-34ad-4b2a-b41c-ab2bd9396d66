@file:Suppress(
        "MagicN<PERSON>ber",
        "TooGenericExceptionCaught",
        "UnsafeCallOnNullableType",
        "UnusedImports"
)

package com.loyalty.offermanagement.integration

import com.loyalty.nova.common.events.definitions.data.v3.OfferPublishedEventData
import com.loyalty.nova.common.test.dynamodb.DynamoDBClient
import com.loyalty.nova.common.test.dynamodb.DynamoDBValidator
import com.loyalty.nova.common.test.integration.RestCallContext
import com.loyalty.nova.common.test.integration.StepDefs
import com.loyalty.nova.common.test.utils.OpType
import com.loyalty.nova.common.test.utils.ValidationRule
import io.cucumber.datatable.DataTable
import org.json.JSONObject
import org.springframework.beans.factory.annotation.Autowired
import kotlin.test.assertEquals

class DynamoDBStepDefs : StepDefs {
    @Autowired
    private lateinit var dynamoDBClient: DynamoDBClient

    @Autowired
    private lateinit var context: RestCallContext

    init {
        Given("^DynamoDB on url (\\S+), region (\\S+) is accessible and client is initialized$") { url: String,
                                                                                                   region: String ->
            dynamoDBClient.start(url, region)
        }

        Given("^all records removed from DynamoDB tables$") { tableNames: DataTable ->
            for (tableName in tableNames.asList()) {
                dynamoDBClient.cleanTable(tableName)
            }
        }

        Given("^The event (.*) has been added to table (\\S+)$") { testDataFileName: String,
                                                                   tableName: String ->
            if (!"".equals(testDataFileName.trim())) {
                try {
                    dynamoDBClient.addItem("/test-data/$testDataFileName.json", tableName, OfferPublishedEventData::class.java)
                } catch (e: Throwable) {
                    println(e)
                }
            }
        }

        Then("^Verify table (\\S+) has (\\d+) records for this offer$") { tableName: String, recordCount: Int ->
            val results = DynamoDBValidator.scanByAttributes(
                    dynamoDBClient.dynamoDB,
                    tableName,
                    "data.id",
                    JSONObject(context.responseBody).getString("id"))
            // be aware this ID is JUST A HACK in mock response and the real event id is not "exposable" to this layer
            // so in mock response, we are passing the offerId as eventId, to cover this test
            assertEquals(recordCount, results?.count)
        }

        // TODO refactor to a generic function
        Then("^verify table (\\S+) has the locale labels$") { tableName: String ->
            dynamoDBClient.dynamoDB?.let {
                val scanResult = DynamoDBValidator.scanDynamoDbTable(it, tableName, listOf("id", "data"))
                DynamoDBValidator.assertDynamoDbTable(scanResult, listOf(
                        ValidationRule("count", OpType.equal, listOf(1)),
                        ValidationRule("$.items.length()", OpType.equal, listOf(1)),
                        ValidationRule("""$.items[0].data.m.offerCategory1Label.m.en-US.s""", OpType.equal, "Liquor"),
                        ValidationRule("""$.items[0].data.m.offerCategory1Label.m.fr-CA.s""", OpType.equal, "Alcool"),
                        ValidationRule("""$.items[0].data.m.promotionLabel.m.en-US.s""", OpType.equal, "Mega Miles"),
                        ValidationRule("""$.items[0].data.m.promotionLabel.m.fr-CA.s""", OpType.equal, "Mega Milles")
                )
                )
            }
        }

        Then("^verify table (\\S+) has (\\S+) records$") { tableName: String, recordCount: Int ->
            dynamoDBClient.dynamoDB?.let {
                val results = DynamoDBValidator.scanDynamoDbTable(
                        it,
                        tableName,
                        listOf("id")
                )
                println(results.count)
                assertEquals(recordCount.toInt(), results.count)
            }
        }
    }
}
