@file:Suppress(
        "LargeClass",
        "TooManyFunctions",
        "MagicNumber",
        "ForbiddenComment",
        "UnusedPrivateMember"
)

package com.loyalty.offermanagement.integration

import com.github.tomakehurst.wiremock.WireMockServer
import com.github.tomakehurst.wiremock.core.WireMockConfiguration
import com.loyalty.nova.common.test.integration.RestCallContext
import com.loyalty.nova.common.test.integration.StepDefs
import com.loyalty.offermanagement.testutils.CreateOfferRecordFactory
import com.loyalty.offermanagement.testutils.JsonPathModifyer
import cucumber.api.Scenario
import cucumber.api.java.After
import cucumber.api.java.Before
import io.cucumber.datatable.DataTable
import org.apache.http.Header
import org.apache.http.HttpMessage
import org.apache.http.client.methods.HttpGet
import org.apache.http.client.methods.HttpPatch
import org.apache.http.client.methods.HttpPost
import org.apache.http.client.methods.HttpPut
import org.apache.http.entity.ContentType
import org.apache.http.entity.StringEntity
import org.apache.http.impl.client.HttpClients
import org.apache.http.message.BasicHeader
import org.apache.http.util.EntityUtils
import org.json.JSONObject
import org.junit.Assert
import org.springframework.beans.factory.annotation.Autowired
import java.util.UUID

class OfferStepDefs : StepDefs {
    @Autowired
    private lateinit var context: RestCallContext

    @Autowired
    private lateinit var responseContext: ResponseContext

    private lateinit var scenario: Scenario

    private val wireMockServer = WireMockServer(WireMockConfiguration.wireMockConfig().port(9090))

    companion object {
        var setOffer: SetOffers = SetOffers(false)
    }

    @Before
    fun before(scenario: Scenario) {
        println("Starting Server")
        wireMockServer.start()
        this.scenario = scenario
    }

    @After
    fun after() {
        println("Stopping Server")
        wireMockServer.stop()
    }

    init {
        And("^All test offers created and published through (\\S+)$") { endpoint: String ->
            if (!setOffer.testOffersCreated) {
                val offersLive = mapOf(
                        "path" to "value",
                        "@.displayDate" to "shiftDisplayDateHours(-240)",
                        "@.startDate" to "shiftStartDateHours(-200)",
                        "@.endDate" to "shiftEndDateHours(120)"
                )
                val offersStaged = mapOf(
                        "path" to "value",
                        "@.displayDate" to "shiftDisplayDateHours(100)",
                        "@.startDate" to "shiftStartDateHours(120)",
                        "@.endDate" to "shiftEndDateHours(160)"
                )
                // TODO: Find workaround to create expired offers as they cannot be created through the API anymore
                val offersExpired = mapOf(
                        "path" to "value",
                        "@.displayDate" to "shiftDisplayDateHours(-240)",
                        "@.startDate" to "shiftStartDateHours(-200)",
                        "@.endDate" to "shiftEndDateHours(-25)"
                )
                val offersToCreate = mutableMapOf(
                        "Rexall_spend" to offersLive,
                        "Sample_Offer_1" to offersLive,
                        "Sample_Offer_2" to offersLive,
                        "Sample_Offer_3" to offersLive,
                        "Sample_Offer_4" to offersLive,
                        "Sample_Offer_4" to offersLive,
                        "Sample_Offer_5" to offersLive,
                        "Sample_Offer_6" to offersLive,
//                        "Sample_Offer_7" to offersExpired,
                        "Sample_Offer_8" to offersStaged
                )

                offersToCreate.forEach { (offerId, dataMap) ->
                    createOffer(offerId, endpoint, dataMap)
                    if (offerId != "Sample_Offer_6") {
                        publishOffer(endpoint)
                    }
                }
                setOffer.testOffersCreated = true
            }
        }
        When("^calling createOffer with file name (\\S+) and endpoint (\\S+)$")
        { fileName: String, endpoint: String, table: DataTable ->
            val transformationData: Map<String, String> = table.asMap<String, String>(
                    String::class.java, String::class.java).filterKeys {
                !it.contains("path")
            }
            createOffer(fileName, endpoint, transformationData)
        }
        When("^calling createBulkOffer with file name (\\S+) and endpoint (\\S+)$")
        { fileName: String, endpoint: String, table: DataTable ->
            val transformationData: Map<String, String> =
                    table.asMap<String, String>(String::class.java, String::class.java).filterKeys {
                        !it.contains("path")
                    }
            val request = HttpPost("$endpoint/v3/offers/bulk")
            request.entity = StringEntity(
                    CreateOfferRecordFactory.getOfferData(
                            "/test-data/create-offer-body/offer_body-$fileName.json", transformationData),
                    ContentType.APPLICATION_JSON)
            createHeader().forEach { header -> request.addHeader(header) }
            val httpClient = HttpClients.createDefault()
            val response = httpClient.execute(request)
            context.responseBody = EntityUtils.toString(response.entity)
            context.statusCode = response.statusLine.statusCode

            printContextInfo(request)
        }

        When("^calling updateBulkOffer with file name (\\S+) and endpoint (\\S+)$")
        { fileName: String, endpoint: String, table: DataTable ->
            var transformationData: Map<String, String> =
                    table.asMap<String, String>(String::class.java, String::class.java).filterKeys {
                        !it.contains("path")
                    }
            val bulkId = JSONObject(context.responseBody).getString("id")

            val requestGet = HttpGet("$endpoint/v3/offers?bulkId=$bulkId")
            val httpClient = HttpClients.createDefault()
            createHeader().forEach { header -> requestGet.addHeader(header) }
            val responseGet = httpClient.execute(requestGet)
            context.responseBody = EntityUtils.toString(responseGet.entity)

            transformationData = transformationData.toMutableMap()
            val offers = JSONObject(context.responseBody).getJSONArray("content")
            for (i in 0 until offers.length()) {
                transformationData.put("\$.offers[$i].id", "\"".plus(offers.getJSONObject(i).getString("id")).plus("\""))
            }

            val request = HttpPatch("$endpoint/v3/offers/bulk/$bulkId")
            request.entity = StringEntity(
                    CreateOfferRecordFactory.getOfferData(
                            "/test-data/create-offer-body/offer_body-$fileName.json", transformationData),
                    ContentType.APPLICATION_JSON)
            createHeader().forEach { header -> request.addHeader(header) }
            val response = httpClient.execute(request)
            context.responseBody = EntityUtils.toString(response.entity)
            context.statusCode = response.statusLine.statusCode

            printContextInfo(request)
        }

        And("^calling GetOffers with endpoint (\\S+)$") { endpoint: String ->
            val request = HttpGet("$endpoint/v3/offers")
            request.addHeader("accept", "application/json")
            request.addHeader("x-user-email", "<EMAIL>")
            request.addHeader("X-Request-ID", UUID.randomUUID().toString())
            val httpClient = HttpClients.createDefault()
            val response = httpClient.execute(request)
            context.responseBody = EntityUtils.toString(response.entity)
            context.statusCode = response.statusLine.statusCode

            printContextInfo(request)
        }




        And("^calling GetOffers with endpoint (\\S+) and Offer Id appended$") { endpoint: String ->
            val request = HttpGet("$endpoint/v3/offers?${JSONObject(context.responseBody).getString("id")}")
            createHeader().forEach { header -> request.addHeader(header) }
            val httpClient = HttpClients.createDefault()
            val response = httpClient.execute(request)
            context.responseBody = EntityUtils.toString(response.entity)
            context.statusCode = response.statusLine.statusCode

            printContextInfo(request)
        }
        When("^calling GetOffers with endpoint (\\S+) with input parameter (\\S+)$")
        { endpoint: String, inputParameter: String ->
            val request = HttpGet("$endpoint/v3/offers?$inputParameter")
            createHeader().forEach { header -> request.addHeader(header) }
            val httpClient = HttpClients.createDefault()
            val response = httpClient.execute(request)
            context.responseBody = EntityUtils.toString(response.entity)
            context.statusCode = response.statusLine.statusCode
            scenario.write(context.responseBody)
            printContextInfo(request)
        }
        When("^calling GetOffersCounts with endpoint (\\S+) with input parameter (\\S+)$")
        { endpoint: String, inputParameter: String ->
            val request = HttpGet("$endpoint/v3/offers/counts?$inputParameter")
            createHeader().forEach { header -> request.addHeader(header) }
            val httpClient = HttpClients.createDefault()
            val response = httpClient.execute(request)
            context.responseBody = EntityUtils.toString(response.entity)
            context.statusCode = response.statusLine.statusCode
            scenario.write(context.responseBody)
            printContextInfo(request)
        }
        And("^calling GetOffers with endpoint (\\S+) with bulkId$") { endpoint: String ->
            val request = HttpGet("$endpoint/v3/offers?bulkId=${JSONObject(context.responseBody).getString("id")}")
            createHeader().forEach { header -> request.addHeader(header) }
            val httpClient = HttpClients.createDefault()
            val response = httpClient.execute(request)
            context.responseBody = EntityUtils.toString(response.entity)
            context.statusCode = response.statusLine.statusCode
            scenario.write(context.responseBody)
            printContextInfo(request)
        }

        When("^calling createOffer with qualifier Fuel and qualifierFrequency 1 (\\S+)$")
        { endpoint: String, table: DataTable ->
            val transformationData: Map<String, String> =
                    table.asMap<String, String>(String::class.java, String::class.java).filterKeys {
                        !it.contains("path")
                    }
            val request = HttpPost("$endpoint/v3/offers")
            request.entity = StringEntity(
                    CreateOfferRecordFactory.getOfferData(
                            "/test-data/create-offer-body/offer_body_qualifier_fuel.json", transformationData),
                    ContentType.APPLICATION_JSON)
            createHeader().forEach { header -> request.addHeader(header) }
            val httpClient = HttpClients.createDefault()
            val response = httpClient.execute(request)
            context.responseBody = EntityUtils.toString(response.entity)
            context.statusCode = response.statusLine.statusCode

            printContextInfo(request)
        }

        When("^calling GetOffers with endpoint (\\S+) and offerId (\\S+)$") { endpoint: String, inputParameter: String ->
            val request = HttpGet("$endpoint/v3/offers/$inputParameter")
            createHeader().forEach { header -> request.addHeader(header) }
            val httpClient = HttpClients.createDefault()
            val response = httpClient.execute(request)
            context.responseBody = EntityUtils.toString(response.entity)
            context.statusCode = response.statusLine.statusCode

            printContextInfo(request)
        }


        Then("^calling Publish Offer with endpoint (\\S+)$") { endpoint: String ->
            publishOffer(endpoint)
        }

        Then("^calling Republish Offer with endpoint (\\S+)$") { endpoint: String ->
            rePublishOffer(endpoint)
        }

        Then("^calling Disable Offer with offerId with endpoint (\\S+)$") { endpoint: String ->
            disableOffer(endpoint)
        }

        Then("^calling putOffer with offerId with endpoint (\\S+)$") { endpoint: String, table: DataTable ->
            val transformationData: Map<String, String> = table.asMap<String, String>(
                    String::class.java, String::class.java).filterKeys {
                !it.contains("path")
            }
            updateOffer(endpoint, transformationData)
        }

        And("^calling DeleteOffers with endpoint (\\S+) and all OfferIds included$") { endpoint: String ->
            val idsToDelete = mutableListOf<String>()
            val items = JSONObject(context.responseBody).getJSONArray("content")
            val lastIndex = items.length() - 1;
            for (i in 0..lastIndex) {
                val item = items.getJSONObject(i)
                idsToDelete += item.getString("id")
            }
            deleteOffer(endpoint, idsToDelete)
        }

        And("^calling DeleteOffers with endpoint (\\S+) and OfferId included$") { endpoint: String ->
            val idToDelete = mutableListOf<String>()
            idToDelete += JSONObject(context.responseBody).getString("id")
            deleteOffer(endpoint, idToDelete)
        }

        And("^calling DisableOffer with endpoint (\\S+) and OfferId included$") { endpoint: String ->
            val idToDisable = JSONObject(context.responseBody).getString("id")
            disableOffer(endpoint, idToDisable)
        }

        And("^calling EnableOffer with endpoint (\\S+) and OfferId included$") { endpoint: String ->
            val idToEnable = JSONObject(context.responseBody).getString("id")
            enableOffer(endpoint, idToEnable)
        }

        And("^store total count from response$"){
            val totalCount = JSONObject(context.responseBody).getInt("total")
            responseContext.totalCount = totalCount
            scenario.write("Stored total count: $totalCount")
        }

        Then("^verify total count increase$"){
            val newTotalCount = JSONObject(context.responseBody).getInt("total")
            Assert.assertTrue("Verified total increase", newTotalCount > (responseContext.totalCount ?: 0))
        }


    }

    fun printContextInfo(message: HttpMessage) {
        scenario.write("============ Context Info =============")
        if (JSONObject(context.responseBody).has("id")) {
            scenario.write("OfferId: ${JSONObject(context.responseBody).getString("id")}")
        }
        scenario.write("${message.getHeaders("X-Request-ID")[0]}")
        scenario.write("=======================================")
    }

    fun createHeader() = arrayOf<Header>(
            BasicHeader("accept", "application/json"),
            BasicHeader("x-user-email", "<EMAIL>"),
            BasicHeader("X-Request-ID", UUID.randomUUID().toString()))

    private fun createOffer(fileName: String, endpoint: String, transformationData: Map<String, String>) {
        val request = HttpPost("$endpoint/v3/offers")
        request.entity = StringEntity(
                CreateOfferRecordFactory.getOfferData(
                        "/test-data/create-offer-body/offer_body-$fileName.json", transformationData),
                ContentType.APPLICATION_JSON)
        createHeader().forEach { header -> request.addHeader(header) }
        val httpClient = HttpClients.createDefault()
        val response = httpClient.execute(request)
        context.responseBody = EntityUtils.toString(response.entity)
        context.statusCode = response.statusLine.statusCode

        printContextInfo(request)
    }

    private fun publishOffer(endpoint: String) {
        val request = HttpPost("$endpoint/v3/offers/publish")
        val json = JSONObject()
        // TODO this work around is useless!!!!
        json.put("id", JSONObject(context.responseBody).getString("id"))
        request.entity = StringEntity(json.toString(), ContentType.APPLICATION_JSON)
        createHeader().forEach { header -> request.addHeader(header) }
        val httpClient = HttpClients.createDefault()
        val response = httpClient.execute(request)
        response.entity?.let { context.responseBody = EntityUtils.toString(response.entity) }
        context.statusCode = response.statusLine.statusCode
    }

    private fun disableOffer(endpoint: String) {
        val request = HttpPost("$endpoint/v3/offers/disable")
        val json = JSONObject()
        json.put("id", JSONObject(context.responseBody).getString("id"))
        request.entity = StringEntity(json.toString(), ContentType.APPLICATION_JSON)
        createHeader().forEach { header -> request.addHeader(header) }
        val httpClient = HttpClients.createDefault()
        val response = httpClient.execute(request)
        response.entity?.let { context.responseBody = EntityUtils.toString(response.entity) }
        context.statusCode = response.statusLine.statusCode
    }

    private fun updateOffer(endpoint: String, transformationData: Map<String, String>) {
        val offerId = JSONObject(context.responseBody).getString("id")
        val request = HttpPut("$endpoint/v3/offers/$offerId")
        val body = context.responseBody?.let { JsonPathModifyer.modifyJson(it, transformationData) }

        request.entity = StringEntity(body, ContentType.APPLICATION_JSON)
        createHeader().forEach { header -> request.addHeader(header) }
        val httpClient = HttpClients.createDefault()
        val response = httpClient.execute(request)
        response.entity?.let { context.responseBody = EntityUtils.toString(response.entity) }
        context.statusCode = response.statusLine.statusCode
    }

    private fun rePublishOffer(endpoint: String) {
        val request = HttpPost("$endpoint/v3/offers/republish-offers")
        createHeader().forEach { header -> request.addHeader(header) }
        val httpClient = HttpClients.createDefault()
        val response = httpClient.execute(request)
        response.entity?.let { context.responseBody = EntityUtils.toString(response.entity) }
        context.statusCode = response.statusLine.statusCode
    }

    private fun deleteOffer(endpoint: String, idsToDelete: MutableList<String>) {
        val request = HttpPost("$endpoint/v3/offers/delete-multiple")
        request.entity = StringEntity("[\"" + idsToDelete.joinToString("\", \"") + "\"]", ContentType.APPLICATION_JSON)
        createHeader().forEach { header -> request.addHeader(header) }
        val httpClient = HttpClients.createDefault()
        val response = httpClient.execute(request)
        response.entity?.let { context.responseBody = EntityUtils.toString(response.entity) }
        context.statusCode = response.statusLine.statusCode
    }

    private fun disableOffer(endpoint: String, idToDisable: String) {
        val request = HttpPost("$endpoint/v3/offers/disable")
        request.entity = StringEntity("{ \"id\": \"$idToDisable\"}", ContentType.APPLICATION_JSON)
        createHeader().forEach { header -> request.addHeader(header) }
        val httpClient = HttpClients.createDefault()
        val response = httpClient.execute(request)
        response.entity?.let { context.responseBody = EntityUtils.toString(response.entity) }
        context.statusCode = response.statusLine.statusCode
    }

    private fun enableOffer(endpoint: String, idToEnable: String) {
        val request = HttpPost("$endpoint/v3/offers/enable")
        request.entity = StringEntity("{ \"id\": \"$idToEnable\"}", ContentType.APPLICATION_JSON)
        createHeader().forEach { header -> request.addHeader(header) }
        val httpClient = HttpClients.createDefault()
        val response = httpClient.execute(request)
        response.entity?.let { context.responseBody = EntityUtils.toString(response.entity) }
        context.statusCode = response.statusLine.statusCode
    }
}
