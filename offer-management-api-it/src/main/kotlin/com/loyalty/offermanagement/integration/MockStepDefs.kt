package com.loyalty.offermanagement.integration

import com.loyalty.nova.common.test.integration.StepDefs
import com.loyalty.offermanagement.testutils.mocks.AirmilesOfferApiV2Mock
import com.loyalty.offermanagement.testutils.mocks.ContentGenerationServiceMock
import com.loyalty.offermanagement.testutils.mocks.OfferFacetsApiMock
import com.loyalty.offermanagement.testutils.mocks.PromotionApiMock

class MockStepDefs : StepDefs {
    init {
        When("^stabbing AirmilesOfferApiV2Mock for stub (\\S+)$") { stubType: String ->
            AirmilesOfferApiV2Mock().mockFor(AirmilesOfferApiV2Mock.StubType.valueOf(stubType))
        }
        When("^stabbing ContentGenerationServiceMock for stub (\\S+)$") { stubType: String ->
            ContentGenerationServiceMock().mockFor(ContentGenerationServiceMock.StubType.valueOf(stubType))
        }
        When("^stabbing OfferFacetsApiMock for stub (\\S+)$") { stubType: String ->
            OfferFacetsApiMock().mockFor(OfferFacetsApiMock.StubType.valueOf(stubType))
        }
        When("^stabbing PromotionApiMock for stub (\\S+)$") { stubType: String ->
            PromotionApiMock().mockFor(PromotionApiMock.StubType.valueOf(stubType))
        }
    }
}
