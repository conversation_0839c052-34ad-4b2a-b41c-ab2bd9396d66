package com.loyalty.offermanagement.integration

import com.loyalty.nova.common.test.integration.RestCallContext
import com.loyalty.nova.common.test.integration.StepDefs
import org.apache.http.client.methods.HttpGet
import org.apache.http.impl.client.HttpClients
import org.apache.http.util.EntityUtils
import org.springframework.beans.factory.annotation.Autowired

class HealthStepDefs : StepDefs {
    @Autowired
    private lateinit var context: RestCallContext

    init {
        When("^calling health endpoint (\\S+)$") { endpoint: String ->
            val request = HttpGet("$endpoint/health")
            request.addHeader("accept", "application/json")
            val httpClient = HttpClients.createDefault()
            val response = httpClient.execute(request)
            context.responseBody = EntityUtils.toString(response.entity)
            context.statusCode = response.statusLine.statusCode
        }
    }
}
