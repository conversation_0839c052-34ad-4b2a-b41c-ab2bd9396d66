apply plugin: 'kotlin-spring'
apply plugin: 'eclipse'
apply plugin: 'com.adarshr.test-logger'
apply plugin: 'jacoco'

group = 'com.loyalty'
version = "${version}"
sourceCompatibility = 11

compileKotlin {
    kotlinOptions {
        freeCompilerArgs = ["-Xjsr305=strict"]
        jvmTarget = "11"
    }
}
compileTestKotlin {
    kotlinOptions {
        freeCompilerArgs = ["-Xjsr305=strict"]
        jvmTarget = "11"
    }
}

repositories {
    mavenCentral()
    google()
    jcenter()
    maven { url "https://repo.spring.io/milestone" }
    maven { url "https://s3-us-west-2.amazonaws.com/dynamodb-local/release" }
    maven { url 'https://jitpack.io' }
    maven {
        url "https://loyalty.jfrog.io/loyalty/libs-release"
        credentials {
            username = 'things'
            password = 'AP2QQPyfCHZWKhshVbB2v4S79T1TNtoTPu3DW5ChcMCvjRVNX'
        }
    }
}

dependencies {
    implementation(
            "com.github.tomakehurst:wiremock-jre8",
            'ch.qos.logback.contrib:logback-jackson',
            'ch.qos.logback.contrib:logback-json-classic',
            'ch.qos.logback:logback-classic',
            'com.amazonaws:aws-java-sdk-core',
            'com.amazonaws:aws-java-sdk-dynamodb',
            'com.amazonaws:aws-java-sdk-kinesis',
            'com.amazonaws:aws-lambda-java-core',
            'com.amazonaws:aws-lambda-java-events',
            'com.amazonaws:DynamoDBLocal',
            'com.amazonaws:aws-java-sdk-secretsmanager',
            'com.amazonaws:aws-java-sdk-lambda',
            'com.fasterxml.jackson.core:jackson-databind',
            'com.fasterxml.jackson.module:jackson-module-kotlin',
            'com.loyalty.nova:nova-common-test',
            'io.cucumber:cucumber-java8',
            'io.cucumber:cucumber-junit',
            'io.cucumber:cucumber-spring',
            'io.kotlintest:kotlintest-extensions-spring',
            'kg.apc:jmeter-plugins-casutg',
            'org.apache.jmeter:ApacheJMeter_core',
            'org.apache.jmeter:ApacheJMeter_http',
            'org.hamcrest:java-hamcrest',
            'org.jetbrains.kotlin:kotlin-reflect',
            'org.jetbrains.kotlin:kotlin-stdlib-jdk8',
            'org.jetbrains.kotlin:kotlin-test',
            'org.junit.jupiter:junit-jupiter-api',
            'org.junit.jupiter:junit-jupiter-api',
            'org.junit.jupiter:junit-jupiter-api',
            'org.junit.jupiter:junit-jupiter-params',
            'org.junit.jupiter:junit-jupiter-params',
            'org.mockito:mockito-core',
            'org.mockito:mockito-core',
            'org.mockito:mockito-junit-jupiter',
            'org.mockito:mockito-junit-jupiter',
            'org.reflections:reflections',
            'org.springframework.boot:spring-boot-starter-data-rest',
            'org.springframework.boot:spring-boot-starter-test',
            'org.springframework.cloud:spring-cloud-function-adapter-aws',
            'org.springframework.cloud:spring-cloud-starter-function-web',
            'org.springframework.boot:spring-boot-starter-web',
            'org.springframework.cloud:spring-cloud-contract-wiremock',
            'org.springframework.boot:spring-boot-starter-data-jpa',
            'org.springframework.cloud:spring-cloud-function-kotlin',
            'org.springframework:spring-context',
            'org.skyscreamer:jsonassert'
    )

    implementation 'com.github.tomakehurst:wiremock-standalone:2.23.2'

    runtimeOnly(
            'org.junit.jupiter:junit-jupiter-engine',
            'com.almworks.sqlite4java:sqlite4java'
    )

    implementation project(':offer-management-api-test-utils')
    implementation project(':offer-management-api-service')

    compile('com.almworks.sqlite4java:libsqlite4java-osx',
            'com.almworks.sqlite4java:libsqlite4java-linux-amd64',
            'com.almworks.sqlite4java:libsqlite4java-linux-i386',
            'com.almworks.sqlite4java:sqlite4java-win32-x64',
            'com.almworks.sqlite4java:sqlite4java-win32-x86'
    )
}

/* Copy the native files */
task copyNativeDeps(type: Copy) {
    from(configurations.compile + configurations.testCompile) {
        include "*.dylib"
        include "*.so"
        include "*.dll"
    }
    into 'build/libs'
}

test {
    useJUnitPlatform {
        includeEngines 'junit-jupiter'
        excludeEngines 'junit-vintage'
    }

    jacoco {
        destinationFile = file("${buildDir}/jacoco/test.exec")
    }

    systemProperty 'java.library.path', 'build/libs'

    environment 'AWS_ACCESS_KEY_ID', 'applmgr'
    environment 'AWS_DEFAULT_REGION', 'us-east-1'
    environment 'AWS_SECRET_ACCESS_KEY', 'applmgr'
    environment 'NOVA_BUS_ENDPOINT', 'http://localhost:8000'
    environment 'NOVA_BUS_TABLE_NAME', 'local-nova-event-bus-offer-event-store'
    environment 'NOVA_BUS_REGION', 'us-east-1'
}

configurations {
    cucumberRuntime {
        extendsFrom runtime
    }
}

task integrationTest() {
    dependsOn assemble, compileTestJava
    doLast {
        javaexec {
            main = "cucumber.api.cli.Main"
            classpath = configurations.cucumberRuntime + sourceSets.main.output + sourceSets.main.runtimeClasspath
            args = ['--plugin', 'pretty', '--monochrome',
                    '--plugin', 'html:build/reports/cucumber/cucumber-html-report',
                    '--plugin', 'json:build/reports/cucumber/cucumber-json-report.json',
                    '--plugin', 'html:build/reports/cucumber/cucumber-html-report-proxy',
                    '--plugin', 'json:build/reports/cucumber/cucumber-json-report-proxy.json',
                    '--tags', cucumberTag,
                    '--glue', 'com.loyalty.offermanagement.integration',
                    '--glue', 'com.loyalty.nova.regionaloffers.integration',
                    '--glue', 'com.loyalty.nova.common.test.integration',
                    '--glue', 'com.loyalty.nova.common.test.performance',
                    'src/main/resources/features']
            jvmArgs = ['-ea', '-Djava.library.path=build/libs', '-Djmeter.home=src/main/resources/jmeter-home', '-Duser.timezone=America/Toronto']
        }
    }
}

task publish {
    // release artifact
}

compileKotlin.dependsOn copyNativeDeps
