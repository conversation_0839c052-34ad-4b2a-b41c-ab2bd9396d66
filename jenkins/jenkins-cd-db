def blue = "#42b3f4"
def good = "#3dd62f"
def danger = "#f45641"
def warning = "#ffd344"

config = null
jenkinsUtils = null

void importFunctions() {
    jenkinsUtils = load "jenkins/JenkinsUtils.groovy"
}

pipeline {
    agent none
    stages {
        stage('Deploying to Dev.') {
            agent {
                docker {
                    image '277983268692.dkr.ecr.us-east-1.amazonaws.com/ubuntu-22'
                }
            }
            steps {
                println "Deploying to Dev."
                println "BUILD_VERSION: ${params.BUILD_VERSION}"
                importFunctions()
                withCredentials([[$class: "AmazonWebServicesCredentialsBinding", credentialsId: "dev-amrpwl-aws-deployer"]]) {
                    script {
                        jenkinsUtils.deployResourceStack('dev', params.BUILD_VERSION)
                    }
                }
            }
            post {
                success {
                    println 'success! Deployed to dev.'
                }
                failure {
                    println "failed to deploy to dev."
                }
                aborted {
                    println "job aborted. Did not deploy to dev."
                }
            }
        }

        stage('Deploy to INT?') {
            agent none
            steps {
                input(message: "Do you want to deploy version ${params.BUILD_VERSION} to INT?")
            }
            post {
                success {
                    echo 'Attempting to deploy to int'
                }
                aborted {
                    echo "Aborting attempt to deploy to int"
                }
            }
        }

        stage('Deploying to INT') {
            agent {
                docker {
                    image '277983268692.dkr.ecr.us-east-1.amazonaws.com/ubuntu-22'
                }
            }
            steps {
                println "Deploying to INT"
                println "BUILD_VERSION: ${params.BUILD_VERSION}"
                importFunctions()
                withCredentials([[$class: "AmazonWebServicesCredentialsBinding", credentialsId: "nonprod-amrpwl-aws-deployer"]]) {
                    script {
                        jenkinsUtils.deployResourceStack('int', params.BUILD_VERSION)
                    }
                }
            }
            post {
                success {
                    println 'success! Deployed to int.'
                }
                failure {
                    println "failed to deploy to int."
                }
                aborted {
                    println "job aborted. Did not deploy to int."
                }
            }
        }

        stage('Deploy to UAT?') {
            agent none
            steps {
                input(message: "Do you want to deploy version ${params.BUILD_VERSION} to UAT?")
            }
            post {
                success {
                    echo 'Attempting to deploy to uat'
                }
                aborted {
                    echo "Aborting attempt to deploy to uat"
                }
            }
        }

        stage('Deploying to UAT') {
            agent {
                docker {
                    image '277983268692.dkr.ecr.us-east-1.amazonaws.com/ubuntu-22'
                }
            }
            steps {
                println "Deploying to UAT"
                println "BUILD_VERSION: ${params.BUILD_VERSION}"
                importFunctions()
                withCredentials([[$class: "AmazonWebServicesCredentialsBinding", credentialsId: "uat-amrpwl-aws-deployer"]]) {
                    script {
                        jenkinsUtils.deployResourceStack('uat', params.BUILD_VERSION)
                    }
                }
            }
            post {
                success {
                    println 'success! Deployed to UAT.'
                }
                failure {
                    println "failed to deploy to uat."
                }
                aborted {
                    println "job aborted. Did not deploy to uat."
                }
            }
        }
    }
}
