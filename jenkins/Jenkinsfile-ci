@Library('jenkins-shared-lib-v2') _

def good = "#3dd62f"
def danger = "#f45641"
def warning = "#ffd344"

config = null
jenkinsUtils = null

/**
 * Checks if the current commit is from jenkins
 * @return true when the current commit is from jenkins
 */
boolean isJenkinsCommit() {
    def commitEmail = sh(script: "git log -1 --pretty=format:'%ae'", returnStdout: true)?.trim()
    return (commitEmail == "${env.GIT_COMMITTER_EMAIL}")
}

/**
 * Check if the current branch is master
 * @return true when the current branch is master
 */
boolean isMaster() {
    def branchFullName = "${env.GIT_BRANCH}"
    def branchList = branchFullName.tokenize('/')
    def branchName = branchList.get(branchList.size() - 1)
    return branchName == 'master'
}

/**
 * Authenticate session for pushing into ECR
 */
void authECR() {
    println "Authenticate to push docker image"
    ansiColor('xterm') {
        sh '''
      #!/bin/bash
      # configure system-wide environment variables and aliases needed for nvm and npm
      source /etc/profile
      export AWS_DEFAULT_REGION=us-east-1
      # Login to private container repository
      eval $(aws ecr get-login --no-include-email)
    '''
    }
}

pipeline {
    agent {
        docker {
          image '277983268692.dkr.ecr.us-east-1.amazonaws.com/ubuntu-22'
          args '--volume=/var/run/docker.sock:/var/run/docker.sock -u jenkins:docker'
        }
    }
    stages {
        stage("Repository Information") {
            steps {
                println "Repository Information"
                script {
                    config = readProperties(defaults: config, file: 'gradle.properties')
                    jenkinsUtils = load "jenkins/JenkinsUtils.groovy"
                    env.RELEASE_ARTIFACTS = (!isJenkinsCommit() && isMaster() && true)
                    env.SKIP_SNAPSHOT_RELEASE = (!isJenkinsCommit() && isMaster() && true)
                    def (versionNumber, snapshot) = config.version.tokenize('-')
                    config.versionNumber = versionNumber
                    config.repoName = jenkinsUtils.getRepoName()
                    jenkinsUtils.getGradleProperties()
                }
            }
        }

		stage("Build") {
			steps {
				println "Build"
				//sh(script: './gradlew build')
				withCredentials([[
										 $class: "AmazonWebServicesCredentialsBinding",
										 credentialsId: "nonprod-amrpwl-aws-deployer"
								 ]]) {
					sh(script: './jenkins/validate-cfn')
				}
			script{
			jfrog.credentials() {
                                    sh """
                                        ./gradlew build -DjfrogUser="${ARTIFACTORY_USERNAME}" -DjfrogPassword="${ARTIFACTORY_PASSWORD}"
                                    """
                                }
			}
			}
			post {
				always {
					junit '**/build/test-results/**/*.xml'
					publishHTML(target: [
							allowMissing: true, alwaysLinkToLastBuild: false, keepAll: true,
							reportDir   : 'build/reports/tests/test/',
							reportFiles : 'index.html',
							reportTitles: 'Unit Tests',
							reportName  : 'Unit Tests'])

					sh './gradlew jacocoTestReport'
					publishHTML(target: [
							allowMissing: true, alwaysLinkToLastBuild: false, keepAll: true,
							reportDir   : 'offer-management-api-service/build/reports/jacoco/',
							reportFiles : 'index.html',
							reportTitles: 'Service Module Code Coverage',
							reportName  : 'Service Module Code Coverage'])

					// Stashing for next step
					stash includes: "offer-management-api-service/build/**", name: 'build'
				}
			}
		}
        stage("Checkmarx Scan") {
                          when { environment name: 'RELEASE_ARTIFACTS', value: 'true' }
                            steps {
                              step([$class: 'CxScanBuilder', comment: '', credentialsId: '', excludeFolders: '', excludeOpenSourceFolders: '',
                                exclusionsSetting: 'job', failBuildOnNewResults: "${!isMaster()}",
                                filterPattern: '''!**/_cvs/**/*, !**/.svn/**/*,   !**/.hg/**/*,   !**/.git/**/*,  !**/.bzr/**/*, !**/bin/**/*, !**/node_modules/**/*,
                                !**/build/**/*, !**/target/**/*, !**/.gradle/**/*, !**/obj/**/*,  !**/backup/**/*, !**/.idea/**/*, !**/*.DS_Store, !**/*.ipr, !**/*.iws,
                                !**/*.bak,     !**/*.tmp,       !**/*.aac,      !**/*.aif,      !**/*.iff,     !**/*.m3u, !**/*.mid, !**/*.mp3,
                                !**/*.mpa,     !**/*.ra,        !**/*.wav,      !**/*.wma,      !**/*.3g2,     !**/*.3gp, !**/*.asf, !**/*.asx,
                                !**/*.avi,     !**/*.flv,       !**/*.mov,      !**/*.mp4,      !**/*.mpg,     !**/*.rm,  !**/*.swf, !**/*.vob,
                                !**/*.wmv,     !**/*.bmp,       !**/*.gif,      !**/*.jpg,      !**/*.png,     !**/*.psd, !**/*.tif, !**/*.swf,
                                !**/*.jar,     !**/*.zip,       !**/*.rar,      !**/*.exe,      !**/*.dll,     !**/*.pdb, !**/*.7z,  !**/*.gz,
                                !**/*.tar.gz,  !**/*.tar,       !**/*.gz,       !**/*.ahtm,     !**/*.ahtml,   !**/*.fhtml, !**/*.hdm,
                                !**/*.hdml,    !**/*.hsql,      !**/*.ht,       !**/*.hta,      !**/*.htc,     !**/*.htd, !**/*.war, !**/*.ear,
                                !**/*.htmls,   !**/*.ihtml,     !**/*.mht,      !**/*.mhtm,     !**/*.mhtml,   !**/*.ssi, !**/*.stm,
                                !**/*.stml,    !**/*.ttml,      !**/*.txn,      !**/*.xhtm,     !**/*.xhtml,   !**/*.class, !**/*.iml,
                                !Checkmarx/Reports/*.*''', fullScanCycle: 50, fullScansScheduled: true, groupId: '19431a46-4d94-4130-9a78-b5e2833244c6',
                                includeOpenSourceFolders: '', osaArchiveIncludePatterns: '*.zip, *.war, *.ear, *.tgz',
                                osaInstallBeforeScan: false, password: '{AQAAABAAAAAQZhCBOvS+ym3pI038ChU4e73PzZVrLLHSeP9Ej1IH8JU=}',
                                preset: '36', projectName: "${config.repoName}", sastEnabled: true,
                                serverUrl: 'https://checkmarx.loyalty.com', sourceEncoding: '1', username: ''])
                          }
			}
			stage("Snapshot Release") {
                when { environment name: 'SKIP_SNAPSHOT_RELEASE', value: 'true' }
                steps {
                    println "Snapshot Release"
                    authECR()
                    sh(script: './gradlew dockerPush')
                }
            }

            /*stage("Clean up Infra") {
                           when { environment name: 'SKIP_SNAPSHOT_RELEASE', value: 'true' }
                           steps {
                               println "Clean up Infra before deployment"
                               withCredentials([[
                                                           $class       : "AmazonWebServicesCredentialsBinding",
                                                           credentialsId: "nonprod-amrpwl-aws-deployer"
                                                   ]]) {
                                   script {
                                       jenkinsUtils.deleteStack()
                                       jenkinsUtils.deleteResourceStack()
                                   }
                               }
                           }
                       }

			stage("Sole Deploy") {
                            when { environment name: 'SKIP_SNAPSHOT_RELEASE', value: 'true' }
                            steps {
                                println "Sole Deploy"
                                withCredentials([[
                                                         $class       : "AmazonWebServicesCredentialsBinding",
                                                         credentialsId: "nonprod-amrpwl-aws-deployer"
                                                 ]]) {
                                    script {
                                        jenkinsUtils.deployResourceStack('sole', config.version)
                                        jenkinsUtils.ecsDeploy('sole', config.version)
                                    }
                                }
                            }
            }*/

			//stage("Sole Test") {

                            //when { environment name: 'SKIP_SNAPSHOT_RELEASE', value: 'true' }
                           // steps {
                                //println "Sole Test"
                                //withCredentials([[
                                                         //$class       : "AmazonWebServicesCredentialsBinding",
                                                         //credentialsId: "nonprod-amrpwl-aws-deployer"
                                                // ]]) {
                                    // sh(script: './gradlew startWireMock')
                                    //sh(script: './gradlew integrationTest -PcucumberTag=@sole')
                                //}
                           // }
                            //post {
                                //always {
                                    //cucumber buildStatus: null,
                                            //fileIncludePattern: '**/build/reports/cucumber/cucumber-json-report.json',
                                            //trendsLimit: 10
                                //}
                           // }
			//}

			stage("OWASP Check") {
                    stages {
                        stage("OWASP  Dependency Check") {
                            when { environment name: 'SKIP_SNAPSHOT_RELEASE', value: 'true' }
                            steps {
                                println "OWASP  Dependency Check"
                                // sh(script: './gradlew dependencyCheckAggregate')
                            }
                            post {
                                always {
                                    publishHTML(target: [
                                            allowMissing: true, alwaysLinkToLastBuild: false, keepAll: true,
                                            reportDir   : 'build/reports',
                                            reportFiles : 'dependency-check-report.html',
                                            reportTitles: 'OWASP dependency check report',
                                            reportName  : 'OWASP dependency check report'])
                                }
                            }
                        }
                    }
            }

            stage("Release") {
            when { environment name: 'SKIP_SNAPSHOT_RELEASE', value: 'true' }
            steps {
                println "Tag Release"
                git(
                        url: "**************:AirMilesLoyaltyInc/offer-management-api.git",
                        branch: "master",
                        credentialsId: "jenkins-ssh-key"
                )
                authECR()
                sshagent(credentials: ['jenkins-ssh-key'], ignoreMissing: false) {
                    sh(script: './gradlew release -Prelease.useAutomaticVersion=true')
                }
            }
            post {
                success {
                    echo 'success! Lets start up the deployment job.'
                    build job: 'Deployment/offer-management-api', parameters: [ gitParameter(name: 'BUILD_VERSION', value: "${config.versionNumber}") ], wait: false
                }
                failure {
                    echo "failure occurred."
                }
                aborted {
                    echo "job aborted."
                }
            }
        }
    }

    post {
        always {
            script {
                gitUtils.reportPRStatus()
            }
        }
    }
}