def blue = "#42b3f4"
def good = "#3dd62f"
def danger = "#f45641"
def warning = "#ffd344"

config = null
jenkinsUtils = null

pipeline {
    agent none

    stages {
        stage('Deploying to Production') {
            agent {
                docker {
                      image '277983268692.dkr.ecr.us-east-1.amazonaws.com/ubuntu-22'
                }
            }
            steps {
                println "Deploying to Production"
                println "BUILD_VERSION: ${params.BUILD_VERSION}"
                withCredentials([[$class: "AmazonWebServicesCredentialsBinding", credentialsId: "prod-amrpwl-aws-deployer"]]) {
                    script {
                        jenkinsUtils = load "jenkins/JenkinsUtils.groovy"
                        jenkinsUtils.deployResourceStack('prod', params.BUILD_VERSION)
                    }
                }
            }
            post {
                success {
                    script {
                        echo 'success! Deployed to production'
                    }
                }
                failure {
                    script {
                        echo "failed to deploy to production"
                    }
                }
                aborted {
                    script {
                        echo "job aborted. Did not deploy to production"
                    }
                }
            }
        }
    }
}
