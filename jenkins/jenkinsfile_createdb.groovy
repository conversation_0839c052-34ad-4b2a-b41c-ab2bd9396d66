@Library('jenkins-shared-lib-v2') _

def ENVIRONMENT = params.ENVIRONMENT
def DBPassword = params.DB_ROOT_PASSWORD

pipeline {
  agent none
  stages {
    stage('Create Change Set') {
	    agent { label 'aws-ec2'}
      steps {
        script {
// Update with the DB stack name
		  def CF_STACK_NAME = ENVIRONMENT + "-offer-management-api-resources"
//		  def CF_STACK_NAME = ENVIRONMENT + "-offer-management-api-sre" #for testing only
// Update with DB cluster template
		  def TEMPLATE_FILE = "./cfn/templates/resources.yaml"

// Update with the AWS account name alias
          awsUtils.withDeployer(ENVIRONMENT, 'amrpwl') {
          sh """
			./cfn/scripts/cfn-create-change-set.sh $ENVIRONMENT $CF_STACK_NAME $TEMPLATE_FILE
		  """
          }
        }
      }
      post {
        cleanup {
          deleteDir()
          dir("${workspace}@tmp") {
            deleteDir()
          }
          dir("${workspace}@script") {
            deleteDir()
          }
        }    
      }        
    } 
	
	stage("Get Approval to Deploy") {
      agent none

      steps {
        input(
          message: "Would you like to deploy the changes above?"
        )
      }
    }

    stage('create db') {
      agent { label 'aws-ec2' }
      steps {
        script {
// Update with the AWS account name alias
		  awsUtils.withDeployer(ENVIRONMENT, 'amrpwl'){
		    sh """
		      ./cfn/scripts/create-db.sh ${ENVIRONMENT}
		    """
		  }
        }
      }
      post {
        cleanup {
          deleteDir()
          dir("${workspace}@tmp") {
            deleteDir()
          }
          dir("${workspace}@script") {
            deleteDir()
          }
        }    
      }   
    }
  }
}