/**
 * Hosts the common utility functions for jenkins pipelines
 */

/**
 * Send slack message to channel given in the config file - not used
 * @param title Title of slack message
 * @param msg Message
 * @param color Colour code
 */
void sendSlackMessage(String title, String msg, String color) {
    if ("${config}" == null || "${config.SLACK_WEBHOOK_URL}" == null) {
        println "No SLACK_WEBHOOK_URL mentioned"
        return
    }
    echo "Sending Slack Message"
    def header = env.JOB_NAME.replace('/', ': ')
    def payload = JsonOutput.toJson([
            attachments: [[
                                  fallback: header,
                                  text    : header,
                                  color   : color,
                                  fields  : [[
                                                     title: "#${env.BUILD_NUMBER} - ${title}",
                                                     value: msg
                                             ]]
                          ]]
    ])
    sh "curl -X POST --data-urlencode \'payload=${payload}\' ${config.SLACK_WEBHOOK_URL}"
    echo "Slack Message Sent"
}

/**
 * Get the CFN stack name
 * @param env [environment name]
 * @return [CFN stack name wrt environment name]
 */
String getCFNStackName(String env) {
    return "${env}-offer-management-api"
}

/**
 * Deploy application in ECS
 * @param env [env name]
 * @param version [version number]
 */
void ecsDeploy(String env, String version) {
    def stackName = getCFNStackName(env) + "-service"
    echo "stackname: ${stackName}"
    stackName = stackName.substring(0, 31)
    echo "stackname substring: ${stackName}"
    updateFileWithBuildVersion(env, 'build-id', version)
    ansiColor('xterm') {
        sh """
      #!/bin/bash
      # configure system-wide environment variables and aliases needed for nvm and npm
      source /etc/profile
      export AWS_DEFAULT_REGION=us-east-1
      # use cfn-flip to convert yml file
      pip3 install cfn-flip==1.3.0 --user
      cfn-flip cfn/templates/service.yaml cfn/templates/service.json
      ecs-service deploy ${stackName} ${version} \
        cfn/templates/service.json cfn/${env}.params.json \
        -t cfn/${env}.tags.json \
        -r us-east-1
      rm cfn/templates/service.json
    """
    }
}

void deleteStack() {
    def stackName = getCFNStackName('sole') + "-service"
    stackName = stackName.substring(0, 31)
    sh """
    if aws cloudformation describe-stacks --stack-name ${stackName} --region us-east-1 > /dev/null 2>&1; then
        aws cloudformation delete-stack --stack-name ${stackName} --region us-east-1
        aws cloudformation wait stack-delete-complete --stack-name ${stackName} --region us-east-1
    else
        echo "Stack ${stackName} does not exist. Skipping deletion process."
    fi
    """

}

void deleteResourceStack() {
    def stackName = getCFNStackName('sole') + "-resources"
    sh """
    if aws cloudformation describe-stacks --stack-name ${stackName} --region us-east-1 > /dev/null 2>&1; then
        aws cloudformation update-termination-protection --no-enable-termination-protection --stack-name ${stackName} --region us-east-1
        aws cloudformation delete-stack --stack-name ${stackName} --region us-east-1
        aws cloudformation wait stack-delete-complete --stack-name ${stackName} --region us-east-1
    else
        echo "Stack ${stackName} does not exist. Skipping deletion process."
    fi
    """
}

void deployResourceStack(String env, String version) {
    echo "Deploying Resource Stack ${env}"
    def scheduler = (env == "prod") ? "false" : "false"
    def stackName = "${getCFNStackName(env)}-resources"
    def tagsFromFile = paramsFromKeyValuePairsFromFile("cfn/${env}.tags.json")
    def tags = "--tags ${tagsFromFile} build-id=${version}"
    def parameterOverrides = paramsFromFile("cfn/db.${env}.params.json")
    def args = "deploy --stack-name ${stackName} --region us-east-1 --template-file cfn/templates/resources.yaml --parameter-overrides ${parameterOverrides} --capabilities CAPABILITY_IAM --no-fail-on-empty-changeset"
    sh "aws cloudformation ${args} ${tags}"
    sh "aws cloudformation update-termination-protection --enable-termination-protection --stack-name ${stackName} --region us-east-1"
    sh "aws cloudformation detect-stack-drift --stack-name ${stackName} --region us-east-1"
}


/**
 * Read json file and return as String required by aws cli
 * @param filename [Name of the file]
 * @param [ 'ParameterKey'   [Key ]
 * @param 'ParameterValue'] [Value]
 * @return [String of key values pairs]
 */
def paramsFromFile(String filename, keyPair = ['ParameterKey', 'ParameterValue']) {
    assert keyPair.size() == 2

    def paramsJson = readJSON(file: filename)

    paramsJson.collect { item ->
        keyPair.collect { key ->
            item.get(key)
        }.join('=')
    }.join(' ')

}

/**
 * Get params from file
 * JSON file format should be:
 *      {
 *          "tag1":"value1,
 *          "tag2":"value2",
 *          ...
 *          "tagN":"valueN"
 *      }
 * @param filename
 * @return String result from JSON file in format: " tag1=value1 tag2=value2 ... tagN=valueN"
 */
String paramsFromKeyValuePairsFromFile(String filename) {
    echo "in paramsFromKeyValuePairsFromFile ${filename}"
    def paramsJson = readJSON(file: filename)
    echo "read json: ${paramsJson}"
    paramsJson.collect {
        item -> "${item}"
    }.join(" ")
}

/**
 * Update the tags json file with specified key and value.
 * Using for build-id (key) : version (value)
 * @param env
 * @param key
 * @param value
 */
void updateFileWithBuildVersion(String env, String key, String value) {
    def paramsJson = readJSON(file: "cfn/${env}.tags.json")
    echo "ParamsJson: ${paramsJson}"
    paramsJson[key] = value
    echo "paramsjson2: ${paramsJson}"
    def writejson = writeJSON(file: "cfn/${env}.tags.json", json: paramsJson)
    echo "writejson: ${writejson}"

    def tagsFromFile = paramsFromKeyValuePairsFromFile("cfn/${env}.tags.json")
    echo "Tags being used for ecs-service deployment: ${tagsFromFile}"
}

/**
 * get the name of the repository
 * @return [Name of the repository]
 */
String getRepoName() {
    return scm.getUserRemoteConfigs()[0].getUrl().tokenize('/').last().split("\\.")[0]
}

/**
 * Read gradle properties from settings.gradle and gradle.properties
 */
void getGradleProperties() {
    gradleProperties = readProperties(file: 'settings.gradle')
    gradleProperties = readProperties(defaults: gradleProperties, file: 'gradle.properties')
}

/**
 * Get artifact from jfrog
 * @param location location in jfrog
 * @param target target folder
 */
void copyArtifactFromJfrog(String location, String target) {
    rtDownload(
            serverId: "jfrog-artifactory",
            spec:
                    """{
        "files": [{
            "pattern": "libs-release-local/${location}",
            "target": "${target}/"
          }]
      }"""
    )
}

return this
