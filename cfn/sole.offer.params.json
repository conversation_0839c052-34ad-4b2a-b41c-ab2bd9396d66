[{"ParameterKey": "DBName", "ParameterValue": "offermanagement"}, {"ParameterKey": "DBUser", "ParameterValue": "db<PERSON><PERSON>"}, {"ParameterKey": "DBInstanceClass", "ParameterValue": "db.r5.large"}, {"ParameterKey": "DBType", "ParameterValue": "postgres"}, {"ParameterKey": "DBEngineVersion", "ParameterValue": "12.9"}, {"ParameterKey": "DBPort", "ParameterValue": "5432"}, {"ParameterKey": "Environment", "ParameterValue": "sole"}, {"ParameterKey": "EnvironmentType", "ParameterValue": "nonprod"}, {"ParameterKey": "Project", "ParameterValue": "offers"}, {"ParameterKey": "Team", "ParameterValue": "Things"}, {"ParameterKey": "Component", "ParameterValue": "offer-management-api"}, {"ParameterKey": "VpcId", "ParameterValue": "vpc-033de5f52e0ff993b"}, {"ParameterKey": "SubnetId1", "ParameterValue": "subnet-018b95ae4773c1989"}, {"ParameterKey": "SubnetId2", "ParameterValue": "subnet-0a2ef31eb3a04e1af"}, {"ParameterKey": "SubnetId3", "ParameterValue": "subnet-0e6f96cd597193c67"}, {"ParameterKey": "KMSKeyName", "ParameterValue": "amrpwl-nonprod-database"}, {"ParameterKey": "CIDRIpAddress", "ParameterValue": "**********/32"}, {"ParameterKey": "NetworkStackName", "ParameterValue": "AMRPWL-Dev"}, {"ParameterKey": "PagerDutyURLAPI", "ParameterValue": "https://events.pagerduty.com/integration/d132264d93f747baa79ead1a770f1935/enqueue"}, {"ParameterKey": "PagerDutyURLDB", "ParameterValue": "https://events.pagerduty.com/integration/d132264d93f747baa79ead1a770f1935/enqueue"}, {"ParameterKey": "CpuUtilizationThreshold", "ParameterValue": "90"}]