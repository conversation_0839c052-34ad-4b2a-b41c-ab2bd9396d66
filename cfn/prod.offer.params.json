[{"ParameterKey": "DBName", "ParameterValue": "offermanagement"}, {"ParameterKey": "DBUser", "ParameterValue": "db<PERSON><PERSON>"}, {"ParameterKey": "DBInstanceClass", "ParameterValue": "db.r5.large"}, {"ParameterKey": "DBType", "ParameterValue": "postgres"}, {"ParameterKey": "DBEngineVersion", "ParameterValue": "12.8"}, {"ParameterKey": "DBPort", "ParameterValue": "5432"}, {"ParameterKey": "Environment", "ParameterValue": "prod"}, {"ParameterKey": "EnvironmentType", "ParameterValue": "prod"}, {"ParameterKey": "Project", "ParameterValue": "offers"}, {"ParameterKey": "Team", "ParameterValue": "Things"}, {"ParameterKey": "Component", "ParameterValue": "offer-management-api"}, {"ParameterKey": "VpcId", "ParameterValue": "vpc-09aa1ade9bdba1446"}, {"ParameterKey": "SubnetId1", "ParameterValue": "subnet-0ede5927e64910695"}, {"ParameterKey": "SubnetId2", "ParameterValue": "subnet-082e28b71bcc164de"}, {"ParameterKey": "SubnetId3", "ParameterValue": "subnet-0372b7fb9669cca83"}, {"ParameterKey": "KMSKeyName", "ParameterValue": "amrpwl-prod-database"}, {"ParameterKey": "CIDRIpAddress", "ParameterValue": "**********/32"}, {"ParameterKey": "NetworkStackName", "ParameterValue": "AMRPWL-Prod"}, {"ParameterKey": "PagerDutyURLAPI", "ParameterValue": "https://events.pagerduty.com/integration/ba9631257b70400dbe50ad2c5a60bdbe/enqueue"}, {"ParameterKey": "PagerDutyURLDB", "ParameterValue": "https://events.pagerduty.com/integration/174a7219edf0410eb0453b7bb4b41e22/enqueue"}, {"ParameterKey": "CpuUtilizationThreshold", "ParameterValue": "90"}]