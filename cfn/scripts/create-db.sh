#!/bin/bash

# Update with the proper region
export AWS_DEFAULT_REGION=us-east-1
set -e

if [[ "$#" -ne 1 ]]; then
    echo "Illegal number of parameters"
    exit 42
fi

if ! [[ $1 =~ dev|int|uat|sole|prod ]]; then
    echo "usage: jenkins-ci.sh dev|int|uat|sole|prod"
    exit 69
fi

if [[ -z "${DB_ROOT_PASSWORD}" ]]; then
    echo "You must set the DB_ROOT_PASSWORD environment variable to set for the new db."
    exit 42
fi

AWS_ENV=$1
# Update with the DB stack name
STACK_NAME=${AWS_ENV}-offer-management-api-resources
#STACK_NAME=${AWS_ENV}-offer-management-api-sre #for testing only

# Add root db password to parameter file in-place.
sed "s/\[/[{\"ParameterKey\": \"DBPassword\", \"ParameterValue\": \"${DB_ROOT_PASSWORD}\"},/" cfn/db.${AWS_ENV}.params.json > params.json

TAGS=$(awk -F\" '/"/ { print "Key=" $2 ",Value=" $4 }' cfn/${AWS_ENV}.tags.json | tr '\n' ' ')
echo TAG: ${TAGS}

if [[ ! -z "${SNAPSHOT_IDENTIFIER}" ]]; then
  echo "Loading DB from Snapshot ${SNAPSHOT_IDENTIFIER}"
  sed "s/\[/[{\"ParameterKey\": \"SnapshotIdentifier\", \"ParameterValue\": \"${SNAPSHOT_IDENTIFIER}\"},/" params.json > temp.params.json
  mv temp.params.json params.json
fi

if ! aws cloudformation describe-stacks --stack-name ${STACK_NAME} ; then
    echo "creating db stack"
    aws cloudformation create-stack \
        --stack-name ${STACK_NAME} --region ${AWS_DEFAULT_REGION}\
        --capabilities CAPABILITY_NAMED_IAM \
        --template-body file://cfn/templates/resources.yaml \
        --tags ${TAGS} \
        --parameters file://params.json
else
    echo "updating db stack"
    aws cloudformation update-stack \
        --stack-name ${STACK_NAME} --region ${AWS_DEFAULT_REGION}\
        --capabilities CAPABILITY_NAMED_IAM \
        --template-body file://cfn/templates/resources.yaml \
        --tags ${TAGS} \
        --parameters file://params.json
fi

cfn_status=`aws cloudformation describe-stacks --stack-name ${STACK_NAME} --region ${AWS_DEFAULT_REGION}  --query 'Stacks[0].StackStatus' --output text`
 echo "StackStatus: ${cfn_status}"

status_list="CREATE_COMPLETE UPDATE_COMPLETE ROLLBACK_COMPLETE UPDATE_ROLLBACK_COMPLETE"
while [[ "$status_list" != *"${cfn_status}"* ]] ; do
  echo "Cloudformation is still being created / updated. Sleeping for 120 seconds..."
  sleep 120

  cfn_status=`aws cloudformation describe-stacks --stack-name ${STACK_NAME} --region ${AWS_DEFAULT_REGION}  --query 'Stacks[0].StackStatus' --output text`
  echo "status is ${cfn_status}"
done

if [[ ${cfn_status} == "ROLLBACK_COMPLETE" || ${cfn_status} == "UPDATE_ROLLBACK_COMPLETE" ]] ; then
  echo "Create / update failed, please check cloufformation for details on aws console..."
  exit 1
fi