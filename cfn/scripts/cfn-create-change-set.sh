#!/bin/bash

# Update with the proper region
export AWS_DEFAULT_REGION=us-east-1

environment=$1
stack_name=$2
template_file=$3

if [[ -z "${DB_ROOT_PASSWORD}" ]]; then
    echo "You must set the DB_ROOT_PASSWORD environment variable to set for the new db."
    exit 42
fi

# Add root db password to parameter file in-place.
sed "s/\[/[{\"ParameterKey\": \"DBPassword\", \"ParameterValue\": \"${DB_ROOT_PASSWORD}\"},/" cfn/db.${environment}.params.json > params.json

TAGS=$(awk -F\" '/"/ { print "Key=" $2 ",Value=" $4 }' cfn/${environment}.tags.json | tr '\n' ' ')
echo TAG: ${TAGS}

echo "aws cloudformation create-change-set --stack-name $stack_name --change-set-name change-set-$(date +%s) --template-body $template_file --parameters file://params.json  --tags ${TAGS} --capabilities CAPABILITY_NAMED_IAM CAPABILITY_IAM | jq -r .Id"

change_set_id=`aws cloudformation create-change-set --stack-name $stack_name --change-set-name change-set-$(date +%s) --template-body file://$template_file --parameters file://params.json  --tags ${TAGS} --capabilities CAPABILITY_NAMED_IAM CAPABILITY_IAM | jq -r .Id`
status=$?

if [ $status -ne 0 ]; then
  echo "Error when creating change set: $change_set_id"
  exit 1
fi

change_set_status=`aws cloudformation describe-change-set --change-set-name $change_set_id | jq -r .Status 2>&1`
echo "Status: $change_set_status"

while [[ $change_set_status == "CREATE_IN_PROGRESS" ]]; do
  echo "Change Set is still being created. Sleeping for 5 seconds..."
  sleep 5

  change_set_status=`aws cloudformation describe-change-set --change-set-name $change_set_id | jq -r .Status 2>&1`
done

change_set_payload=`aws cloudformation describe-change-set --change-set-name $change_set_id`
stack_id=`echo $change_set_payload | jq -r .StackId`
changes=`echo $change_set_payload | jq -r .Changes`

base_url="https://${AWS_DEFAULT_REGION}.console.aws.amazon.com/cloudformation/home?region=${AWS_DEFAULT_REGION}#/stacks/changesets/"
uri=`echo changes?stackId=${stack_id}\&changeSetId=${change_set_id} | sed 's/:/%3A/g' | sed 's/\//%2F/g'`

echo ""
echo "Change Set has been created."
#echo "$changes"
echo ""
echo "View the change set in the AWS Console: ${base_url}${uri}"