
Command-line Instructions
==============================================================

### Create ECR repo.

* **Prerequisites:**
	* Install [aws-cli](https://docs.aws.amazon.com/cli/latest/userguide/installing.html).
	* Install [jq](https://stedolan.github.io/jq/)

```bash
aws cloudformation deploy \
--stack-name dev-offer-management-api-service-ecr \
--region us-east-1 \
--template-file ../../apollo-platform/common/ecr/createEcr.yaml \
--parameter-overrides $(jq -r '.[] | [.ParameterKey, .ParameterValue] | join("=")' ecr.params.json) \
--capabilities CAPABILITY_NAMED_IAM
```

### Deploy offer-management-api's resources access to jenkins stack manually.

```bash
aws cloudformation deploy \
--stack-name sole-offer-management-api-resources-access \
--region us-east-1 \
--template-file templates/jenkinsUserAccessToSoleResources.yaml \
--parameter-overrides $(jq -r '.[] | [.ParameterKey, .ParameterValue] | join("=")' resourcesCFN.sole.params.json) \
--capabilities CAPABILITY_NAMED_IAM
```

### Deploy offer-management-api stack manually

* **Prerequisites:**
	* Install [ecs-service](https://www.npmjs.com/package/ecs-service).

```bash
ecs-service deploy <stack-name> <app-version> \
templates/service.json \
dev.params.json \
-e dev.env \
-t dev.tags.json \
-r us-east-1 \
-p assumed_role
```
##### Example

```bash
ecs-service deploy dev-offer-management-api 0.0.1-SNAPSHOT  \
templates/service.json \
dev.params.json \
--env-file dev.env \
--tag-file  dev.tags.json \
-r us-east-1 \
-p assumed_role 
```