[{"ParameterKey": "CIDRIpAddress", "ParameterValue": "**********/32"}, {"ParameterKey": "Component", "ParameterValue": "offer-management-api"}, {"ParameterKey": "DBEngineVersion", "ParameterValue": "15.10"}, {"ParameterKey": "DBInstanceClass", "ParameterValue": "db.r5.large"}, {"ParameterKey": "DBName", "ParameterValue": "offermanagement"}, {"ParameterKey": "DBPort", "ParameterValue": "5432"}, {"ParameterKey": "DBSnapshotName", "ParameterValue": ""}, {"ParameterKey": "DBType", "ParameterValue": "postgres"}, {"ParameterKey": "DBUser", "ParameterValue": "db<PERSON><PERSON>"}, {"ParameterKey": "Environment", "ParameterValue": "dev"}, {"ParameterKey": "KMSKeyName", "ParameterValue": "amrpwl-nonprod-database"}, {"ParameterKey": "NetworkStackName", "ParameterValue": "AMRPWL-Dev"}, {"ParameterKey": "PagerDutyURLDB", "ParameterValue": "https://events.pagerduty.com/integration/d132264d93f747baa79ead1a770f1935/enqueue"}, {"ParameterKey": "Project", "ParameterValue": "offers"}, {"ParameterKey": "ReplicaCpuThreshold", "ParameterValue": "60"}, {"ParameterKey": "ReplicaMaxCapacity", "ParameterValue": "5"}, {"ParameterKey": "ReplicaMinCapacity", "ParameterValue": "1"}, {"ParameterKey": "ReplicaScaleInCooldownInSeconds", "ParameterValue": "300"}, {"ParameterKey": "ReplicaScaleOutCooldownInSeconds", "ParameterValue": "120"}, {"ParameterKey": "SubnetId1", "ParameterValue": "subnet-018b95ae4773c1989"}, {"ParameterKey": "SubnetId2", "ParameterValue": "subnet-0a2ef31eb3a04e1af"}, {"ParameterKey": "SubnetId3", "ParameterValue": "subnet-0e6f96cd597193c67"}, {"ParameterKey": "Team", "ParameterValue": "Things"}, {"ParameterKey": "VpcId", "ParameterValue": "vpc-033de5f52e0ff993b"}]