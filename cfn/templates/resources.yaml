AWSTemplateFormatVersion: '2010-09-09'
Description: Create RDS Aurora postgres cluster with one reader and writer instance and auto scaling

Parameters:
  DBName:
    Description: The database name
    Type: String
    MinLength: 1
    MaxLength: 25
  KMSKeyName:
    Description: The name of the KMS Key
    Type: String
    Default: amrpwl-nonprod-database
  DBUser:
    Description: The database admin account username
    Type: String
    MinLength: 1
    MaxLength: 16
    AllowedPattern: "[a-zA-Z][a-zA-Z0-9]*"
    ConstraintDescription: must begin with a letter and contain only alphanumeric characters.
    Default: dbadmin
  DBPassword:
    Description: The database admin account password
    NoEcho: true
    Type: String
    MinLength: 8
    MaxLength: 41
    AllowedPattern: '[a-zA-Z0-9_,<>;`?&^~!$%\|\(\)\*\{\}\[\]\-\+\=\.]*'
    ConstraintDescription: must contain only alphanumeric characters and the special characters _-.,
  DBPort:
    Description: The port at which database will be running
    Type: Number
    Default: 5432
  DBInstanceClass:
    Type: String
    Description: The instance type to use for the database.
    Default: db.r5.large
    AllowedValues:
      - db.t3.small
      - db.t3.medium
      - db.r5.large
      - db.r5.xlarge
      - db.r5.2xlarge
      - db.r5.4xlarge
      - db.r5.8xlarge
  DBType:
    Type: String
    Description: Type of aurora database
    AllowedValues: [postgres]
  Environment:
    Type: String
    AllowedValues: ['sole', 'dev', 'int', 'uat', 'prod']
    Default: dev
    Description: The environment
    ConstraintDescription: Must be one of dev, uat or prod.
  ReplicaMinCapacity:
    Type: Number
    Description: Minimum number of read replicas required
    Default: 1
  ReplicaMaxCapacity:
    Type: Number
    Description: Maximum number of read replicas required
    Default: 5
  ReplicaCpuThreshold:
    Type: Number
    Description: CPU threshold for auto scaling replica
    Default: 60
  ReplicaScaleInCooldownInSeconds:
    Type: Number
    Description: Amount of time, in seconds, after a scale-in activity completes before another scale-in activity can start
    # 5 mins before scaling in happens - its fine if we have more and it waits for more time to scale in
    Default: 300
  ReplicaScaleOutCooldownInSeconds:
    Type: Number
    Description: Amount of time, in seconds, after a scale-out activity completes before another scale-out activity can start
    # 2 mins before next scaling out happens - we want this quick
    Default: 120
    # Tags
  Project:
    Type: String
    Description: The Project tag value
    MinLength: 1
    ConstraintDescription: Must be a non-empty value
  CIDRIpAddress:
    Type: String
    Description: CIDR IP Address
    MinLength: 1
    ConstraintDescription: Must be a non-empty value
  Team:
    Type: String
    Description: The Team tag value
    MinLength: 1
    ConstraintDescription: Must be a non-empty value
  Component:
    Type: String
    Description: The Component tag value
    MinLength: 1
    ConstraintDescription: Must be a non-empty value
  DBSnapshotName:
    Description: Optional. DB Snapshot ID to restore database. Leave this blank if you are not restoring from a snapshot.
    Type: String
    Default: ""
  DBEngineVersion:
    Description: Select Database Engine Version
    Type: String
    Default: 9.6.12
    AllowedValues:
      - 9.6.12
      - 10.7
      - 10.11
      - 10.14
      - 10.18
      - 12.8
      - 12.9
      - 12.16
      - '15.10'
  VpcId:
    Type: String
    Description: Vpc id
    Default: vpc-033de5f52e0ff993b
  SubnetId1:
    Type: String
    Description: Subnet 1
    Default: subnet-02d9fa1e617ed1833
  SubnetId2:
    Type: String
    Description: Subnet 2
    Default: subnet-034ca364a5500f16b
  SubnetId3:
    Type: String
    Description: Subnet 3
    Default: subnet-03533bbf2381016c9
  NetworkStackName:
    Description: Name of the Network Stack. i.e 'AMRPWL-Dev/AMRPWL-UAT/AMRPWL-Load/AMRPWL-Prod'
    Type: String
  PagerDutyURLDB:
    Type: String
    Description: Pager Duty endpoint
  CpuUtilizationThreshold:
    Type: Number
    Description: Percentage of cpu utilization that is considered high
    Default: '90'

Mappings:
  DBFamilyMap:
    "9.6.12":
      "family": "aurora-postgresql9.6"
    "10.7":
      "family": "aurora-postgresql10"
    "10.11":
      "family": "aurora-postgresql10"
    "10.14":
      "family": "aurora-postgresql10"
    "10.18":
      "family": "aurora-postgresql10"
    "12.8":
      "family": "aurora-postgresql12"
    "12.9":
      "family": "aurora-postgresql12"
    "12.16":
      "family": "aurora-postgresql12"
    "15.10":
      "family": "aurora-postgresql15"
  Engine:
    postgres:
      engine: aurora-postgresql
#   mysql:
#     engine: aurora-mysql
  EngineVersion:
    postgres:
      version: 9.6.12
#   mysql:
#     version: 5.7.12
  ParameterGroup:
    postgres:
      group: default.aurora-postgresql15
#   mysql:
#     group: default.aurora-mysql5.7
Conditions:
  IsUseDBSnapshot: !Not [!Equals [!Ref DBSnapshotName, ""]]
  IsNotUseDBSnapshot: !Not [Condition: IsUseDBSnapshot]
  IsProd: !Equals [!Ref Environment, 'prod']
  IsDev: !Equals [ !Ref 'Environment', 'dev' ]
  IsSole: !Equals [ !Ref 'Environment', 'sole']
  IsUat: !Equals [ !Ref 'Environment', 'uat']
  IsReplica: !Or [Condition: IsUat, Condition: IsProd]
  DeletionPolicyCond: !Or [ Condition: IsDev, Condition: IsSole ]


Resources:
  MonitoringIAMRole:
    Type: AWS::IAM::Role
    Condition: IsProd
    Properties:
      AssumeRolePolicyDocument:
        Version: "2012-10-17"
        Statement:
          - Effect: "Allow"
            Principal:
              Service:
                - "monitoring.rds.amazonaws.com"
            Action:
              - "sts:AssumeRole"
      Path: "/"
      ManagedPolicyArns:
        - arn:aws:iam::aws:policy/service-role/AmazonRDSEnhancedMonitoringRole
  DBSNSTopic:
   Type: AWS::SNS::Topic
   Properties:
     Subscription:
       - Endpoint:
           Fn::Sub: '${PagerDutyURLDB}'
         Protocol: https
     TopicName:
       Fn::Sub: "${AWS::StackName}-SNS-Alarm"
  AuroraMasterSecret:
    Type: AWS::SecretsManager::Secret
    Properties:
      Name: !Sub '${AWS::StackName}-master-user'
      Description: !Join ['', ['Aurora PostgreSQL Master User Secret ', 'for CloudFormation Stack ', !Ref 'AWS::StackName']]
      GenerateSecretString:
        SecretStringTemplate: !Join ['', ['{"username": "', !Ref DBUser, '"}']]
        GenerateStringKey: "password"
        ExcludePunctuation: true
        PasswordLength: 16

  AuroraReportingUserSecret:
    Type: AWS::SecretsManager::Secret
    Properties:
      Name: !Sub '${AWS::StackName}-reporting-user'
      Description: !Join ['', ['Aurora PostgreSQL Reporting User Secret ', 'for CloudFormation Stack ', !Ref 'AWS::StackName']]
      GenerateSecretString:
        SecretStringTemplate: !Join ['', ['{"username": "dbreportinguser"}']]
        GenerateStringKey: "password"
        ExcludePunctuation: true
        PasswordLength: 16

  AuroraAppUserSecret:
    Type: AWS::SecretsManager::Secret
    Properties:
      Name: !Sub '${AWS::StackName}-app-user'
      Description: !Join ['', ['Aurora PostgreSQL App User Secret ', 'for CloudFormation Stack ', !Ref 'AWS::StackName']]
      GenerateSecretString:
        SecretStringTemplate: !Join ['', ['{"username": "dbappuser"}']]
        GenerateStringKey: "password"
        ExcludePunctuation: true
        PasswordLength: 16

  AuroraAdminUserSecret:
    Type: AWS::SecretsManager::Secret
    Properties:
      Name: !Sub '${AWS::StackName}-admin-user'
      Description: !Join ['', ['Aurora PostgreSQL Admin User Secret ', 'for CloudFormation Stack ', !Ref 'AWS::StackName']]
      GenerateSecretString:
        SecretStringTemplate: !Join ['', ['{"username": "dbadminuser"}']]
        GenerateStringKey: "password"
        ExcludePunctuation: true
        PasswordLength: 16

  AuroraSecretResourcePolicy:
    Type: AWS::SecretsManager::ResourcePolicy
    Properties:
      SecretId: !Ref AuroraMasterSecret
      ResourcePolicy:
        Version: "2012-10-17"
        Statement:
          - Effect: "Deny"
            Principal:
              AWS: !Sub "arn:aws:iam::${AWS::AccountId}:root"
            Action: "secretsmanager:DeleteSecret"
            Resource: "*"

  ClusterSecurityGroup:
    Type: AWS::EC2::SecurityGroup
    Properties:
      GroupDescription: !Sub "Postgres Database Access for ${AWS::StackName}"
      VpcId:
        Ref: VpcId
      SecurityGroupIngress:
        - IpProtocol: tcp
          FromPort: !Ref DBPort
          ToPort: !Ref DBPort
          CidrIp:
            !ImportValue
            'Fn::Sub': "${NetworkStackName}-App-AZ-A-CIDR"
          Description:
            !ImportValue
            'Fn::Sub': "${NetworkStackName}-App-AZ-A-SubID"
        - IpProtocol: tcp
          FromPort: !Ref DBPort
          ToPort: !Ref DBPort
          CidrIp:
            !ImportValue
            'Fn::Sub': "${NetworkStackName}-App-AZ-B-CIDR"
          Description:
            !ImportValue
            'Fn::Sub': "${NetworkStackName}-App-AZ-B-SubID"
        - IpProtocol: tcp
          FromPort: !Ref DBPort
          ToPort: !Ref DBPort
          CidrIp:
            !ImportValue
            'Fn::Sub': "${NetworkStackName}-App-AZ-C-CIDR"
          Description:
            !ImportValue
            'Fn::Sub': "${NetworkStackName}-App-AZ-C-SubID"
        - IpProtocol: tcp
          FromPort: !Ref DBPort
          ToPort: !Ref DBPort
          CidrIp: !Ref CIDRIpAddress
          Description: "Bastion Host Location"

  ClusterSecurityGroupIngress:
    Type: 'AWS::EC2::SecurityGroupIngress'
    Properties:
      GroupId: !GetAtt 'ClusterSecurityGroup.GroupId'
      IpProtocol: -1
      SourceSecurityGroupId: !Ref ClusterSecurityGroup
      Description: 'Self Reference'

  RDSDBClusterParameterGroup:
    Type: AWS::RDS::DBClusterParameterGroup
    Properties:
      Description: !Join [ "- ", [ "Aurora PG Cluster Parameter Group for  Cloudformation Stack ", !Ref DBName ] ]
      Family: !FindInMap [DBFamilyMap, !Ref DBEngineVersion, "family"]
#     Family: aurora-postgresql15
      Parameters:
        rds.force_ssl: 1
        shared_preload_libraries: auto_explain,pg_stat_statements,pg_hint_plan,pgaudit
        log_statement: "ddl"
        log_connections: 1
        log_disconnections: 1
        log_lock_waits: 1
        log_min_duration_statement: 5000
        auto_explain.log_min_duration: 5000
        auto_explain.log_verbose: 1
        log_rotation_age: 1440
        log_rotation_size: 102400
        rds.log_retention_period: 10080
        random_page_cost: 1
        track_activity_query_size: 16384
        idle_in_transaction_session_timeout: 7200000
        statement_timeout: 7200000
        search_path: '"$user",public'

#  DBParamGroup:
#    Type: AWS::RDS::DBParameterGroup
#    Properties:
#      Description: !Join [ "- ", [ "Aurora PG Database Instance Parameter Group for Cloudformation Stack ", !Ref DBName ] ]
#     Family: !FindInMap [DBFamilyMap, !Ref DBEngineVersion, "family"]
#      Family: aurora-postgresql15
#      Parameters:
#        shared_preload_libraries: auto_explain,pg_stat_statements,pg_hint_plan,pgaudit
#        log_statement: "ddl"
#        log_connections: 1
#        log_disconnections: 1
#        log_lock_waits: 1
#        log_min_duration_statement: 5000
#        auto_explain.log_min_duration: 5000
#        auto_explain.log_verbose: 1
#        log_rotation_age: 1440
#        log_rotation_size: 102400
#        rds.log_retention_period: 10080
#        random_page_cost: 1
#        track_activity_query_size: 16384
#        idle_in_transaction_session_timeout: 7200000
#        statement_timeout: 7200000
#        search_path: '"$user",public'

  DBSubnetGroup:
    Type: AWS::RDS::DBSubnetGroup
    Properties:
      DBSubnetGroupDescription: !Sub 'DB Subnet Group for ${AWS::StackName}'
      SubnetIds:
        - Ref: SubnetId1
        - Ref: SubnetId2
        - Ref: SubnetId3

  AuroraDBCluster:
    Type: AWS::RDS::DBCluster
    DeletionPolicy: !If [ DeletionPolicyCond, Delete, Snapshot ]
    UpdateReplacePolicy: !If [ DeletionPolicyCond, Delete, Snapshot ]
    Properties:
      Engine: aurora-postgresql
      EngineVersion: !Ref DBEngineVersion
      DatabaseName: !If [IsUseDBSnapshot, !Ref "AWS::NoValue", !Ref DBName]
      Port: !Ref DBPort
      MasterUsername:
        !If [IsUseDBSnapshot, !Ref "AWS::NoValue", !Join ['', ['{{resolve:secretsmanager:', !Ref AuroraMasterSecret, ':SecretString:username}}' ]]]
      MasterUserPassword:
        !If [IsUseDBSnapshot, !Ref "AWS::NoValue", !Join ['', ['{{resolve:secretsmanager:', !Ref AuroraMasterSecret, ':SecretString:password}}' ]]]
      DBSubnetGroupName: !Ref DBSubnetGroup
      VpcSecurityGroupIds:
        - !Ref ClusterSecurityGroup
      BackupRetentionPeriod: !If [IsProd, 35, 7]
      DBClusterParameterGroupName: !Ref RDSDBClusterParameterGroup
      SnapshotIdentifier: !If [IsUseDBSnapshot, !Ref DBSnapshotName, !Ref "AWS::NoValue"]
      StorageEncrypted:  !If [IsUseDBSnapshot, !Ref "AWS::NoValue", true]
      KmsKeyId:
        Fn::ImportValue: !Ref KMSKeyName
      EnableIAMDatabaseAuthentication: true
      PreferredBackupWindow: 05:00-06:00
      PreferredMaintenanceWindow: sun:06:00-sun:07:00

  AuroraDBFirstInstance:
    Type: AWS::RDS::DBInstance
    Properties:
      CopyTagsToSnapshot: true
      DBInstanceClass:
        Ref: DBInstanceClass
      DBClusterIdentifier: !Ref AuroraDBCluster
      Engine: aurora-postgresql
      EngineVersion: !Ref DBEngineVersion
#      DBParameterGroupName:
#        Ref: DBParamGroup
      DBInstanceIdentifier: !Sub ${AWS::StackName}-writer
      MonitoringInterval: !If [IsProd, 1, 0]
      MonitoringRoleArn: !If [IsProd, !GetAtt MonitoringIAMRole.Arn, !Ref "AWS::NoValue"]
      AutoMinorVersionUpgrade: true
      DBSubnetGroupName: !Ref DBSubnetGroup
      PubliclyAccessible: false
      EnablePerformanceInsights: true
      PerformanceInsightsKMSKeyId:
        Fn::ImportValue: !Ref KMSKeyName
      PerformanceInsightsRetentionPeriod: !If [IsProd, 731, 7]
      CACertificateIdentifier: rds-ca-rsa2048-g1

  AuroraDBSecondInstance:
    Condition: IsReplica
    Type: AWS::RDS::DBInstance
    DependsOn:
      - AuroraDBFirstInstance
    Properties:
      CopyTagsToSnapshot: true
      DBInstanceClass:
        Ref: DBInstanceClass
      DBInstanceIdentifier: !Sub ${AWS::StackName}-reader
      DBClusterIdentifier: !Ref AuroraDBCluster
      Engine: aurora-postgresql
      EngineVersion: !Ref DBEngineVersion
#      DBParameterGroupName:
#        Ref: DBParamGroup
      MonitoringInterval: !If [IsProd, 1, 0]
      MonitoringRoleArn: !If [IsProd, !GetAtt MonitoringIAMRole.Arn, !Ref "AWS::NoValue"]
      AutoMinorVersionUpgrade: true
      DBSubnetGroupName: !Ref DBSubnetGroup
      PubliclyAccessible: false
      EnablePerformanceInsights: true
      PerformanceInsightsKMSKeyId:
        Fn::ImportValue: !Ref KMSKeyName
      PerformanceInsightsRetentionPeriod: !If [IsProd, 731, 7]
      CACertificateIdentifier: rds-ca-rsa2048-g1

  RdsReplicaScalableTarget:
    # https://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-applicationautoscaling-scalabletarget.html
    Type: AWS::ApplicationAutoScaling::ScalableTarget
    Properties:
      MaxCapacity: !Ref ReplicaMaxCapacity
      MinCapacity: !Ref ReplicaMinCapacity
      ResourceId: !Sub cluster:${AuroraDBCluster}
      RoleARN:
        # this is the service linked role for RDS Auto Scaling
        !Sub arn:aws:iam::${AWS::AccountId}:role/aws-service-role/rds.application-autoscaling.amazonaws.com/AWSServiceRoleForApplicationAutoScaling_RDSCluster
      ScalableDimension: rds:cluster:ReadReplicaCount
      ServiceNamespace: rds

  RdsReplicaScalingPolicy:
    # https://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-applicationautoscaling-scalingpolicy.html
    Type: AWS::ApplicationAutoScaling::ScalingPolicy
    Properties:
      PolicyName: AuroraAutoScalingCpuPolicy
      PolicyType: TargetTrackingScaling
      ScalingTargetId: !Ref RdsReplicaScalableTarget
      TargetTrackingScalingPolicyConfiguration:
        TargetValue: !Ref ReplicaCpuThreshold
        ScaleInCooldown: !Ref ReplicaScaleInCooldownInSeconds
        ScaleOutCooldown: !Ref ReplicaScaleOutCooldownInSeconds
        PredefinedMetricSpecification:
          PredefinedMetricType: RDSReaderAverageCPUUtilization

  DatabaseHighCPUUtilizationAlarm:
    Type: "AWS::CloudWatch::Alarm"
    Properties:
      ActionsEnabled: !If [ IsDev, false, true ]
      AlarmActions:
        - Ref: DBSNSTopic
      OKActions:
        - Ref: DBSNSTopic
      AlarmDescription: 'High CPU Utilization on Aurora Cluster'
      MetricName: CPUUtilization
      Namespace: AWS/RDS
      Statistic: Maximum
      Period: 300
      EvaluationPeriods: 3
      DatapointsToAlarm: 3
      Threshold: !Ref 'CpuUtilizationThreshold'
      ComparisonOperator: GreaterThanOrEqualToThreshold
      TreatMissingData: breaching
      Dimensions:
        - Name: DBClusterIdentifier
          Value:
            Ref: AuroraDBCluster

  CPUUtilizationAlarm1:
    Type: "AWS::CloudWatch::Alarm"
    Properties:
      ActionsEnabled: true
      AlarmActions:
        - Ref: DBSNSTopic
      OKActions:
        - Ref: DBSNSTopic
      AlarmDescription: 'CPU_Utilization'
      Dimensions:
        - Name: DBInstanceIdentifier
          Value:
            Ref: AuroraDBFirstInstance
      MetricName: CPUUtilization
      Statistic: Maximum
      Namespace: 'AWS/RDS'
      Threshold: '80'
      Unit: Percent
      ComparisonOperator: 'GreaterThanOrEqualToThreshold'
      Period: '60'
      EvaluationPeriods: '5'
      TreatMissingData: 'notBreaching'

  CPUUtilizationAlarm2:
    Condition: IsReplica
    Type: "AWS::CloudWatch::Alarm"
    Properties:
      ActionsEnabled: true
      AlarmActions:
        - Ref: DBSNSTopic
      OKActions:
        - Ref: DBSNSTopic
      AlarmDescription: 'CPU_Utilization'
      Dimensions:
        - Name: DBInstanceIdentifier
          Value:
            Ref: AuroraDBSecondInstance
      MetricName: CPUUtilization
      Statistic: Maximum
      Namespace: 'AWS/RDS'
      Threshold: '80'
      Unit: Percent
      ComparisonOperator: 'GreaterThanOrEqualToThreshold'
      Period: '60'
      EvaluationPeriods: '5'
      TreatMissingData: 'notBreaching'

  MaxUsedTxIDsAlarm1:
    Type: "AWS::CloudWatch::Alarm"
    Properties:
      ActionsEnabled: true
      AlarmActions:
        - Ref: DBSNSTopic
      OKActions:
        - Ref: DBSNSTopic
      AlarmDescription: 'Maximum Used Transaction IDs'
      Dimensions:
        - Name: DBInstanceIdentifier
          Value:
            Ref: AuroraDBFirstInstance
      MetricName: 'MaximumUsedTransactionIDs'
      Statistic: Average
      Namespace: 'AWS/RDS'
      Threshold: '600000000'
      Unit: Count
      ComparisonOperator: 'GreaterThanOrEqualToThreshold'
      Period: '60'
      EvaluationPeriods: '5'
      TreatMissingData: 'notBreaching'

  MaxUsedTxIDsAlarm2:
    Condition: IsReplica
    Type: "AWS::CloudWatch::Alarm"
    Properties:
      ActionsEnabled: true
      AlarmActions:
        - Ref: DBSNSTopic
      OKActions:
        - Ref: DBSNSTopic
      AlarmDescription: 'Maximum Used Transaction IDs'
      Dimensions:
        - Name: DBInstanceIdentifier
          Value:
            Ref: AuroraDBSecondInstance
      MetricName: 'MaximumUsedTransactionIDs'
      Statistic: Average
      Namespace: 'AWS/RDS'
      Threshold: '600000000'
      Unit: Count
      ComparisonOperator: 'GreaterThanOrEqualToThreshold'
      Period: '60'
      EvaluationPeriods: '5'
      TreatMissingData: 'notBreaching'

  FreeLocalStorageAlarm1:
    Type: "AWS::CloudWatch::Alarm"
    Properties:
      ActionsEnabled: true
      AlarmActions:
        - Ref: DBSNSTopic
      OKActions:
        - Ref: DBSNSTopic
      AlarmDescription: 'Free Local Storage'
      Dimensions:
        - Name: DBInstanceIdentifier
          Value:
            Ref: AuroraDBFirstInstance
      MetricName: 'FreeLocalStorage'
      Statistic: Average
      Namespace: 'AWS/RDS'
      Threshold: '5368709120'
      Unit: Bytes
      ComparisonOperator: 'LessThanOrEqualToThreshold'
      Period: '60'
      EvaluationPeriods: '5'
      TreatMissingData: 'notBreaching'

  FreeLocalStorageAlarm2:
    Condition: IsReplica
    Type: "AWS::CloudWatch::Alarm"
    Properties:
      ActionsEnabled: true
      AlarmActions:
        - Ref: DBSNSTopic
      OKActions:
        - Ref: DBSNSTopic
      AlarmDescription: 'Free Local Storage'
      Dimensions:
        - Name: DBInstanceIdentifier
          Value:
            Ref: AuroraDBSecondInstance
      MetricName: 'FreeLocalStorage'
      Statistic: Average
      Namespace: 'AWS/RDS'
      Threshold: '5368709120'
      Unit: Bytes
      ComparisonOperator: 'LessThanOrEqualToThreshold'
      Period: '60'
      EvaluationPeriods: '5'
      TreatMissingData: 'notBreaching'

  DatabaseClusterEventSubscription:
    Type: 'AWS::RDS::EventSubscription'
    Properties:
      EventCategories:
        - failover
        - failure
        - maintenance
      SnsTopicArn: !Ref DBSNSTopic
      SourceIds: [!Ref AuroraDBCluster]
      SourceType: 'db-cluster'

  DatabaseInstanceEventSubscription:
    Type: 'AWS::RDS::EventSubscription'
    Properties:
     EventCategories:
       - backup
       - failover
       - failure
       - maintenance
     SnsTopicArn: !Ref DBSNSTopic
     SourceIds:
       - !Ref AuroraDBFirstInstance
       - !If [IsReplica, !Ref AuroraDBSecondInstance, !Ref "AWS::NoValue"]
     SourceType: 'db-instance'

#  DBParameterGroupEventSubscription:
#    Type: 'AWS::RDS::EventSubscription'
#    Properties:
#      EventCategories:
#       - configuration change
#      SnsTopicArn: !Ref DBSNSTopic
#      SourceIds: [!Ref DBParamGroup]
#      SourceType: 'db-parameter-group'

  SecretAuroraClusterAttachment:
    Condition: IsNotUseDBSnapshot
    Type: AWS::SecretsManager::SecretTargetAttachment
    Properties:
      SecretId: !Ref AuroraMasterSecret
      TargetId: !Ref AuroraDBCluster
      TargetType: AWS::RDS::DBCluster

## Outputs ##
Outputs:
  DatabaseWriterEndpoint:
    Description: Cluster Writer Endpoint
    Value: !Sub '${AuroraDBCluster.Endpoint.Address}:${AuroraDBCluster.Endpoint.Port}'
    Export:
      Name:
        Fn::Sub: '${AWS::StackName}-DatabaseWriterEndpoint'
  DatabaseReaderEndpoint:
    Description: Cluster Reader Endpoint
    Value: !Sub '${AuroraDBCluster.ReadEndpoint.Address}:${AuroraDBCluster.Endpoint.Port}'
    Export:
      Name:
        Fn::Sub: '${AWS::StackName}-DatabaseReaderEndpoint'
  AuroraDBClusterIdentifier:
    Description: Cluster Identifier
    Value: !Sub '${AuroraDBCluster}'
    Export:
      Name:
        Fn::Sub: '${AWS::StackName}-DatabaseClusterIdentifier'
  DatabasePort:
    Description: Database Port
    Value: !Sub '${AuroraDBCluster.Endpoint.Port}'
    Export:
      Name:
        Fn::Sub: '${AWS::StackName}-DatabasePort'
  DatabaseName:
    Description: Database Name
    Value: !Ref DBName
    Export:
      Name:
        Fn::Sub: '${AWS::StackName}-DatabaseName'