AWSTemplateFormatVersion: '2010-09-09'
Description: Creates a service / task def for partner-offer-submission-tool
Parameters:
  ClusterStackName:
    Description: Name of an active CloudFormation stack that contains an ECS cluster
    Type: String
    MinLength: 1
    MaxLength: 255
    AllowedPattern: ^[a-zA-Z][-a-zA-Z0-9]*$
  Path:
    Type: String
    Default: /offer-management-api
    Description: The path to register the application under the load balancer. Only
      include path name starting with a /
  Priority:
    Type: Number
    Description: The priority to register with the Load Balancer
    Default: 408
  AppName:
    Type: String
    Description: Name of app. Should be the same as docker repository name.
    Default: offer-management-api-service
  AppVersion:
    Type: String
    Description: Version label of app
  AppContainerPort:
    Type: Number
    Description: Port the app runs on in the image
    Default: '8080'
  ImageRepository:
    Type: String
    Description: The URI of the image repository where the app is published. Do not
      include app name in URI.
    Default: 277983268692.dkr.ecr.us-east-1.amazonaws.com
  ContainerTotalMemory:
    Type: Number
    Description: Soft memory of all containers (secure-proxy uses 512mb of this by default)
    Default: 2048
  AppMemory:
    Type: Number
    Description: Soft memory of app container
    Default: 512
  AppCpu:
    Type: Number
    Description: cpu units of app container
    Default: 256
  ContainerTotalCpu:
    Type: Number
    Description: cpu units of all containers (secure-proxy uses 256 of this by default)
    Default: 1024
  AppDesiredCount:
    Type: Number
    Description: Number of instances of the service to run
    Default: '1'
  AppMaxCount:
    Type: Number
    Description: Max number of instances of the service to scale out to
    Default: '3'
  AppMinCount:
    Type: Number
    Description: Min number of instances of the service to scale in to
    Default: '1'
  AutoScaleHighThreshold:
    Type: Number
    Description: Percentage of service memory utilization that is considered high
    Default: '70'
  AutoScaleLowThreshold:
    Type: Number
    Description: Percentage of service memory utilization that is considered low
    Default: '15'
  CpuUtilizationThreshold:
    Type: Number
    Description: Percentage of cpu utilization that is considered high
    Default: '90'
  MemoryUtilizationThreshold:
    Type: Number
    Description: Percentage of memory utilization that is considered high
    Default: '80'
  HealthyHostThreshold:
    Type: Number
    Description: healthy host alarm threshold
    Default: '1'
  LogRetention:
    Type: Number
    Description: Number of days to retain logs in CWL
    Default: '14'
  PagerDutyURLAPI:
    Type: String
    Description: Pager Duty endpoint
  ResourcesStackPrefix:
    Type: String
    Default: offer-management-api
    Description: Name of CloudFormation stack that contains resources such as Kinesis
      and DynamoDB
  KinesisAccountId:
    Type: String
    Description: the account where kinesis iam role is located
  KinesisUSEastAccountId:
    Type: String
    Description: the account where kinesis iam role is located
    Default: '************'
  KinesisUSRegion:
    Type: String
    Description: The AWS region for the Kinesis stream
    Default: us-east-1
  KMSKeyName:
    Description: The name of the KMS Key
    Type: String
    Default: amrpwl-nonprod-application
  Environment:
    Type: String
    Description: Environment Name
    Default: dev
  EnvironmentType:
    Type: String
    Description: Environment Type
    Default: nonprod
  SecureProxyEnvironment:
    Type: String
    Description: Secure Proxy Environment Name
    Default: dev
  AwsRegion:
    Type: String
    Description: Name of the AWS Region
  AwsSecretId:
    Type: String
    Description: Name of the AWS Secret ID
  CgsEndpoint:
    Type: String
    Description: CGS Endpoint
  OfferApiV2Endpoint:
    Type: String
    Description: Offer API V2 Endpoint
  offerFacetsEndpoint:
    Type: String
    Description: Offer FACETS API V1 Endpoint
  offerPromotionsEndpoint:
    Type: String
    Description: PROMOTIONS API V1 Endpoint
  partnersEndpoint:
    Type: String
    Description: Partners API V1 Endpoint
  offerDefaultImageBasePath:
    Type: String
    Description: Offer Default Image Base Path
  UrlPrefix:
    Type: String
    Description: URL Prefix
  NovaBusRegion:
    Type: String
    Description: NOVA BUS REGION
  NovaBusEndPoint:
    Type: String
    Description: NOVA BUS ENDPOINT
  NovaBusTableName:
    Type: String
    Description: NOVA BUS TABLENAME
  NLBListenerPort:
    Type: Number
    Description: Port the NLB listens on
    Default: '2024'
  NetworkStackName:
    Description: Name of the Network Stack. i.e 'AMRPWL-Dev/AMRPWL-UAT/AMRPWL-Load'
    Type: String
  KinesisForSplunkStackName:
    Description: The name of Kinesis which is unique for each environment, used for logging to splunk
    Type: String
Conditions:
  IsSole: !Equals [!Ref Environment, 'sole']
  IsDev: !Equals [ !Ref 'Environment', 'dev' ]
Resources:
  NLBTargetGroup:
    Type: AWS::ElasticLoadBalancingV2::TargetGroup
    Properties:
      Port: 443
      Protocol: TCP
      TargetType: ip
      TargetGroupAttributes:
        - Key: deregistration_delay.timeout_seconds
          Value: '20'
      VpcId: !ImportValue
        Fn::Sub: ${ClusterStackName}-VpcId
      Tags:
        - Key: Name
          Value: !Sub 'ECS Target Group - ${AWS::StackName}'
  NLBListener:
    Type: AWS::ElasticLoadBalancingV2::Listener
    Properties:
      DefaultActions:
        - Type: forward
          TargetGroupArn: !Ref 'NLBTargetGroup'
      LoadBalancerArn: !ImportValue
        Fn::Sub: ${ClusterStackName}-NetworkLoadBalancerARN
      Port: !Ref 'NLBListenerPort'
      Protocol: TCP
  EcsTaskRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Sid: ''
            Effect: Allow
            Principal:
              Service: ecs-tasks.amazonaws.com
            Action: sts:AssumeRole
      Path: /
      Policies:
        - PolicyName: SECRET_MANAGER-ACCESS
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - secretsmanager:GetSecretValue
                  - secretsmanager:DescribeSecret
                Resource:
                  - !Sub 'arn:aws:secretsmanager:${AWS::Region}:${AWS::AccountId}:secret:${Environment}-offer-management-api-resources*'

        - PolicyName: CROSS_ACCOUNT_KINESIS_ACCESS
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action: sts:AssumeRole
                Resource:
                  - !Sub 'arn:aws:iam::${KinesisAccountId}:role/${Environment}-nova-common-events-kinesis-role'
        - PolicyName: KINESIS_ACCESS
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - kinesis:DescribeStream
                  - kinesis:PutRecords
                  - kinesis:PutRecord
                Resource:
                  - !Sub 'arn:aws:kinesis:${KinesisUSRegion}:${KinesisUSEastAccountId}:stream/${Environment}-nova-event-bus-offer-event-stream'
        - PolicyName: KMS-Keys
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - kms:GenerateDataKey
                Resource:
                  - Fn::ImportValue:
                      Ref:
                        KMSKeyName
  EcsExecutionTaskRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Sid: ''
            Effect: Allow
            Principal:
              Service: ecs-tasks.amazonaws.com
            Action: sts:AssumeRole
      Path: /
      ManagedPolicyArns:
        - arn:aws:iam::aws:policy/service-role/AmazonECSTaskExecutionRolePolicy
  TaskDefinition:
    Type: AWS::ECS::TaskDefinition
    Properties:
      ContainerDefinitions:
        - Name: !Ref 'AppName'
          Image: !Sub '${ImageRepository}/${AppName}:${AppVersion}'
          Cpu: !Ref 'AppCpu'
          PortMappings:
            - ContainerPort: !Ref 'AppContainerPort'
          MemoryReservation: !Ref 'AppMemory'
          Environment:
            - Name: AWS_REGION
              Value: !Ref 'AwsRegion'
            - Name: AWS_SECRET_ID
              Value: !Ref 'AwsSecretId'
            - Name: CGS_ENDPOINT
              Value: !Ref 'CgsEndpoint'
            - Name: OFFER_API_V2_ENDPOINT
              Value: !Ref 'OfferApiV2Endpoint'
            - Name: OFFER_FACETS_ENDPOINT
              Value: !Ref 'offerFacetsEndpoint'
            - Name: OFFER_PROMOTIONS_ENDPOINT
              Value: !Ref 'offerPromotionsEndpoint'
            - Name: PARTNERS_ENDPOINT
              Value: !Ref 'partnersEndpoint'
            - Name: OFFER_DEFAULT_IMAGE_BASE
              Value: !Ref 'offerDefaultImageBasePath'
            - Name: URL_PREFIX
              Value: !Ref 'UrlPrefix'
            - Name: DATABASE_PORT
              Value: !ImportValue
                Fn::Sub: ${Environment}-${ResourcesStackPrefix}-resources-DatabasePort
            - Name: DATABASE_NAME
              Value: !ImportValue
                Fn::Sub: ${Environment}-${ResourcesStackPrefix}-resources-DatabaseName
            - Name: DATABASE_WRITER_ENDPOINT
              Value: !ImportValue
                Fn::Sub: ${Environment}-${ResourcesStackPrefix}-resources-DatabaseWriterEndpoint
            - Name: DATABASE_READER_ENDPOINT
              Value: !ImportValue
                Fn::Sub: ${Environment}-${ResourcesStackPrefix}-resources-DatabaseReaderEndpoint
            - Name: DATABASE_CLUSTER
              Value: !ImportValue
                Fn::Sub: ${Environment}-${ResourcesStackPrefix}-resources-DatabaseClusterIdentifier
            - Name: SPRING_PROFILES_ACTIVE
              Value: !Ref 'Environment'
            - Name: NOVA_BUS_REGION
              Value: !Ref 'NovaBusRegion'
            - Name: NOVA_BUS_ENDPOINT
              Value: !Ref 'NovaBusEndPoint'
            - Name: NOVA_BUS_TABLE_NAME
              Value: !Ref 'NovaBusTableName'
          Essential: 'true'
          HealthCheck:
            StartPeriod: 20
            Command:
              - "CMD-SHELL"
              - !Sub "curl -f http://localhost:${AppContainerPort}/offer-management-api/health || exit 123"
          Memory: !Ref 'ContainerTotalMemory'
          LogConfiguration:
            LogDriver: awslogs
            Options:
              awslogs-group: !Ref 'LogGroup'
              awslogs-region: !Ref 'AWS::Region'
              awslogs-stream-prefix: ecs
        - Name: secure-proxy
          Image: !Sub '277983268692.dkr.ecr.${AWS::Region}.amazonaws.com/secure-proxy:${SecureProxyEnvironment}'
          Cpu: 256
          MemoryReservation: 512
          VersionConsistency: disabled
          PortMappings:
            - ContainerPort: 443
          Environment:
            - Name: BACKEND_TARGET
              Value: !Sub 'http://127.0.0.1:${AppContainerPort}'
          Ulimits:
            - Name: nofile
              SoftLimit: 32768
              HardLimit: 32768
          Essential: "true"
          LogConfiguration:
            LogDriver: awslogs
            Options:
              awslogs-group: !Ref "LogGroup"
              awslogs-region: !Ref "AWS::Region"
              awslogs-stream-prefix: ecs
      Volumes: []
      Family: !Ref 'AWS::StackName'
      TaskRoleArn: !Ref 'EcsTaskRole'
      NetworkMode: awsvpc
      RequiresCompatibilities:
        - FARGATE
      ExecutionRoleArn: !Ref 'EcsExecutionTaskRole'
      Cpu: !Ref 'ContainerTotalCpu'
      Memory: !Ref 'ContainerTotalMemory'
  Service:
    Type: AWS::ECS::Service
    DependsOn:
      - LogGroup
    Properties:
      ServiceName: !Ref 'AWS::StackName'
      LaunchType: FARGATE
      TaskDefinition: !Ref 'TaskDefinition'
      DesiredCount: !Ref 'AppDesiredCount'
      LoadBalancers:
        - TargetGroupArn: !Ref 'NLBTargetGroup'
          ContainerPort: 443
          ContainerName: secure-proxy
      Cluster: !ImportValue
        Fn::Sub: ${ClusterStackName}-ClusterName
      DeploymentConfiguration:
        MinimumHealthyPercent: 100
      NetworkConfiguration:
        AwsvpcConfiguration:
          AssignPublicIp: DISABLED
          Subnets:
            - !ImportValue
              'Fn::Sub': "${NetworkStackName}-App-AZ-A-SubID"
            - !ImportValue
              'Fn::Sub': "${NetworkStackName}-App-AZ-B-SubID"
            - !ImportValue
              'Fn::Sub': "${NetworkStackName}-App-AZ-C-SubID"
          SecurityGroups:
            - !Ref 'AppSecurityGroup'
  AppSecurityGroup:
    Type: AWS::EC2::SecurityGroup
    Properties:
      GroupName: !Sub '${Environment}-${AppName}-sg'
      GroupDescription: Allow HTTPS from NLB
      VpcId: !ImportValue
        Fn::Sub: ${ClusterStackName}-VpcId
      SecurityGroupIngress:
        - IpProtocol: tcp
          FromPort: 443
          ToPort: 443
          CidrIp:
            !ImportValue
            'Fn::Sub': "${NetworkStackName}-LB-AZ-A-CIDR"
          Description:
            !ImportValue
            'Fn::Sub': "${NetworkStackName}-LoadBalancers-AZ-A-SubID"
        - IpProtocol: tcp
          FromPort: 443
          ToPort: 443
          CidrIp:
            !ImportValue
            'Fn::Sub': "${NetworkStackName}-LB-AZ-B-CIDR"
          Description:
            !ImportValue
            'Fn::Sub': "${NetworkStackName}-LoadBalancers-AZ-B-SubID"
        - IpProtocol: tcp
          FromPort: 443
          ToPort: 443
          CidrIp:
            !ImportValue
            'Fn::Sub': "${NetworkStackName}-LB-AZ-C-CIDR"
          Description:
            !ImportValue
            'Fn::Sub': "${NetworkStackName}-LoadBalancers-AZ-C-SubID"
      Tags:
        - Key: Name
          Value: !Sub '${Environment}-${AppName}-sg'
  LogGroup:
    Type: AWS::Logs::LogGroup
    Properties:
      RetentionInDays: !Ref 'LogRetention'
      LogGroupName: !Ref 'AWS::StackName'
  SubscriptionFilter:
    DependsOn: LogGroup
    Type: 'AWS::Logs::SubscriptionFilter'
    Properties:
      RoleArn:
        Fn::ImportValue:
          Fn::Sub: '${KinesisForSplunkStackName}-Role-Arn'
      LogGroupName:
        Ref: LogGroup
      FilterPattern: ''
      DestinationArn:
        Fn::ImportValue:
          Fn::Sub: '${KinesisForSplunkStackName}-Stream-Arn'
  EcsAutoScaleRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service: application-autoscaling.amazonaws.com
            Action: sts:AssumeRole
      Path: /
      ManagedPolicyArns:
        - arn:aws:iam::aws:policy/service-role/AmazonEC2ContainerServiceAutoscaleRole
  ScalableTarget:
    Type: AWS::ApplicationAutoScaling::ScalableTarget
    Properties:
      MaxCapacity: !Ref 'AppMaxCount'
      MinCapacity: !Ref 'AppMinCount'
      ResourceId: !Join
        - /
        - - service
          - !ImportValue
            Fn::Sub: ${ClusterStackName}-ClusterName
          - !GetAtt 'Service.Name'
      RoleARN: !GetAtt 'EcsAutoScaleRole.Arn'
      ScalableDimension: ecs:service:DesiredCount
      ServiceNamespace: ecs
  ScaleUpPolicy:
    Type: AWS::ApplicationAutoScaling::ScalingPolicy
    Properties:
      PolicyName: !Join
        - '-'
        - - !GetAtt 'Service.Name'
          - ScaleUpPolicy
      PolicyType: StepScaling
      ScalingTargetId: !Ref 'ScalableTarget'
      StepScalingPolicyConfiguration:
        AdjustmentType: PercentChangeInCapacity
        Cooldown: 60
        MetricAggregationType: Average
        StepAdjustments:
          - MetricIntervalLowerBound: 0
            MetricIntervalUpperBound: 10
            ScalingAdjustment: 10
          - MetricIntervalLowerBound: 10
            ScalingAdjustment: 30
  ScaleDownPolicy:
    Type: AWS::ApplicationAutoScaling::ScalingPolicy
    Properties:
      PolicyName: !Join
        - '-'
        - - !GetAtt 'Service.Name'
          - ScaleDownPolicy
      PolicyType: StepScaling
      ScalingTargetId: !Ref 'ScalableTarget'
      StepScalingPolicyConfiguration:
        AdjustmentType: PercentChangeInCapacity
        Cooldown: 60
        MetricAggregationType: Average
        StepAdjustments:
          - MetricIntervalLowerBound: -10
            MetricIntervalUpperBound: 0
            ScalingAdjustment: -10
          - MetricIntervalUpperBound: -10
            ScalingAdjustment: -30
  CpuUtilAlarmHigh:
    Type: AWS::CloudWatch::Alarm
    Properties:
      AlarmDescription: Cpu utilization alarm for ECS service for high cpu usage
      AlarmActions:
        - !Ref 'ScaleUpPolicy'
      MetricName: CPUUtilization
      Namespace: AWS/ECS
      Statistic: Average
      Period: '60'
      EvaluationPeriods: '2'
      Threshold: !Ref 'AutoScaleHighThreshold'
      ComparisonOperator: GreaterThanThreshold
      TreatMissingData: ignore
      Dimensions:
        - Name: ClusterName
          Value: !ImportValue
            Fn::Sub: ${ClusterStackName}-ClusterName
        - Name: ServiceName
          Value: !GetAtt 'Service.Name'
  CpuUtilAlarmLow:
    Type: AWS::CloudWatch::Alarm
    Properties:
      AlarmDescription: Cpu utilization alarm for ECS service for low cpu usage
      AlarmActions:
        - !Ref 'ScaleDownPolicy'
      MetricName: CPUUtilization
      Namespace: AWS/ECS
      Statistic: Average
      Period: '60'
      EvaluationPeriods: '2'
      Threshold: !Ref 'AutoScaleLowThreshold'
      ComparisonOperator: LessThanThreshold
      TreatMissingData: ignore
      Dimensions:
        - Name: ClusterName
          Value: !ImportValue
            Fn::Sub: ${ClusterStackName}-ClusterName
        - Name: ServiceName
          Value: !GetAtt 'Service.Name'
  CpuUtilization:
    Type: AWS::CloudWatch::Alarm
    Properties:
      AlarmDescription: CPU utilization alarm for ECS service for high CPU usage
      AlarmActions:
        - !Ref 'AlarmSNSTopic'
      OKActions:
        - !Ref 'AlarmSNSTopic'
      MetricName: CPUUtilization
      Namespace: AWS/ECS
      Statistic: Average
      Period: '60'
      EvaluationPeriods: '5'
      Threshold: !Ref 'CpuUtilizationThreshold'
      ActionsEnabled: !If [ IsDev, false, true ]
      ComparisonOperator: GreaterThanThreshold
      TreatMissingData: ignore
      Dimensions:
        - Name: ClusterName
          Value: !ImportValue
            Fn::Sub: ${ClusterStackName}-ClusterName
        - Name: ServiceName
          Value: !GetAtt 'Service.Name'
  MemoryUtilization:
    Type: AWS::CloudWatch::Alarm
    Properties:
      AlarmDescription: Memory utilization alarm for ECS service for high memory usage
      AlarmActions:
        - !Ref 'AlarmSNSTopic'
      OKActions:
        - !Ref 'AlarmSNSTopic'
      MetricName: MemoryUtilization
      Namespace: AWS/ECS
      Statistic: Average
      Period: '60'
      EvaluationPeriods: '5'
      Threshold: !Ref 'MemoryUtilizationThreshold'
      ActionsEnabled: !If [ IsDev, false, true ]
      ComparisonOperator: GreaterThanThreshold
      TreatMissingData: ignore
      Dimensions:
        - Name: ClusterName
          Value: !ImportValue
            Fn::Sub: ${ClusterStackName}-ClusterName
        - Name: ServiceName
          Value: !GetAtt 'Service.Name'
  HealthyHostAlarm:
    Type: AWS::CloudWatch::Alarm
    Properties:
      AlarmDescription: At least one healthy host not present
      AlarmActions:
        - !Ref 'AlarmSNSTopic'
      OKActions:
        - !Ref 'AlarmSNSTopic'
      MetricName: HealthyHostCount
      Namespace: AWS/NetworkELB
      Statistic: SampleCount
      Period: '60'
      EvaluationPeriods: '1'
      Threshold: !Ref 'HealthyHostThreshold'
      ActionsEnabled: !If [ IsDev, false, true ]
      ComparisonOperator: GreaterThanThreshold
      TreatMissingData: ignore
      Dimensions:
        - Name: TargetGroup
          Value: !GetAtt 'NLBTargetGroup.TargetGroupFullName'
        - Name: LoadBalancer
          Value: !ImportValue
            Fn::Sub: ${ClusterStackName}-LoadBalancerFullName
  AlarmSNSTopic:
    Type: AWS::SNS::Topic
    Properties:
      Subscription:
        - Endpoint: !Sub '${PagerDutyURLAPI}'
          Protocol: https
      TopicName: !Sub '${AWS::StackName}-SNS-Alarm'
Outputs:
  Service:
    Description: The name of the ECS service created
    Value: !GetAtt 'Service.Name'
    Export:
      Name: !Sub '${AWS::StackName}-ServiceName'
  TaskFamily:
    Description: The family of the task created for the service
    Value: !Ref 'AWS::StackName'
  TaskArn:
    Description: The ARN of the task created for the service
    Value: !Ref 'TaskDefinition'
  ServiceDNS:
    Description: The dns name of the app load balancer for the service
    Value: !Sub 'https://${ClusterStackName}.api.loyalty.com'
    Export:
      Name: !Sub '${AWS::StackName}-LoadBalancerDNSName'
  LogGroup:
    Description: The name of the log group created for the app
    Value: !Ref 'LogGroup'
    Export:
      Name: !Sub '${AWS::StackName}-LogGroupName'