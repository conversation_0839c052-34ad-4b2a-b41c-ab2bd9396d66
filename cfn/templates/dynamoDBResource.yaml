AWSTemplateFormatVersion: '2010-09-09'
Description: Resources for nova-event-producer sole environment
Parameters:
  Environment:
    Type: String
    Description: Environment Name
    Default: sole
Resources:
  EventStore:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: !Sub '${Environment}-offer-management-api-event-store'
      BillingMode: "PAY_PER_REQUEST"
      AttributeDefinitions:
        - AttributeName: 'id'
          AttributeType: 'S'
      KeySchema:
        - AttributeName: 'id'
          KeyType: 'HASH'
      PointInTimeRecoverySpecification:
        PointInTimeRecoveryEnabled: true
Outputs:
  EventStoreARN:
    Description: ARN of Event Store DynamoDB Table
    Value: !GetAtt EventStore.Arn
    Export:
      Name: !Sub '${AWS::StackName}-Event-Store-ARN'
  EventStoreName:
    Description: Name of Event Store DynamoDB Table
    Value: !Ref EventStore
    Export:
      Name: !Sub '${AWS::StackName}-Event-Store-Name'