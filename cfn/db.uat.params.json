[{"ParameterKey": "CIDRIpAddress", "ParameterValue": "**********/32"}, {"ParameterKey": "Component", "ParameterValue": "offer-management-api"}, {"ParameterKey": "DBEngineVersion", "ParameterValue": "15.10"}, {"ParameterKey": "DBInstanceClass", "ParameterValue": "db.r5.large"}, {"ParameterKey": "DBName", "ParameterValue": "offermanagement"}, {"ParameterKey": "DBPort", "ParameterValue": "5432"}, {"ParameterKey": "DBSnapshotName", "ParameterValue": ""}, {"ParameterKey": "DBType", "ParameterValue": "postgres"}, {"ParameterKey": "DBUser", "ParameterValue": "db<PERSON><PERSON>"}, {"ParameterKey": "Environment", "ParameterValue": "uat"}, {"ParameterKey": "KMSKeyName", "ParameterValue": "amrpwl-nonprod-database"}, {"ParameterKey": "NetworkStackName", "ParameterValue": "AMRPWL-UAT"}, {"ParameterKey": "PagerDutyURLDB", "ParameterValue": "https://events.pagerduty.com/integration/d132264d93f747baa79ead1a770f1935/enqueue"}, {"ParameterKey": "Project", "ParameterValue": "offers"}, {"ParameterKey": "ReplicaCpuThreshold", "ParameterValue": "60"}, {"ParameterKey": "ReplicaMaxCapacity", "ParameterValue": "5"}, {"ParameterKey": "ReplicaMinCapacity", "ParameterValue": "1"}, {"ParameterKey": "ReplicaScaleInCooldownInSeconds", "ParameterValue": "300"}, {"ParameterKey": "ReplicaScaleOutCooldownInSeconds", "ParameterValue": "120"}, {"ParameterKey": "SubnetId1", "ParameterValue": "subnet-0da721331bd4ea768"}, {"ParameterKey": "SubnetId2", "ParameterValue": "subnet-0cfc59f6f7bd1d03f"}, {"ParameterKey": "SubnetId3", "ParameterValue": "subnet-06e45ca89d7c915c9"}, {"ParameterKey": "Team", "ParameterValue": "Things"}, {"ParameterKey": "VpcId", "ParameterValue": "vpc-0b23d99194bdaca42"}]