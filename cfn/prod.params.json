{"Environment": "prod", "EnvironmentType": "prod", "SecureProxyEnvironment": "prod", "ClusterStackName": "prod-cpu-amrpwl-ecs", "AwsRegion": "us-east-1", "AwsSecretId": "prod-offer-management-api-resources", "CgsEndpoint": "https://content-generation.prod.api.loyalty.com:2023/content-generation-service", "OfferApiV2Endpoint": "https://prod-services.api.loyalty.com/offer-management", "offerFacetsEndpoint": "https://offer-facets.prod.api.loyalty.com:40001/offer-facets", "offerPromotionsEndpoint": "https://promotions.prod.api.loyalty.com:40010/promotions", "partnersEndpoint": "https://partners.prod.api.loyalty.com:2223/partners", "offerDefaultImageBasePath": "https://prod-l1-amrpwl-post-images.s3.amazonaws.com/default-images/", "UrlPrefix": "offer-management-api", "NovaBusTableName": "prod-nova-event-bus-offer-event-store", "NovaBusRegion": "us-east-1", "NovaBusEndPoint": "https://dynamodb.us-east-1.amazonaws.com", "PagerDutyURLAPI": "https://events.pagerduty.com/integration/ba9631257b70400dbe50ad2c5a60bdbe/enqueue", "NetworkStackName": "AMRPWL-Prod", "KinesisForSplunkStackName": "prod-kinesissplunk", "KinesisAccountId": "************", "KinesisUSEastAccountId": "************", "KinesisUSRegion": "us-east-1", "KMSKeyName": "amrpwl-prod-application", "ContainerTotalCpu": "1024", "AppMemory": "512", "ContainerTotalMemory": "2048", "AppCpu": "256", "AutoScaleHighThreshold": "70", "CpuUtilizationThreshold": "90", "MemoryUtilizationThreshold": "80", "HealthyHostThreshold": "1"}