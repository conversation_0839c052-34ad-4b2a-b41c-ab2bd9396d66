[{"ParameterKey": "CIDRIpAddress", "ParameterValue": "**********/32"}, {"ParameterKey": "Component", "ParameterValue": "offer-management-api"}, {"ParameterKey": "DBEngineVersion", "ParameterValue": "15.10"}, {"ParameterKey": "DBInstanceClass", "ParameterValue": "db.r5.large"}, {"ParameterKey": "DBName", "ParameterValue": "offermanagement"}, {"ParameterKey": "DBPort", "ParameterValue": "5432"}, {"ParameterKey": "DBSnapshotName", "ParameterValue": ""}, {"ParameterKey": "DBType", "ParameterValue": "postgres"}, {"ParameterKey": "DBUser", "ParameterValue": "db<PERSON><PERSON>"}, {"ParameterKey": "Environment", "ParameterValue": "int"}, {"ParameterKey": "KMSKeyName", "ParameterValue": "amrpwl-nonprod-database"}, {"ParameterKey": "NetworkStackName", "ParameterValue": "AMRPWL-Int"}, {"ParameterKey": "PagerDutyURLDB", "ParameterValue": "https://events.pagerduty.com/integration/d132264d93f747baa79ead1a770f1935/enqueue"}, {"ParameterKey": "Project", "ParameterValue": "offers"}, {"ParameterKey": "ReplicaCpuThreshold", "ParameterValue": "60"}, {"ParameterKey": "ReplicaMaxCapacity", "ParameterValue": "5"}, {"ParameterKey": "ReplicaMinCapacity", "ParameterValue": "1"}, {"ParameterKey": "ReplicaScaleInCooldownInSeconds", "ParameterValue": "300"}, {"ParameterKey": "ReplicaScaleOutCooldownInSeconds", "ParameterValue": "120"}, {"ParameterKey": "SubnetId1", "ParameterValue": "subnet-0bacca686cdcf1244"}, {"ParameterKey": "SubnetId2", "ParameterValue": "subnet-03157d8c0a2e79c27"}, {"ParameterKey": "SubnetId3", "ParameterValue": "subnet-0204f38421808bac6"}, {"ParameterKey": "Team", "ParameterValue": "Things"}, {"ParameterKey": "VpcId", "ParameterValue": "vpc-08312799d98f0d197"}]