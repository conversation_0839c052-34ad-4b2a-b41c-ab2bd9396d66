[{"ParameterKey": "CIDRIpAddress", "ParameterValue": "**********/32"}, {"ParameterKey": "Component", "ParameterValue": "offer-management-api"}, {"ParameterKey": "DBEngineVersion", "ParameterValue": "15.10"}, {"ParameterKey": "DBInstanceClass", "ParameterValue": "db.r5.large"}, {"ParameterKey": "DBName", "ParameterValue": "offermanagement"}, {"ParameterKey": "DBPort", "ParameterValue": "5432"}, {"ParameterKey": "DBSnapshotName", "ParameterValue": ""}, {"ParameterKey": "DBType", "ParameterValue": "postgres"}, {"ParameterKey": "DBUser", "ParameterValue": "db<PERSON><PERSON>"}, {"ParameterKey": "Environment", "ParameterValue": "prod"}, {"ParameterKey": "KMSKeyName", "ParameterValue": "amrpwl-prod-database"}, {"ParameterKey": "NetworkStackName", "ParameterValue": "AMRPWL-Prod"}, {"ParameterKey": "PagerDutyURLDB", "ParameterValue": "https://events.pagerduty.com/integration/174a7219edf0410eb0453b7bb4b41e22/enqueue"}, {"ParameterKey": "Project", "ParameterValue": "offers"}, {"ParameterKey": "ReplicaCpuThreshold", "ParameterValue": "60"}, {"ParameterKey": "ReplicaMaxCapacity", "ParameterValue": "5"}, {"ParameterKey": "ReplicaMinCapacity", "ParameterValue": "1"}, {"ParameterKey": "ReplicaScaleInCooldownInSeconds", "ParameterValue": "300"}, {"ParameterKey": "ReplicaScaleOutCooldownInSeconds", "ParameterValue": "120"}, {"ParameterKey": "SubnetId1", "ParameterValue": "subnet-0ede5927e64910695"}, {"ParameterKey": "SubnetId2", "ParameterValue": "subnet-082e28b71bcc164de"}, {"ParameterKey": "SubnetId3", "ParameterValue": "subnet-0372b7fb9669cca83"}, {"ParameterKey": "Team", "ParameterValue": "Things"}, {"ParameterKey": "VpcId", "ParameterValue": "vpc-09aa1ade9bdba1446"}]