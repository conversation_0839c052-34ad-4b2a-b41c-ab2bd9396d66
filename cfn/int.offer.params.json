[{"ParameterKey": "DBName", "ParameterValue": "offermanagement"}, {"ParameterKey": "DBUser", "ParameterValue": "db<PERSON><PERSON>"}, {"ParameterKey": "DBInstanceClass", "ParameterValue": "db.r5.large"}, {"ParameterKey": "DBType", "ParameterValue": "postgres"}, {"ParameterKey": "DBEngineVersion", "ParameterValue": "12.8"}, {"ParameterKey": "DBPort", "ParameterValue": "5432"}, {"ParameterKey": "Environment", "ParameterValue": "int"}, {"ParameterKey": "EnvironmentType", "ParameterValue": "nonprod"}, {"ParameterKey": "Project", "ParameterValue": "offers"}, {"ParameterKey": "Team", "ParameterValue": "Things"}, {"ParameterKey": "Component", "ParameterValue": "offer-management-api"}, {"ParameterKey": "VpcId", "ParameterValue": "vpc-08312799d98f0d197"}, {"ParameterKey": "SubnetId1", "ParameterValue": "subnet-0bacca686cdcf1244"}, {"ParameterKey": "SubnetId2", "ParameterValue": "subnet-03157d8c0a2e79c27"}, {"ParameterKey": "SubnetId3", "ParameterValue": "subnet-0204f38421808bac6"}, {"ParameterKey": "KMSKeyName", "ParameterValue": "amrpwl-nonprod-database"}, {"ParameterKey": "CIDRIpAddress", "ParameterValue": "**********/32"}, {"ParameterKey": "NetworkStackName", "ParameterValue": "AMRPWL-Int"}, {"ParameterKey": "PagerDutyURLAPI", "ParameterValue": "https://events.pagerduty.com/integration/d132264d93f747baa79ead1a770f1935/enqueue"}, {"ParameterKey": "PagerDutyURLDB", "ParameterValue": "https://events.pagerduty.com/integration/d132264d93f747baa79ead1a770f1935/enqueue"}, {"ParameterKey": "CpuUtilizationThreshold", "ParameterValue": "90"}]