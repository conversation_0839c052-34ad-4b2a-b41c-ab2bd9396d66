{"Environment": "sole", "SecureProxyEnvironment": "dev", "ClusterStackName": "dev-cpu-amrpwl-ecs", "Path": "/offer-management-api", "Priority": "421", "AwsRegion": "us-east-1", "AwsSecretId": "sole-offer-management-api-resources", "CgsEndpoint": "https://content-generation.dev.api.loyalty.com:2023/content-generation-service", "OfferApiV2Endpoint": "https://dev-services.api.loyalty.com/offer-management", "offerFacetsEndpoint": "https://offer-facets.dev.api.loyalty.com:40001/offer-facets", "offerPromotionsEndpoint": "https://promotions.dev.api.loyalty.com:40010/promotions", "partnersEndpoint": "https://partners.dev.api.loyalty.com:2223/partners", "offerDefaultImageBasePath": "https://dev-l1-amrpwl-post-images.s3.amazonaws.com/default-images/", "UrlPrefix": "offer-management-api", "NovaBusTableName": "sole-offer-management-api-event-store", "NovaBusRegion": "us-east-1", "NovaBusEndPoint": "https://dynamodb.us-east-1.amazonaws.com", "NLBListenerPort": "3024", "PagerDutyURLAPI": "https://events.pagerduty.com/integration/d132264d93f747baa79ead1a770f1935/enqueue", "NetworkStackName": "AMRPWL-Dev", "KinesisForSplunkStackName": "nonprod-kinesissplunk", "KinesisAccountId": "************", "KinesisUSEastAccountId": "************", "KinesisUSRegion": "us-east-1", "KMSKeyName": "amrpwl-nonprod-application", "ContainerTotalCpu": "1024", "AppMemory": "512", "ContainerTotalMemory": "2048", "AppCpu": "256", "AutoScaleHighThreshold": "70", "CpuUtilizationThreshold": "90", "MemoryUtilizationThreshold": "80", "HealthyHostThreshold": "1"}