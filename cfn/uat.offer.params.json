[{"ParameterKey": "DBName", "ParameterValue": "offermanagement"}, {"ParameterKey": "DBUser", "ParameterValue": "db<PERSON><PERSON>"}, {"ParameterKey": "DBInstanceClass", "ParameterValue": "db.r5.large"}, {"ParameterKey": "DBType", "ParameterValue": "postgres"}, {"ParameterKey": "DBEngineVersion", "ParameterValue": "12.8"}, {"ParameterKey": "DBPort", "ParameterValue": "5432"}, {"ParameterKey": "Environment", "ParameterValue": "uat"}, {"ParameterKey": "EnvironmentType", "ParameterValue": "nonprod"}, {"ParameterKey": "Project", "ParameterValue": "offers"}, {"ParameterKey": "Team", "ParameterValue": "Things"}, {"ParameterKey": "Component", "ParameterValue": "offer-management-api"}, {"ParameterKey": "VpcId", "ParameterValue": "vpc-0b23d99194bdaca42"}, {"ParameterKey": "SubnetId1", "ParameterValue": "subnet-0da721331bd4ea768"}, {"ParameterKey": "SubnetId2", "ParameterValue": "subnet-0cfc59f6f7bd1d03f"}, {"ParameterKey": "SubnetId3", "ParameterValue": "subnet-06e45ca89d7c915c9"}, {"ParameterKey": "KMSKeyName", "ParameterValue": "amrpwl-nonprod-database"}, {"ParameterKey": "CIDRIpAddress", "ParameterValue": "**********/32"}, {"ParameterKey": "NetworkStackName", "ParameterValue": "AMRPWL-UAT"}, {"ParameterKey": "PagerDutyURLAPI", "ParameterValue": "https://events.pagerduty.com/integration/d132264d93f747baa79ead1a770f1935/enqueue"}, {"ParameterKey": "PagerDutyURLDB", "ParameterValue": "https://events.pagerduty.com/integration/d132264d93f747baa79ead1a770f1935/enqueue"}, {"ParameterKey": "CpuUtilizationThreshold", "ParameterValue": "90"}]