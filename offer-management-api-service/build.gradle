apply plugin: 'kotlin-spring'
apply plugin: 'eclipse'
apply plugin: 'com.palantir.docker'
apply plugin: 'org.springframework.boot'
apply plugin: 'com.adarshr.test-logger'

repositories {
    maven { url 'https://jitpack.io' }
}

configurations {
    cucumberRuntime {
        extendsFrom testRuntime
    }
}

dependencies {
    implementation(
            'com.amazonaws:aws-java-sdk-core',
            'com.amazonaws:aws-java-sdk-dynamodb',
            'com.amazonaws:aws-java-sdk-secretsmanager',
            'com.fasterxml.jackson.core:jackson-databind',
            'com.fasterxml.jackson.module:jackson-module-kotlin',
            'com.github.everit-org.json-schema:org.everit.json.schema:1.11.0',
            'com.h2database:h2:1.4.199',
            'com.loyalty.nova:nova-common',
            'io.springfox:springfox-swagger-ui',
            'io.springfox:springfox-swagger2',
            'javax.validation:validation-api:2.0.1.Final',
            'org.flywaydb:flyway-core',
            'org.jetbrains.kotlin:kotlin-reflect',
            'org.jetbrains.kotlin:kotlin-stdlib-jdk8',
            'org.springframework.boot:spring-boot-starter-data-jpa',
            'org.springframework.boot:spring-boot-starter-data-rest',
            'org.springframework.boot:spring-boot-starter-web',
            'org.springframework.cloud:spring-cloud-function-kotlin',
            'org.springframework.cloud:spring-cloud-starter-function-web',
            'org.postgresql:postgresql',
            'com.amazonaws:aws-java-sdk-sts',
            'com.amazonaws:aws-java-sdk-kinesis',
            "software.amazon.awssdk:sts",
            "software.amazon.awssdk:auth"
    )

    compile("javax.servlet:javax.servlet-api:4.0.1")
    testCompile project(':offer-management-api-test-utils')

    testImplementation(
            'com.loyalty.nova:nova-common-test',
            'io.cucumber:cucumber-junit',
            'io.cucumber:cucumber-java8',
            'io.cucumber:cucumber-spring',
            'org.mockito:mockito-inline:2.13.0',
            'org.apache.jmeter:ApacheJMeter_core',
            'org.apache.jmeter:ApacheJMeter_http',
            'kg.apc:jmeter-plugins-casutg',
            'com.jayway.jsonpath:json-path:2.4.0',
            'org.jtwig:jtwig-core:5.87.0.RELEASE'
    )
}

/* Copy the native files */
task copyNativeDeps(type: Copy) {
    from(configurations.compile + configurations.testCompile) {
        include "*.dylib"
        include "*.so"
        include "*.dll"
    }
    into 'build/libs'
}

test {
    useJUnitPlatform {
        includeEngines 'junit-jupiter'
        excludeEngines 'junit-vintage'
    }
    jacoco {
        destinationFile = file("${buildDir}/jacoco/test.exec")
    }

    systemProperty 'java.library.path', 'build/libs'
    systemProperty 'spring.flyway.enabled', 'false'
    environment 'AWS_ACCESS_KEY_ID', 'applmgr'
    environment 'AWS_DEFAULT_REGION', 'us-east-1'
    environment 'AWS_SECRET_ACCESS_KEY', 'applmgr'
    environment 'URL_PREFIX', 'offer-management-api'
    environment 'CGS_ENDPOINT', 'http://localhost:9090/content-generation-service'
    environment 'OFFER_API_V2_ENDPOINT', 'http://localhost:9090/offer-management'
    environment 'OFFER_FACETS_ENDPOINT', 'http://localhost:9090/offer-facets'
    environment 'OFFER_PROMOTIONS_ENDPOINT', 'http://localhost:9090/promotion-service'
    environment 'PARTNERS_ENDPOINT', 'http://localhost:9090/partners'
    environment 'OFFER_DEFAULT_IMAGE_BASE', 'https://dev-post-public.s3.amazonaws.com/images/default/'
    environment 'NOVA_BUS_ENDPOINT', 'http://localhost:8000'
    environment 'NOVA_BUS_REGION', 'us-east-1'
    environment 'NOVA_BUS_TABLE_NAME', 'eventsTable'
    environment 'SPRING_DATASOURCE_USERNAME', 'sa'
    environment 'SPRING_DATASOURCE_URL', 'jdbc:h2:mem:memorydb'
    environment 'FEATURE_TOGGLE_STREAM_EVENT_V3', 'true'
    environment 'FEATURE_TOGGLE_STREAM_EVENT_V2', 'false'
    environment 'FEATURE_TOGGLE_CLO', 'false'
    environment 'FEATURE_TOGGLE_AM_RECEIPTS', 'false'
    environment 'Environment', 'local'
    environment 'KinesisName', 'dev-nova-offer-published-eventbus'
    environment 'KinesisRegion', 'ca-central-1'
    environment 'KinesisAccountId', '************'
    environment 'KinesisNameForUSEast', 'dev-nova-event-bus-offer-event-stream'
    environment 'KinesisUSRegion', 'us-east-1'
    environment 'KinesisUSEastAccountId', '************'

}

testlogger {
    theme 'mocha'
    showExceptions true
    slowThreshold 2000
    showSummary true
    showPassed true
    showSkipped true
    showFailed true
    showStandardStreams false
    showPassedStandardStreams true
    showSkippedStandardStreams true
    showFailedStandardStreams true
}

task copyJarToTmp(type: Copy, dependsOn: [jar]) {
    copy {
        from "${buildDir}/libs/offer-management-api-service-${version}.jar"
        into "${buildDir}/tmp"
        rename("offer-management-api-service-${version}.jar", "offer-management-api-service.jar")
    }
}

docker {
    dependsOn build
    name "************.dkr.ecr.us-east-1.amazonaws.com/${bootJar.baseName}:${project.version}"
    files bootJar.archivePath
    buildArgs(['JAR_FILE': "${bootJar.archiveName}"])
}

task publish {
    dependsOn dockerPush
}

compileKotlin.dependsOn copyNativeDeps
