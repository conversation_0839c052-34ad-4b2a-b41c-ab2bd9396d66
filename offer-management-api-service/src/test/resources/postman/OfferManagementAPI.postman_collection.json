{"id": "a24e3f55-51f0-4333-9908-87da6ed07f7b", "name": "OfferManagementAPI", "description": "", "auth": null, "events": null, "variables": [], "order": ["12a901f5-6c0f-42d4-944f-7f0cdd583fc0", "0cec244a-97ed-42dc-a9ab-addd3ee75498", "09cfd957-f10c-4d23-8f0c-b0c62f61a349", "620d2211-0383-43d0-843f-84e6c98cfbf6", "c4989688-c902-4c1a-8b40-d43be01f0d4a", "91b633d4-5680-4432-9530-2f262bbb755c", "41379a49-9c23-4695-a45f-305c2bb96719", "581f63af-7898-46a4-ab39-65f6c1327a03", "d1420b5a-0f43-4bf2-8892-b8a64778a7ee", "02ff3e5b-1b27-47ca-bbda-7ab11e62b9db", "3459babc-be43-4a0a-b629-adf99d809efe", "0b25c021-6a2b-43f5-b84e-522664663c07", "fb6cf10e-2635-407f-bbf3-e007447f63f7", "bcdf681a-573e-4103-9837-01606bd0b629", "74ddd7d9-a5bd-4536-8daf-3afbb7a0c7be", "4625c5e5-d4a9-4e99-88ac-e3f7f67e65e4", "991fb3ff-b9d0-4620-a85c-fc968b8e6441", "8f059c1d-08ed-4951-a6f5-081c58675caf", "57ddcb04-64e7-416b-b409-d7aa4e79d87f", "76996a29-abb3-4ee8-802e-b92025eb1ddc", "54195f71-0369-4894-bcf4-1fdb50914cec", "8deefbda-5466-4e22-809f-e4c3faa57f6b", "781177a6-6dd0-47be-9525-02a213885840", "d7bbdbbb-eb30-4d5b-9ccf-1491abdda0c4", "4c2e39da-06cf-4b65-97a7-f9ac85c7f5f8"], "folders_order": [], "protocolProfileBehavior": {}, "folders": [], "requests": [{"id": "02ff3e5b-1b27-47ca-bbda-7ab11e62b9db", "name": "Bulk Create 3 Bad Request", "url": "{{basePath}}/offer-management-api/v3/offers/bulk", "description": "", "data": [], "dataOptions": null, "dataMode": "raw", "headerData": [{"key": "Content-Type", "value": "application/json", "enabled": true}, {"key": "x-user-email", "value": "<EMAIL>", "enabled": true}], "method": "POST", "pathVariableData": [], "queryParams": null, "auth": null, "events": null, "folder": null, "rawModeData": "{\n  \"bulkName\": \"TestBatch\",\n  \"offers\": [\n    {\n      \"partnerId\": \"3db0b3e0-4134-453e-8381-08e90f2b5141\",\n      \"partnerName\": \"Rexall\",\n      \"offerType\": \"spend\",\n      \"qualifier\": \"storewide\",\n      \"awardType\": \"flatMiles\",\n      \"displayDate\": \"2019-02-22T00:00:00Z\",\n      \"startDate\": \"2019-02-22T00:00:00Z\",\n      \"endDate\": \"2019-02-22T23:59:00Z\",\n      \"displayPriority\": 0,\n      \"regions\": [\n        \"AB\",\n        \"BC\",\n        \"MB\",\n        \"NT\",\n        \"ON\",\n        \"SK\"\n      ],\n      \"image\": {\n        \"en-US\": {\n          \"path\": \"https://dev-post-public.s3.amazonaws.com/images/default/spend.png\"\n        }\n      },\n      \"tiers\": [\n        {\n          \"qualifierValue\": 10,\n          \"awardValue\": 10\n        },\n        {\n          \"qualifierValue\": 20,\n          \"awardValue\": 20\n        },\n        {\n          \"qualifierValue\": 30,\n          \"awardValue\": 30\n        }\n      ],\n      \"canBeCombined\": true,\n      \"availability\": [\n        \"inStore\"\n      ],\n      \"offerLimitation\": \"noLimit\",\n      \"hasCustomLegal\": false,\n      \"mechanisms\": [\n        {\n          \"mechanismType\": \"noAction\"\n        }\n      ]\n    },\n    {\n      \"partnerId\": \"3db0b3e0-4134-453e-8381-08e90f2b5141\",\n      \"partnerName\": \"Rexall\",\n      \"offerType\": \"spend\",\n      \"qualifier\": \"storewide\",\n      \"awardType\": \"flatMiles\",\n      \"displayDate\": \"2019-02-22T00:00:00Z\",\n      \"startDate\": \"2019-02-22T00:00:00Z\",\n      \"endDate\": \"2019-02-22T23:59:00Z\",\n      \"displayPriority\": 0,\n      \"regions\": [\n        \"AB\",\n        \"BC\",\n        \"MB\",\n        \"NT\",\n        \"ON\",\n        \"SK\"\n      ],\n      \"image\": {\n        \"en-US\": {\n          \"path\": \"https://dev-post-public.s3.amazonaws.com/images/default/spend.png\"\n        }\n      },\n      \"tiers\": [\n        {\n          \"qualifierValue\": 40,\n          \"awardValue\": 40\n        },\n        {\n          \"qualifierValue\": 50,\n          \"awardValue\": 50\n        },\n        {\n          \"qualifierValue\": 60,\n          \"awardValue\": 60\n        }\n      ],\n      \"canBeCombined\": true,\n      \"availability\": [\n        \"inStore\"\n      ],\n      \"offerLimitation\": \"noLimit\",\n      \"hasCustomLegal\": false,\n      \"mechanisms\": [\n        {\n          \"mechanismType\": \"noAction\"\n        }\n      ]\n    },\n    {\n      \"partnerId\": \"3db0b3e0-4134-453e-8381-08e90f2b5141\",\n      \"partnerName\": \"Rexall\",\n      \"offerType\": \"spend\",\n      \"qualifier\": \"storewide\",\n      \"awardType\": \"flatMiles\",\n      \"displayDate\": \"2019-02-22T00:00:00Z\",\n      \"startDate\": \"2019-02-22T00:00:00Z\",\n      \"endDate\": \"2019-02-22T23:59:00Z\",\n      \"displayPriority\": 10000,\n      \"regions\": [\n        \"AB\",\n        \"BC\",\n        \"MB\",\n        \"NT\",\n        \"ON\",\n        \"SK\"\n      ],\n      \"image\": {\n        \"en-US\": {\n          \"path\": \"https://dev-post-public.s3.amazonaws.com/images/default/spend.png\"\n        }\n      },\n      \"tiers\": [\n        {\n          \"qualifierValue\": 60,\n          \"awardValue\": 60\n        },\n        {\n          \"qualifierValue\": 70,\n          \"awardValue\": 70\n        },\n        {\n          \"qualifierValue\": 80,\n          \"awardValue\": 80\n        }\n      ],\n      \"canBeCombined\": true,\n      \"availability\": [\n        \"inStore\"\n      ],\n      \"offerLimitation\": \"noLimit\",\n      \"hasCustomLegal\": false,\n      \"mechanisms\": [\n        {\n          \"mechanismType\": \"noAction\"\n        }\n      ]\n    }\n  ]\n}", "headers": "Content-Type: application/json\nx-user-email: <EMAIL>\n", "pathVariables": {}}, {"id": "09cfd957-f10c-4d23-8f0c-b0c62f61a349", "name": "Get Offer By Id", "url": "{{basePath}}/offer-management-api/v3/offers/{{offerId}}", "description": "", "data": null, "dataOptions": null, "dataMode": null, "headerData": [{"key": "Content-Type", "value": "application/json", "enabled": true}, {"key": "x-user-email", "value": "<EMAIL>", "enabled": true}], "method": "GET", "pathVariableData": [], "queryParams": null, "auth": null, "events": null, "folder": null, "headers": "Content-Type: application/json\nx-user-email: <EMAIL>\n", "pathVariables": {}}, {"id": "0b25c021-6a2b-43f5-b84e-522664663c07", "name": "Bulk Create 50 Offers - One Bad Offer", "url": "{{basePath}}/offer-management-api/v3/offers/bulk", "description": "", "data": [], "dataOptions": null, "dataMode": "raw", "headerData": [{"key": "Content-Type", "value": "application/json", "enabled": true}, {"key": "x-user-email", "value": "<EMAIL>", "enabled": true}], "method": "POST", "pathVariableData": [], "queryParams": null, "auth": null, "events": null, "folder": null, "rawModeData": "{\n  \"bulkName\": \"TestBatch\",\n  \"offers\": [\n    {\n      \"partnerId\": \"3db0b3e0-4134-453e-8381-08e90f2b5141\",\n      \"partnerName\": \"Rexall\",\n      \"offerType\": \"spend\",\n      \"qualifier\": \"storewide\",\n      \"awardType\": \"flatMiles\",\n      \"displayDate\": \"2019-02-22T00:00:00Z\",\n      \"startDate\": \"2019-02-22T00:00:00Z\",\n      \"endDate\": \"2019-02-22T23:59:00Z\",\n      \"displayPriority\": 0,\n      \"regions\": [\n        \"AB\",\n        \"BC\",\n        \"MB\",\n        \"NT\",\n        \"ON\",\n        \"SK\"\n      ],\n      \"image\": {\n        \"en-US\": {\n          \"path\": \"https://dev-post-public.s3.amazonaws.com/images/default/spend.png\"\n        }\n      },\n      \"tiers\": [\n        {\n          \"qualifierValue\": 10,\n          \"awardValue\": 10\n        },\n        {\n          \"qualifierValue\": 20,\n          \"awardValue\": 20\n        },\n        {\n          \"qualifierValue\": 30,\n          \"awardValue\": 30\n        }\n      ],\n      \"canBeCombined\": true,\n      \"availability\": [\n        \"inStore\"\n      ],\n      \"offerLimitation\": \"noLimit\",\n      \"hasCustomLegal\": false,\n      \"mechanisms\": [\n        {\n          \"mechanismType\": \"noAction\"\n        }\n      ]\n    },\n    {\n      \"partnerId\": \"3db0b3e0-4134-453e-8381-08e90f2b5141\",\n      \"partnerName\": \"Rexall\",\n      \"offerType\": \"spend\",\n      \"qualifier\": \"storewide\",\n      \"awardType\": \"flatMiles\",\n      \"displayDate\": \"2019-02-22T00:00:00Z\",\n      \"startDate\": \"2019-02-22T00:00:00Z\",\n      \"endDate\": \"2019-02-22T23:59:00Z\",\n      \"displayPriority\": 0,\n      \"regions\": [\n        \"AB\",\n        \"BC\",\n        \"MB\",\n        \"NT\",\n        \"ON\",\n        \"SK\"\n      ],\n      \"image\": {\n        \"en-US\": {\n          \"path\": \"https://dev-post-public.s3.amazonaws.com/images/default/spend.png\"\n        }\n      },\n      \"tiers\": [\n        {\n          \"qualifierValue\": 10,\n          \"awardValue\": 10\n        },\n        {\n          \"qualifierValue\": 20,\n          \"awardValue\": 20\n        },\n        {\n          \"qualifierValue\": 30,\n          \"awardValue\": 30\n        }\n      ],\n      \"canBeCombined\": true,\n      \"availability\": [\n        \"inStore\"\n      ],\n      \"offerLimitation\": \"noLimit\",\n      \"hasCustomLegal\": false,\n      \"mechanisms\": [\n        {\n          \"mechanismType\": \"noAction\"\n        }\n      ]\n    },\n    {\n      \"partnerId\": \"3db0b3e0-4134-453e-8381-08e90f2b5141\",\n      \"partnerName\": \"Rexall\",\n      \"offerType\": \"spend\",\n      \"qualifier\": \"storewide\",\n      \"awardType\": \"flatMiles\",\n      \"displayDate\": \"2019-02-22T00:00:00Z\",\n      \"startDate\": \"2019-02-22T00:00:00Z\",\n      \"endDate\": \"2019-02-22T23:59:00Z\",\n      \"displayPriority\": 0,\n      \"regions\": [\n        \"AB\",\n        \"BC\",\n        \"MB\",\n        \"NT\",\n        \"ON\",\n        \"SK\"\n      ],\n      \"image\": {\n        \"en-US\": {\n          \"path\": \"https://dev-post-public.s3.amazonaws.com/images/default/spend.png\"\n        }\n      },\n      \"tiers\": [\n        {\n          \"qualifierValue\": 10,\n          \"awardValue\": 10\n        },\n        {\n          \"qualifierValue\": 20,\n          \"awardValue\": 20\n        },\n        {\n          \"qualifierValue\": 30,\n          \"awardValue\": 30\n        }\n      ],\n      \"canBeCombined\": true,\n      \"availability\": [\n        \"inStore\"\n      ],\n      \"offerLimitation\": \"noLimit\",\n      \"hasCustomLegal\": false,\n      \"mechanisms\": [\n        {\n          \"mechanismType\": \"noAction\"\n        }\n      ]\n    },\n    {\n      \"partnerId\": \"3db0b3e0-4134-453e-8381-08e90f2b5141\",\n      \"partnerName\": \"Rexall\",\n      \"offerType\": \"spend\",\n      \"qualifier\": \"storewide\",\n      \"awardType\": \"flatMiles\",\n      \"displayDate\": \"2019-02-22T00:00:00Z\",\n      \"startDate\": \"2019-02-22T00:00:00Z\",\n      \"endDate\": \"2019-02-22T23:59:00Z\",\n      \"displayPriority\": 0,\n      \"regions\": [\n        \"AB\",\n        \"BC\",\n        \"MB\",\n        \"NT\",\n        \"ON\",\n        \"SK\"\n      ],\n      \"image\": {\n        \"en-US\": {\n          \"path\": \"https://dev-post-public.s3.amazonaws.com/images/default/spend.png\"\n        }\n      },\n      \"tiers\": [\n        {\n          \"qualifierValue\": 10,\n          \"awardValue\": 10\n        },\n        {\n          \"qualifierValue\": 20,\n          \"awardValue\": 20\n        },\n        {\n          \"qualifierValue\": 30,\n          \"awardValue\": 30\n        }\n      ],\n      \"canBeCombined\": true,\n      \"availability\": [\n        \"inStore\"\n      ],\n      \"offerLimitation\": \"noLimit\",\n      \"hasCustomLegal\": false,\n      \"mechanisms\": [\n        {\n          \"mechanismType\": \"noAction\"\n        }\n      ]\n    },\n    {\n      \"partnerId\": \"3db0b3e0-4134-453e-8381-08e90f2b5141\",\n      \"partnerName\": \"Rexall\",\n      \"offerType\": \"spend\",\n      \"qualifier\": \"storewide\",\n      \"awardType\": \"flatMiles\",\n      \"displayDate\": \"2019-02-22T00:00:00Z\",\n      \"startDate\": \"2019-02-22T00:00:00Z\",\n      \"endDate\": \"2019-02-22T23:59:00Z\",\n      \"displayPriority\": 0,\n      \"regions\": [\n        \"AB\",\n        \"BC\",\n        \"MB\",\n        \"NT\",\n        \"ON\",\n        \"SK\"\n      ],\n      \"image\": {\n        \"en-US\": {\n          \"path\": \"https://dev-post-public.s3.amazonaws.com/images/default/spend.png\"\n        }\n      },\n      \"tiers\": [\n        {\n          \"qualifierValue\": 10,\n          \"awardValue\": 10\n        },\n        {\n          \"qualifierValue\": 20,\n          \"awardValue\": 20\n        },\n        {\n          \"qualifierValue\": 30,\n          \"awardValue\": 30\n        }\n      ],\n      \"canBeCombined\": true,\n      \"availability\": [\n        \"inStore\"\n      ],\n      \"offerLimitation\": \"noLimit\",\n      \"hasCustomLegal\": false,\n      \"mechanisms\": [\n        {\n          \"mechanismType\": \"noAction\"\n        }\n      ]\n    },\n    {\n      \"partnerId\": \"3db0b3e0-4134-453e-8381-08e90f2b5141\",\n      \"partnerName\": \"Rexall\",\n      \"offerType\": \"spend\",\n      \"qualifier\": \"storewide\",\n      \"awardType\": \"flatMiles\",\n      \"displayDate\": \"2019-02-22T00:00:00Z\",\n      \"startDate\": \"2019-02-22T00:00:00Z\",\n      \"endDate\": \"2019-02-22T23:59:00Z\",\n      \"displayPriority\": 0,\n      \"regions\": [\n        \"AB\",\n        \"BC\",\n        \"MB\",\n        \"NT\",\n        \"ON\",\n        \"SK\"\n      ],\n      \"image\": {\n        \"en-US\": {\n          \"path\": \"https://dev-post-public.s3.amazonaws.com/images/default/spend.png\"\n        }\n      },\n      \"tiers\": [\n        {\n          \"qualifierValue\": 10,\n          \"awardValue\": 10\n        },\n        {\n          \"qualifierValue\": 20,\n          \"awardValue\": 20\n        },\n        {\n          \"qualifierValue\": 30,\n          \"awardValue\": 30\n        }\n      ],\n      \"canBeCombined\": true,\n      \"availability\": [\n        \"inStore\"\n      ],\n      \"offerLimitation\": \"noLimit\",\n      \"hasCustomLegal\": false,\n      \"mechanisms\": [\n        {\n          \"mechanismType\": \"noAction\"\n        }\n      ]\n    },\n    {\n      \"partnerId\": \"3db0b3e0-4134-453e-8381-08e90f2b5141\",\n      \"partnerName\": \"Rexall\",\n      \"offerType\": \"spend\",\n      \"qualifier\": \"storewide\",\n      \"awardType\": \"flatMiles\",\n      \"displayDate\": \"2019-02-22T00:00:00Z\",\n      \"startDate\": \"2019-02-22T00:00:00Z\",\n      \"endDate\": \"2019-02-22T23:59:00Z\",\n      \"displayPriority\": 0,\n      \"regions\": [\n        \"AB\",\n        \"BC\",\n        \"MB\",\n        \"NT\",\n        \"ON\",\n        \"SK\"\n      ],\n      \"image\": {\n        \"en-US\": {\n          \"path\": \"https://dev-post-public.s3.amazonaws.com/images/default/spend.png\"\n        }\n      },\n      \"tiers\": [\n        {\n          \"qualifierValue\": 10,\n          \"awardValue\": 10\n        },\n        {\n          \"qualifierValue\": 20,\n          \"awardValue\": 20\n        },\n        {\n          \"qualifierValue\": 30,\n          \"awardValue\": 30\n        }\n      ],\n      \"canBeCombined\": true,\n      \"availability\": [\n        \"inStore\"\n      ],\n      \"offerLimitation\": \"noLimit\",\n      \"hasCustomLegal\": false,\n      \"mechanisms\": [\n        {\n          \"mechanismType\": \"noAction\"\n        }\n      ]\n    },\n    {\n      \"partnerId\": \"3db0b3e0-4134-453e-8381-08e90f2b5141\",\n      \"partnerName\": \"Rexall\",\n      \"offerType\": \"spend\",\n      \"qualifier\": \"storewide\",\n      \"awardType\": \"flatMiles\",\n      \"displayDate\": \"2019-02-22T00:00:00Z\",\n      \"startDate\": \"2019-02-22T00:00:00Z\",\n      \"endDate\": \"2019-02-22T23:59:00Z\",\n      \"displayPriority\": 0,\n      \"regions\": [\n        \"AB\",\n        \"BC\",\n        \"MB\",\n        \"NT\",\n        \"ON\",\n        \"SK\"\n      ],\n      \"image\": {\n        \"en-US\": {\n          \"path\": \"https://dev-post-public.s3.amazonaws.com/images/default/spend.png\"\n        }\n      },\n      \"tiers\": [\n        {\n          \"qualifierValue\": 10,\n          \"awardValue\": 10\n        },\n        {\n          \"qualifierValue\": 20,\n          \"awardValue\": 20\n        },\n        {\n          \"qualifierValue\": 30,\n          \"awardValue\": 30\n        }\n      ],\n      \"canBeCombined\": true,\n      \"availability\": [\n        \"inStore\"\n      ],\n      \"offerLimitation\": \"noLimit\",\n      \"hasCustomLegal\": false,\n      \"mechanisms\": [\n        {\n          \"mechanismType\": \"noAction\"\n        }\n      ]\n    },\n    {\n      \"partnerId\": \"3db0b3e0-4134-453e-8381-08e90f2b5141\",\n      \"partnerName\": \"Rexall\",\n      \"offerType\": \"spend\",\n      \"qualifier\": \"storewide\",\n      \"awardType\": \"flatMiles\",\n      \"displayDate\": \"2019-02-22T00:00:00Z\",\n      \"startDate\": \"2019-02-22T00:00:00Z\",\n      \"endDate\": \"2019-02-22T23:59:00Z\",\n      \"displayPriority\": 0,\n      \"regions\": [\n        \"AB\",\n        \"BC\",\n        \"MB\",\n        \"NT\",\n        \"ON\",\n        \"SK\"\n      ],\n      \"image\": {\n        \"en-US\": {\n          \"path\": \"https://dev-post-public.s3.amazonaws.com/images/default/spend.png\"\n        }\n      },\n      \"tiers\": [\n        {\n          \"qualifierValue\": 10,\n          \"awardValue\": 10\n        },\n        {\n          \"qualifierValue\": 20,\n          \"awardValue\": 20\n        },\n        {\n          \"qualifierValue\": 30,\n          \"awardValue\": 30\n        }\n      ],\n      \"canBeCombined\": true,\n      \"availability\": [\n        \"inStore\"\n      ],\n      \"offerLimitation\": \"noLimit\",\n      \"hasCustomLegal\": false,\n      \"mechanisms\": [\n        {\n          \"mechanismType\": \"noAction\"\n        }\n      ]\n    },\n    {\n      \"partnerId\": \"3db0b3e0-4134-453e-8381-08e90f2b5141\",\n      \"partnerName\": \"Rexall\",\n      \"offerType\": \"spend\",\n      \"qualifier\": \"storewide\",\n      \"awardType\": \"flatMiles\",\n      \"displayDate\": \"2019-02-22T00:00:00Z\",\n      \"startDate\": \"2019-02-22T00:00:00Z\",\n      \"endDate\": \"2019-02-22T23:59:00Z\",\n      \"displayPriority\": 0,\n      \"regions\": [\n        \"AB\",\n        \"BC\",\n        \"MB\",\n        \"NT\",\n        \"ON\",\n        \"SK\"\n      ],\n      \"image\": {\n        \"en-US\": {\n          \"path\": \"https://dev-post-public.s3.amazonaws.com/images/default/spend.png\"\n        }\n      },\n      \"tiers\": [\n        {\n          \"qualifierValue\": 10,\n          \"awardValue\": 10\n        },\n        {\n          \"qualifierValue\": 20,\n          \"awardValue\": 20\n        },\n        {\n          \"qualifierValue\": 30,\n          \"awardValue\": 30\n        }\n      ],\n      \"canBeCombined\": true,\n      \"availability\": [\n        \"inStore\"\n      ],\n      \"offerLimitation\": \"noLimit\",\n      \"hasCustomLegal\": false,\n      \"mechanisms\": [\n        {\n          \"mechanismType\": \"noAction\"\n        }\n      ]\n    },\n    {\n      \"partnerId\": \"3db0b3e0-4134-453e-8381-08e90f2b5141\",\n      \"partnerName\": \"Rexall\",\n      \"offerType\": \"spend\",\n      \"qualifier\": \"storewide\",\n      \"awardType\": \"flatMiles\",\n      \"displayDate\": \"2019-02-22T00:00:00Z\",\n      \"startDate\": \"2019-02-22T00:00:00Z\",\n      \"endDate\": \"2019-02-22T23:59:00Z\",\n      \"displayPriority\": 0,\n      \"regions\": [\n        \"AB\",\n        \"BC\",\n        \"MB\",\n        \"NT\",\n        \"ON\",\n        \"SK\"\n      ],\n      \"image\": {\n        \"en-US\": {\n          \"path\": \"https://dev-post-public.s3.amazonaws.com/images/default/spend.png\"\n        }\n      },\n      \"tiers\": [\n        {\n          \"qualifierValue\": 10,\n          \"awardValue\": 10\n        },\n        {\n          \"qualifierValue\": 20,\n          \"awardValue\": 20\n        },\n        {\n          \"qualifierValue\": 30,\n          \"awardValue\": 30\n        }\n      ],\n      \"canBeCombined\": true,\n      \"availability\": [\n        \"inStore\"\n      ],\n      \"offerLimitation\": \"noLimit\",\n      \"hasCustomLegal\": false,\n      \"mechanisms\": [\n        {\n          \"mechanismType\": \"noAction\"\n        }\n      ]\n    },\n    {\n      \"partnerId\": \"3db0b3e0-4134-453e-8381-08e90f2b5141\",\n      \"partnerName\": \"Rexall\",\n      \"offerType\": \"spend\",\n      \"qualifier\": \"storewide\",\n      \"awardType\": \"flatMiles\",\n      \"displayDate\": \"2019-02-22T00:00:00Z\",\n      \"startDate\": \"2019-02-22T00:00:00Z\",\n      \"endDate\": \"2019-02-22T23:59:00Z\",\n      \"displayPriority\": 0,\n      \"regions\": [\n        \"AB\",\n        \"BC\",\n        \"MB\",\n        \"NT\",\n        \"ON\",\n        \"SK\"\n      ],\n      \"image\": {\n        \"en-US\": {\n          \"path\": \"https://dev-post-public.s3.amazonaws.com/images/default/spend.png\"\n        }\n      },\n      \"tiers\": [\n        {\n          \"qualifierValue\": 10,\n          \"awardValue\": 10\n        },\n        {\n          \"qualifierValue\": 20,\n          \"awardValue\": 20\n        },\n        {\n          \"qualifierValue\": 30,\n          \"awardValue\": 30\n        }\n      ],\n      \"canBeCombined\": true,\n      \"availability\": [\n        \"inStore\"\n      ],\n      \"offerLimitation\": \"noLimit\",\n      \"hasCustomLegal\": false,\n      \"mechanisms\": [\n        {\n          \"mechanismType\": \"noAction\"\n        }\n      ]\n    },\n    {\n      \"partnerId\": \"3db0b3e0-4134-453e-8381-08e90f2b5141\",\n      \"partnerName\": \"Rexall\",\n      \"offerType\": \"spend\",\n      \"qualifier\": \"storewide\",\n      \"awardType\": \"flatMiles\",\n      \"displayDate\": \"2019-02-22T00:00:00Z\",\n      \"startDate\": \"2019-02-22T00:00:00Z\",\n      \"endDate\": \"2019-02-22T23:59:00Z\",\n      \"displayPriority\": 0,\n      \"regions\": [\n        \"AB\",\n        \"BC\",\n        \"MB\",\n        \"NT\",\n        \"ON\",\n        \"SK\"\n      ],\n      \"image\": {\n        \"en-US\": {\n          \"path\": \"https://dev-post-public.s3.amazonaws.com/images/default/spend.png\"\n        }\n      },\n      \"tiers\": [\n        {\n          \"qualifierValue\": 10,\n          \"awardValue\": 10\n        },\n        {\n          \"qualifierValue\": 20,\n          \"awardValue\": 20\n        },\n        {\n          \"qualifierValue\": 30,\n          \"awardValue\": 30\n        }\n      ],\n      \"canBeCombined\": true,\n      \"availability\": [\n        \"inStore\"\n      ],\n      \"offerLimitation\": \"noLimit\",\n      \"hasCustomLegal\": false,\n      \"mechanisms\": [\n        {\n          \"mechanismType\": \"noAction\"\n        }\n      ]\n    },\n    {\n      \"partnerId\": \"3db0b3e0-4134-453e-8381-08e90f2b5141\",\n      \"partnerName\": \"Rexall\",\n      \"offerType\": \"spend\",\n      \"qualifier\": \"storewide\",\n      \"awardType\": \"flatMiles\",\n      \"displayDate\": \"2019-02-22T00:00:00Z\",\n      \"startDate\": \"2019-02-22T00:00:00Z\",\n      \"endDate\": \"2019-02-22T23:59:00Z\",\n      \"displayPriority\": 0,\n      \"regions\": [\n        \"AB\",\n        \"BC\",\n        \"MB\",\n        \"NT\",\n        \"ON\",\n        \"SK\"\n      ],\n      \"image\": {\n        \"en-US\": {\n          \"path\": \"https://dev-post-public.s3.amazonaws.com/images/default/spend.png\"\n        }\n      },\n      \"tiers\": [\n        {\n          \"qualifierValue\": 10,\n          \"awardValue\": 10\n        },\n        {\n          \"qualifierValue\": 20,\n          \"awardValue\": 20\n        },\n        {\n          \"qualifierValue\": 30,\n          \"awardValue\": 30\n        }\n      ],\n      \"canBeCombined\": true,\n      \"availability\": [\n        \"inStore\"\n      ],\n      \"offerLimitation\": \"noLimit\",\n      \"hasCustomLegal\": false,\n      \"mechanisms\": [\n        {\n          \"mechanismType\": \"noAction\"\n        }\n      ]\n    },\n    {\n      \"partnerId\": \"3db0b3e0-4134-453e-8381-08e90f2b5141\",\n      \"partnerName\": \"Rexall\",\n      \"offerType\": \"spend\",\n      \"qualifier\": \"storewide\",\n      \"awardType\": \"flatMiles\",\n      \"displayDate\": \"2019-02-22T00:00:00Z\",\n      \"startDate\": \"2019-02-22T00:00:00Z\",\n      \"endDate\": \"2019-02-22T23:59:00Z\",\n      \"displayPriority\": 0,\n      \"regions\": [\n        \"AB\",\n        \"BC\",\n        \"MB\",\n        \"NT\",\n        \"ON\",\n        \"SK\"\n      ],\n      \"image\": {\n        \"en-US\": {\n          \"path\": \"https://dev-post-public.s3.amazonaws.com/images/default/spend.png\"\n        }\n      },\n      \"tiers\": [\n        {\n          \"qualifierValue\": 10,\n          \"awardValue\": 10\n        },\n        {\n          \"qualifierValue\": 20,\n          \"awardValue\": 20\n        },\n        {\n          \"qualifierValue\": 30,\n          \"awardValue\": 30\n        }\n      ],\n      \"canBeCombined\": true,\n      \"availability\": [\n        \"inStore\"\n      ],\n      \"offerLimitation\": \"noLimit\",\n      \"hasCustomLegal\": false,\n      \"mechanisms\": [\n        {\n          \"mechanismType\": \"noAction\"\n        }\n      ]\n    },\n    {\n      \"partnerId\": \"3db0b3e0-4134-453e-8381-08e90f2b5141\",\n      \"partnerName\": \"Rexall\",\n      \"offerType\": \"spend\",\n      \"qualifier\": \"storewide\",\n      \"awardType\": \"flatMiles\",\n      \"displayDate\": \"2019-02-22T00:00:00Z\",\n      \"startDate\": \"2019-02-22T00:00:00Z\",\n      \"endDate\": \"2019-02-22T23:59:00Z\",\n      \"displayPriority\": 0,\n      \"regions\": [\n        \"AB\",\n        \"BC\",\n        \"MB\",\n        \"NT\",\n        \"ON\",\n        \"SK\"\n      ],\n      \"image\": {\n        \"en-US\": {\n          \"path\": \"https://dev-post-public.s3.amazonaws.com/images/default/spend.png\"\n        }\n      },\n      \"tiers\": [\n        {\n          \"qualifierValue\": 10,\n          \"awardValue\": 10\n        },\n        {\n          \"qualifierValue\": 20,\n          \"awardValue\": 20\n        },\n        {\n          \"qualifierValue\": 30,\n          \"awardValue\": 30\n        }\n      ],\n      \"canBeCombined\": true,\n      \"availability\": [\n        \"inStore\"\n      ],\n      \"offerLimitation\": \"noLimit\",\n      \"hasCustomLegal\": false,\n      \"mechanisms\": [\n        {\n          \"mechanismType\": \"noAction\"\n        }\n      ]\n    },\n    {\n      \"partnerId\": \"3db0b3e0-4134-453e-8381-08e90f2b5141\",\n      \"partnerName\": \"Rexall\",\n      \"offerType\": \"spend\",\n      \"qualifier\": \"storewide\",\n      \"awardType\": \"flatMiles\",\n      \"displayDate\": \"2019-02-22T00:00:00Z\",\n      \"startDate\": \"2019-02-22T00:00:00Z\",\n      \"endDate\": \"2019-02-22T23:59:00Z\",\n      \"displayPriority\": 0,\n      \"regions\": [\n        \"AB\",\n        \"BC\",\n        \"MB\",\n        \"NT\",\n        \"ON\",\n        \"SK\"\n      ],\n      \"image\": {\n        \"en-US\": {\n          \"path\": \"https://dev-post-public.s3.amazonaws.com/images/default/spend.png\"\n        }\n      },\n      \"tiers\": [\n        {\n          \"qualifierValue\": 10,\n          \"awardValue\": 10\n        },\n        {\n          \"qualifierValue\": 20,\n          \"awardValue\": 20\n        },\n        {\n          \"qualifierValue\": 30,\n          \"awardValue\": 30\n        }\n      ],\n      \"canBeCombined\": true,\n      \"availability\": [\n        \"inStore\"\n      ],\n      \"offerLimitation\": \"noLimit\",\n      \"hasCustomLegal\": false,\n      \"mechanisms\": [\n        {\n          \"mechanismType\": \"noAction\"\n        }\n      ]\n    },\n    {\n      \"partnerId\": \"3db0b3e0-4134-453e-8381-08e90f2b5141\",\n      \"partnerName\": \"Rexall\",\n      \"offerType\": \"spend\",\n      \"qualifier\": \"storewide\",\n      \"awardType\": \"flatMiles\",\n      \"displayDate\": \"2019-02-22T00:00:00Z\",\n      \"startDate\": \"2019-02-22T00:00:00Z\",\n      \"endDate\": \"2019-02-22T23:59:00Z\",\n      \"displayPriority\": 0,\n      \"regions\": [\n        \"AB\",\n        \"BC\",\n        \"MB\",\n        \"NT\",\n        \"ON\",\n        \"SK\"\n      ],\n      \"image\": {\n        \"en-US\": {\n          \"path\": \"https://dev-post-public.s3.amazonaws.com/images/default/spend.png\"\n        }\n      },\n      \"tiers\": [\n        {\n          \"qualifierValue\": 10,\n          \"awardValue\": 10\n        },\n        {\n          \"qualifierValue\": 20,\n          \"awardValue\": 20\n        },\n        {\n          \"qualifierValue\": 30,\n          \"awardValue\": 30\n        }\n      ],\n      \"canBeCombined\": true,\n      \"availability\": [\n        \"inStore\"\n      ],\n      \"offerLimitation\": \"noLimit\",\n      \"hasCustomLegal\": false,\n      \"mechanisms\": [\n        {\n          \"mechanismType\": \"noAction\"\n        }\n      ]\n    },\n    {\n      \"partnerId\": \"3db0b3e0-4134-453e-8381-08e90f2b5141\",\n      \"partnerName\": \"Rexall\",\n      \"offerType\": \"spend\",\n      \"qualifier\": \"storewide\",\n      \"awardType\": \"flatMiles\",\n      \"displayDate\": \"2019-02-22T00:00:00Z\",\n      \"startDate\": \"2019-02-22T00:00:00Z\",\n      \"endDate\": \"2019-02-22T23:59:00Z\",\n      \"displayPriority\": 0,\n      \"regions\": [\n        \"AB\",\n        \"BC\",\n        \"MB\",\n        \"NT\",\n        \"ON\",\n        \"SK\"\n      ],\n      \"image\": {\n        \"en-US\": {\n          \"path\": \"https://dev-post-public.s3.amazonaws.com/images/default/spend.png\"\n        }\n      },\n      \"tiers\": [\n        {\n          \"qualifierValue\": 10,\n          \"awardValue\": 10\n        },\n        {\n          \"qualifierValue\": 20,\n          \"awardValue\": 20\n        },\n        {\n          \"qualifierValue\": 30,\n          \"awardValue\": 30\n        }\n      ],\n      \"canBeCombined\": true,\n      \"availability\": [\n        \"inStore\"\n      ],\n      \"offerLimitation\": \"noLimit\",\n      \"hasCustomLegal\": false,\n      \"mechanisms\": [\n        {\n          \"mechanismType\": \"noAction\"\n        }\n      ]\n    },\n    {\n      \"partnerId\": \"3db0b3e0-4134-453e-8381-08e90f2b5141\",\n      \"partnerName\": \"Rexall\",\n      \"offerType\": \"spend\",\n      \"qualifier\": \"storewide\",\n      \"awardType\": \"flatMiles\",\n      \"displayDate\": \"2019-02-22T00:00:00Z\",\n      \"startDate\": \"2019-02-22T00:00:00Z\",\n      \"endDate\": \"2019-02-22T23:59:00Z\",\n      \"displayPriority\": 0,\n      \"regions\": [\n        \"AB\",\n        \"BC\",\n        \"MB\",\n        \"NT\",\n        \"ON\",\n        \"SK\"\n      ],\n      \"image\": {\n        \"en-US\": {\n          \"path\": \"https://dev-post-public.s3.amazonaws.com/images/default/spend.png\"\n        }\n      },\n      \"tiers\": [\n        {\n          \"qualifierValue\": 10,\n          \"awardValue\": 10\n        },\n        {\n          \"qualifierValue\": 20,\n          \"awardValue\": 20\n        },\n        {\n          \"qualifierValue\": 30,\n          \"awardValue\": 30\n        }\n      ],\n      \"canBeCombined\": true,\n      \"availability\": [\n        \"inStore\"\n      ],\n      \"offerLimitation\": \"noLimit\",\n      \"hasCustomLegal\": false,\n      \"mechanisms\": [\n        {\n          \"mechanismType\": \"noAction\"\n        }\n      ]\n    },\n    {\n      \"partnerId\": \"3db0b3e0-4134-453e-8381-08e90f2b5141\",\n      \"partnerName\": \"Rexall\",\n      \"offerType\": \"spend\",\n      \"qualifier\": \"storewide\",\n      \"awardType\": \"flatMiles\",\n      \"displayDate\": \"2019-02-22T00:00:00Z\",\n      \"startDate\": \"2019-02-22T00:00:00Z\",\n      \"endDate\": \"2019-02-22T23:59:00Z\",\n      \"displayPriority\": 0,\n      \"regions\": [\n        \"AB\",\n        \"BC\",\n        \"MB\",\n        \"NT\",\n        \"ON\",\n        \"SK\"\n      ],\n      \"image\": {\n        \"en-US\": {\n          \"path\": \"https://dev-post-public.s3.amazonaws.com/images/default/spend.png\"\n        }\n      },\n      \"tiers\": [\n        {\n          \"qualifierValue\": 10,\n          \"awardValue\": 10\n        },\n        {\n          \"qualifierValue\": 20,\n          \"awardValue\": 20\n        },\n        {\n          \"qualifierValue\": 30,\n          \"awardValue\": 30\n        }\n      ],\n      \"canBeCombined\": true,\n      \"availability\": [\n        \"inStore\"\n      ],\n      \"offerLimitation\": \"noLimit\",\n      \"hasCustomLegal\": false,\n      \"mechanisms\": [\n        {\n          \"mechanismType\": \"noAction\"\n        }\n      ]\n    },\n    {\n      \"partnerId\": \"3db0b3e0-4134-453e-8381-08e90f2b5141\",\n      \"partnerName\": \"Rexall\",\n      \"offerType\": \"spend\",\n      \"qualifier\": \"storewide\",\n      \"awardType\": \"flatMiles\",\n      \"displayDate\": \"2019-02-22T00:00:00Z\",\n      \"startDate\": \"2019-02-22T00:00:00Z\",\n      \"endDate\": \"2019-02-22T23:59:00Z\",\n      \"displayPriority\": 0,\n      \"regions\": [\n        \"AB\",\n        \"BC\",\n        \"MB\",\n        \"NT\",\n        \"ON\",\n        \"SK\"\n      ],\n      \"image\": {\n        \"en-US\": {\n          \"path\": \"https://dev-post-public.s3.amazonaws.com/images/default/spend.png\"\n        }\n      },\n      \"tiers\": [\n        {\n          \"qualifierValue\": 10,\n          \"awardValue\": 10\n        },\n        {\n          \"qualifierValue\": 20,\n          \"awardValue\": 20\n        },\n        {\n          \"qualifierValue\": 30,\n          \"awardValue\": 30\n        }\n      ],\n      \"canBeCombined\": true,\n      \"availability\": [\n        \"inStore\"\n      ],\n      \"offerLimitation\": \"noLimit\",\n      \"hasCustomLegal\": false,\n      \"mechanisms\": [\n        {\n          \"mechanismType\": \"noAction\"\n        }\n      ]\n    },\n    {\n      \"partnerId\": \"3db0b3e0-4134-453e-8381-08e90f2b5141\",\n      \"partnerName\": \"Rexall\",\n      \"offerType\": \"spend\",\n      \"qualifier\": \"storewide\",\n      \"awardType\": \"flatMiles\",\n      \"displayDate\": \"2019-02-22T00:00:00Z\",\n      \"startDate\": \"2019-02-22T00:00:00Z\",\n      \"endDate\": \"2019-02-22T23:59:00Z\",\n      \"displayPriority\": 0,\n      \"regions\": [\n        \"AB\",\n        \"BC\",\n        \"MB\",\n        \"NT\",\n        \"ON\",\n        \"SK\"\n      ],\n      \"image\": {\n        \"en-US\": {\n          \"path\": \"https://dev-post-public.s3.amazonaws.com/images/default/spend.png\"\n        }\n      },\n      \"tiers\": [\n        {\n          \"qualifierValue\": 10,\n          \"awardValue\": 10\n        },\n        {\n          \"qualifierValue\": 20,\n          \"awardValue\": 20\n        },\n        {\n          \"qualifierValue\": 30,\n          \"awardValue\": 30\n        }\n      ],\n      \"canBeCombined\": true,\n      \"availability\": [\n        \"inStore\"\n      ],\n      \"offerLimitation\": \"noLimit\",\n      \"hasCustomLegal\": false,\n      \"mechanisms\": [\n        {\n          \"mechanismType\": \"noAction\"\n        }\n      ]\n    },\n    {\n      \"partnerId\": \"3db0b3e0-4134-453e-8381-08e90f2b5141\",\n      \"partnerName\": \"Rexall\",\n      \"offerType\": \"spend\",\n      \"qualifier\": \"storewide\",\n      \"awardType\": \"flatMiles\",\n      \"displayDate\": \"2019-02-22T00:00:00Z\",\n      \"startDate\": \"2019-02-22T00:00:00Z\",\n      \"endDate\": \"2019-02-22T23:59:00Z\",\n      \"displayPriority\": 0,\n      \"regions\": [\n        \"AB\",\n        \"BC\",\n        \"MB\",\n        \"NT\",\n        \"ON\",\n        \"SK\"\n      ],\n      \"image\": {\n        \"en-US\": {\n          \"path\": \"https://dev-post-public.s3.amazonaws.com/images/default/spend.png\"\n        }\n      },\n      \"tiers\": [\n        {\n          \"qualifierValue\": 10,\n          \"awardValue\": 10\n        },\n        {\n          \"qualifierValue\": 20,\n          \"awardValue\": 20\n        },\n        {\n          \"qualifierValue\": 30,\n          \"awardValue\": 30\n        }\n      ],\n      \"canBeCombined\": true,\n      \"availability\": [\n        \"inStore\"\n      ],\n      \"offerLimitation\": \"noLimit\",\n      \"hasCustomLegal\": false,\n      \"mechanisms\": [\n        {\n          \"mechanismType\": \"noAction\"\n        }\n      ]\n    },\n    {\n      \"partnerId\": \"3db0b3e0-4134-453e-8381-08e90f2b5141\",\n      \"partnerName\": \"Rexall\",\n      \"offerType\": \"spend\",\n      \"qualifier\": \"storewide\",\n      \"awardType\": \"flatMiles\",\n      \"displayDate\": \"2019-02-22T00:00:00Z\",\n      \"startDate\": \"2019-02-22T00:00:00Z\",\n      \"endDate\": \"2019-02-22T23:59:00Z\",\n      \"displayPriority\": 0,\n      \"regions\": [\n        \"AB\",\n        \"BC\",\n        \"MB\",\n        \"NT\",\n        \"ON\",\n        \"SK\"\n      ],\n      \"image\": {\n        \"en-US\": {\n          \"path\": \"https://dev-post-public.s3.amazonaws.com/images/default/spend.png\"\n        }\n      },\n      \"tiers\": [\n        {\n          \"qualifierValue\": 10,\n          \"awardValue\": 10\n        },\n        {\n          \"qualifierValue\": 20,\n          \"awardValue\": 20\n        },\n        {\n          \"qualifierValue\": 30,\n          \"awardValue\": 30\n        }\n      ],\n      \"canBeCombined\": true,\n      \"availability\": [\n        \"inStore\"\n      ],\n      \"offerLimitation\": \"noLimit\",\n      \"hasCustomLegal\": false,\n      \"mechanisms\": [\n        {\n          \"mechanismType\": \"noAction\"\n        }\n      ]\n    },\n    {\n      \"partnerId\": \"3db0b3e0-4134-453e-8381-08e90f2b5141\",\n      \"partnerName\": \"Rexall\",\n      \"offerType\": \"spend\",\n      \"qualifier\": \"storewide\",\n      \"awardType\": \"flatMiles\",\n      \"displayDate\": \"2019-02-22T00:00:00Z\",\n      \"startDate\": \"2019-02-22T00:00:00Z\",\n      \"endDate\": \"2019-02-22T23:59:00Z\",\n      \"displayPriority\": 0,\n      \"regions\": [\n        \"AB\",\n        \"BC\",\n        \"MB\",\n        \"NT\",\n        \"ON\",\n        \"SK\"\n      ],\n      \"image\": {\n        \"en-US\": {\n          \"path\": \"https://dev-post-public.s3.amazonaws.com/images/default/spend.png\"\n        }\n      },\n      \"tiers\": [\n        {\n          \"qualifierValue\": 10,\n          \"awardValue\": 10\n        },\n        {\n          \"qualifierValue\": 20,\n          \"awardValue\": 20\n        },\n        {\n          \"qualifierValue\": 30,\n          \"awardValue\": 30\n        }\n      ],\n      \"canBeCombined\": true,\n      \"availability\": [\n        \"inStore\"\n      ],\n      \"offerLimitation\": \"noLimit\",\n      \"hasCustomLegal\": false,\n      \"mechanisms\": [\n        {\n          \"mechanismType\": \"noAction\"\n        }\n      ]\n    },\n    {\n      \"partnerId\": \"3db0b3e0-4134-453e-8381-08e90f2b5141\",\n      \"partnerName\": \"Rexall\",\n      \"offerType\": \"spend\",\n      \"qualifier\": \"storewide\",\n      \"awardType\": \"flatMiles\",\n      \"displayDate\": \"2019-02-22T00:00:00Z\",\n      \"startDate\": \"2019-02-22T00:00:00Z\",\n      \"endDate\": \"2019-02-22T23:59:00Z\",\n      \"displayPriority\": 0,\n      \"regions\": [\n        \"AB\",\n        \"BC\",\n        \"MB\",\n        \"NT\",\n        \"ON\",\n        \"SK\"\n      ],\n      \"image\": {\n        \"en-US\": {\n          \"path\": \"https://dev-post-public.s3.amazonaws.com/images/default/spend.png\"\n        }\n      },\n      \"tiers\": [\n        {\n          \"qualifierValue\": 10,\n          \"awardValue\": 10\n        },\n        {\n          \"qualifierValue\": 20,\n          \"awardValue\": 20\n        },\n        {\n          \"qualifierValue\": 30,\n          \"awardValue\": 30\n        }\n      ],\n      \"canBeCombined\": true,\n      \"availability\": [\n        \"inStore\"\n      ],\n      \"offerLimitation\": \"noLimit\",\n      \"hasCustomLegal\": false,\n      \"mechanisms\": [\n        {\n          \"mechanismType\": \"noAction\"\n        }\n      ]\n    },\n    {\n      \"partnerId\": \"3db0b3e0-4134-453e-8381-08e90f2b5141\",\n      \"partnerName\": \"Rexall\",\n      \"offerType\": \"spend\",\n      \"qualifier\": \"storewide\",\n      \"awardType\": \"flatMiles\",\n      \"displayDate\": \"2019-02-22T00:00:00Z\",\n      \"startDate\": \"2019-02-22T00:00:00Z\",\n      \"endDate\": \"2019-02-22T23:59:00Z\",\n      \"displayPriority\": 0,\n      \"regions\": [\n        \"AB\",\n        \"BC\",\n        \"MB\",\n        \"NT\",\n        \"ON\",\n        \"SK\"\n      ],\n      \"image\": {\n        \"en-US\": {\n          \"path\": \"https://dev-post-public.s3.amazonaws.com/images/default/spend.png\"\n        }\n      },\n      \"tiers\": [\n        {\n          \"qualifierValue\": 10,\n          \"awardValue\": 10\n        },\n        {\n          \"qualifierValue\": 20,\n          \"awardValue\": 20\n        },\n        {\n          \"qualifierValue\": 30,\n          \"awardValue\": 30\n        }\n      ],\n      \"canBeCombined\": true,\n      \"availability\": [\n        \"inStore\"\n      ],\n      \"offerLimitation\": \"noLimit\",\n      \"hasCustomLegal\": false,\n      \"mechanisms\": [\n        {\n          \"mechanismType\": \"noAction\"\n        }\n      ]\n    },\n    {\n      \"partnerId\": \"3db0b3e0-4134-453e-8381-08e90f2b5141\",\n      \"partnerName\": \"Rexall\",\n      \"offerType\": \"spend\",\n      \"qualifier\": \"storewide\",\n      \"awardType\": \"flatMiles\",\n      \"displayDate\": \"2019-02-22T00:00:00Z\",\n      \"startDate\": \"2019-02-22T00:00:00Z\",\n      \"endDate\": \"2019-02-22T23:59:00Z\",\n      \"displayPriority\": 0,\n      \"regions\": [\n        \"AB\",\n        \"BC\",\n        \"MB\",\n        \"NT\",\n        \"ON\",\n        \"SK\"\n      ],\n      \"image\": {\n        \"en-US\": {\n          \"path\": \"https://dev-post-public.s3.amazonaws.com/images/default/spend.png\"\n        }\n      },\n      \"tiers\": [\n        {\n          \"qualifierValue\": 10,\n          \"awardValue\": 10\n        },\n        {\n          \"qualifierValue\": 20,\n          \"awardValue\": 20\n        },\n        {\n          \"qualifierValue\": 30,\n          \"awardValue\": 30\n        }\n      ],\n      \"canBeCombined\": true,\n      \"availability\": [\n        \"inStore\"\n      ],\n      \"offerLimitation\": \"noLimit\",\n      \"hasCustomLegal\": false,\n      \"mechanisms\": [\n        {\n          \"mechanismType\": \"noAction\"\n        }\n      ]\n    },\n    {\n      \"partnerId\": \"3db0b3e0-4134-453e-8381-08e90f2b5141\",\n      \"partnerName\": \"Rexall\",\n      \"offerType\": \"spend\",\n      \"qualifier\": \"storewide\",\n      \"awardType\": \"flatMiles\",\n      \"displayDate\": \"2019-02-22T00:00:00Z\",\n      \"startDate\": \"2019-02-22T00:00:00Z\",\n      \"endDate\": \"2019-02-22T23:59:00Z\",\n      \"displayPriority\": 0,\n      \"regions\": [\n        \"AB\",\n        \"BC\",\n        \"MB\",\n        \"NT\",\n        \"ON\",\n        \"SK\"\n      ],\n      \"image\": {\n        \"en-US\": {\n          \"path\": \"https://dev-post-public.s3.amazonaws.com/images/default/spend.png\"\n        }\n      },\n      \"tiers\": [\n        {\n          \"qualifierValue\": 10,\n          \"awardValue\": 10\n        },\n        {\n          \"qualifierValue\": 20,\n          \"awardValue\": 20\n        },\n        {\n          \"qualifierValue\": 30,\n          \"awardValue\": 30\n        }\n      ],\n      \"canBeCombined\": true,\n      \"availability\": [\n        \"inStore\"\n      ],\n      \"offerLimitation\": \"noLimit\",\n      \"hasCustomLegal\": false,\n      \"mechanisms\": [\n        {\n          \"mechanismType\": \"noAction\"\n        }\n      ]\n    },\n    {\n      \"partnerId\": \"3db0b3e0-4134-453e-8381-08e90f2b5141\",\n      \"partnerName\": \"Rexall\",\n      \"offerType\": \"spend\",\n      \"qualifier\": \"storewide\",\n      \"awardType\": \"flatMiles\",\n      \"displayDate\": \"2019-02-22T00:00:00Z\",\n      \"startDate\": \"2019-02-22T00:00:00Z\",\n      \"endDate\": \"2019-02-22T23:59:00Z\",\n      \"displayPriority\": 0,\n      \"regions\": [\n        \"AB\",\n        \"BC\",\n        \"MB\",\n        \"NT\",\n        \"ON\",\n        \"SK\"\n      ],\n      \"image\": \"badImage\",\n      \"tiers\": [\n        {\n          \"qualifierValue\": 10,\n          \"awardValue\": 10\n        },\n        {\n          \"qualifierValue\": 20,\n          \"awardValue\": 20\n        },\n        {\n          \"qualifierValue\": 30,\n          \"awardValue\": 30\n        }\n      ],\n      \"canBeCombined\": true,\n      \"availability\": [\n        \"inStore\"\n      ],\n      \"offerLimitation\": \"noLimit\",\n      \"hasCustomLegal\": false,\n      \"mechanisms\": [\n        {\n          \"mechanismType\": \"noAction\"\n        }\n      ]\n    },\n    {\n      \"partnerId\": \"3db0b3e0-4134-453e-8381-08e90f2b5141\",\n      \"partnerName\": \"Rexall\",\n      \"offerType\": \"spend\",\n      \"qualifier\": \"storewide\",\n      \"awardType\": \"flatMiles\",\n      \"displayDate\": \"2019-02-22T00:00:00Z\",\n      \"startDate\": \"2019-02-22T00:00:00Z\",\n      \"endDate\": \"2019-02-22T23:59:00Z\",\n      \"displayPriority\": 0,\n      \"regions\": [\n        \"AB\",\n        \"BC\",\n        \"MB\",\n        \"NT\",\n        \"ON\",\n        \"SK\"\n      ],\n      \"image\": {\n        \"en-US\": {\n          \"path\": \"https://dev-post-public.s3.amazonaws.com/images/default/spend.png\"\n        }\n      },\n      \"tiers\": [\n        {\n          \"qualifierValue\": 10,\n          \"awardValue\": 10\n        },\n        {\n          \"qualifierValue\": 20,\n          \"awardValue\": 20\n        },\n        {\n          \"qualifierValue\": 30,\n          \"awardValue\": 30\n        }\n      ],\n      \"canBeCombined\": true,\n      \"availability\": [\n        \"inStore\"\n      ],\n      \"offerLimitation\": \"noLimit\",\n      \"hasCustomLegal\": false,\n      \"mechanisms\": [\n        {\n          \"mechanismType\": \"noAction\"\n        }\n      ]\n    },\n    {\n      \"partnerId\": \"3db0b3e0-4134-453e-8381-08e90f2b5141\",\n      \"partnerName\": \"Rexall\",\n      \"offerType\": \"spend\",\n      \"qualifier\": \"storewide\",\n      \"awardType\": \"flatMiles\",\n      \"displayDate\": \"2019-02-22T00:00:00Z\",\n      \"startDate\": \"2019-02-22T00:00:00Z\",\n      \"endDate\": \"2019-02-22T23:59:00Z\",\n      \"displayPriority\": 0,\n      \"regions\": [\n        \"AB\",\n        \"BC\",\n        \"MB\",\n        \"NT\",\n        \"ON\",\n        \"SK\"\n      ],\n      \"image\": {\n        \"en-US\": {\n          \"path\": \"https://dev-post-public.s3.amazonaws.com/images/default/spend.png\"\n        }\n      },\n      \"tiers\": [\n        {\n          \"qualifierValue\": 10,\n          \"awardValue\": 10\n        },\n        {\n          \"qualifierValue\": 20,\n          \"awardValue\": 20\n        },\n        {\n          \"qualifierValue\": 30,\n          \"awardValue\": 30\n        }\n      ],\n      \"canBeCombined\": true,\n      \"availability\": [\n        \"inStore\"\n      ],\n      \"offerLimitation\": \"noLimit\",\n      \"hasCustomLegal\": false,\n      \"mechanisms\": [\n        {\n          \"mechanismType\": \"noAction\"\n        }\n      ]\n    },\n    {\n      \"partnerId\": \"3db0b3e0-4134-453e-8381-08e90f2b5141\",\n      \"partnerName\": \"Rexall\",\n      \"offerType\": \"spend\",\n      \"qualifier\": \"storewide\",\n      \"awardType\": \"flatMiles\",\n      \"displayDate\": \"2019-02-22T00:00:00Z\",\n      \"startDate\": \"2019-02-22T00:00:00Z\",\n      \"endDate\": \"2019-02-22T23:59:00Z\",\n      \"displayPriority\": 0,\n      \"regions\": [\n        \"AB\",\n        \"BC\",\n        \"MB\",\n        \"NT\",\n        \"ON\",\n        \"SK\"\n      ],\n      \"image\": {\n        \"en-US\": {\n          \"path\": \"https://dev-post-public.s3.amazonaws.com/images/default/spend.png\"\n        }\n      },\n      \"tiers\": [\n        {\n          \"qualifierValue\": 10,\n          \"awardValue\": 10\n        },\n        {\n          \"qualifierValue\": 20,\n          \"awardValue\": 20\n        },\n        {\n          \"qualifierValue\": 30,\n          \"awardValue\": 30\n        }\n      ],\n      \"canBeCombined\": true,\n      \"availability\": [\n        \"inStore\"\n      ],\n      \"offerLimitation\": \"noLimit\",\n      \"hasCustomLegal\": false,\n      \"mechanisms\": [\n        {\n          \"mechanismType\": \"noAction\"\n        }\n      ]\n    },\n    {\n      \"partnerId\": \"3db0b3e0-4134-453e-8381-08e90f2b5141\",\n      \"partnerName\": \"Rexall\",\n      \"offerType\": \"spend\",\n      \"qualifier\": \"storewide\",\n      \"awardType\": \"flatMiles\",\n      \"displayDate\": \"2019-02-22T00:00:00Z\",\n      \"startDate\": \"2019-02-22T00:00:00Z\",\n      \"endDate\": \"2019-02-22T23:59:00Z\",\n      \"displayPriority\": 0,\n      \"regions\": [\n        \"AB\",\n        \"BC\",\n        \"MB\",\n        \"NT\",\n        \"ON\",\n        \"SK\"\n      ],\n      \"image\": {\n        \"en-US\": {\n          \"path\": \"https://dev-post-public.s3.amazonaws.com/images/default/spend.png\"\n        }\n      },\n      \"tiers\": [\n        {\n          \"qualifierValue\": 10,\n          \"awardValue\": 10\n        },\n        {\n          \"qualifierValue\": 20,\n          \"awardValue\": 20\n        },\n        {\n          \"qualifierValue\": 30,\n          \"awardValue\": 30\n        }\n      ],\n      \"canBeCombined\": true,\n      \"availability\": [\n        \"inStore\"\n      ],\n      \"offerLimitation\": \"noLimit\",\n      \"hasCustomLegal\": false,\n      \"mechanisms\": [\n        {\n          \"mechanismType\": \"noAction\"\n        }\n      ]\n    },\n    {\n      \"partnerId\": \"3db0b3e0-4134-453e-8381-08e90f2b5141\",\n      \"partnerName\": \"Rexall\",\n      \"offerType\": \"spend\",\n      \"qualifier\": \"storewide\",\n      \"awardType\": \"flatMiles\",\n      \"displayDate\": \"2019-02-22T00:00:00Z\",\n      \"startDate\": \"2019-02-22T00:00:00Z\",\n      \"endDate\": \"2019-02-22T23:59:00Z\",\n      \"displayPriority\": 0,\n      \"regions\": [\n        \"AB\",\n        \"BC\",\n        \"MB\",\n        \"NT\",\n        \"ON\",\n        \"SK\"\n      ],\n      \"image\": {\n        \"en-US\": {\n          \"path\": \"https://dev-post-public.s3.amazonaws.com/images/default/spend.png\"\n        }\n      },\n      \"tiers\": [\n        {\n          \"qualifierValue\": 10,\n          \"awardValue\": 10\n        },\n        {\n          \"qualifierValue\": 20,\n          \"awardValue\": 20\n        },\n        {\n          \"qualifierValue\": 30,\n          \"awardValue\": 30\n        }\n      ],\n      \"canBeCombined\": true,\n      \"availability\": [\n        \"inStore\"\n      ],\n      \"offerLimitation\": \"noLimit\",\n      \"hasCustomLegal\": false,\n      \"mechanisms\": [\n        {\n          \"mechanismType\": \"noAction\"\n        }\n      ]\n    },\n    {\n      \"partnerId\": \"3db0b3e0-4134-453e-8381-08e90f2b5141\",\n      \"partnerName\": \"Rexall\",\n      \"offerType\": \"spend\",\n      \"qualifier\": \"storewide\",\n      \"awardType\": \"flatMiles\",\n      \"displayDate\": \"2019-02-22T00:00:00Z\",\n      \"startDate\": \"2019-02-22T00:00:00Z\",\n      \"endDate\": \"2019-02-22T23:59:00Z\",\n      \"displayPriority\": 0,\n      \"regions\": [\n        \"AB\",\n        \"BC\",\n        \"MB\",\n        \"NT\",\n        \"ON\",\n        \"SK\"\n      ],\n      \"image\": {\n        \"en-US\": {\n          \"path\": \"https://dev-post-public.s3.amazonaws.com/images/default/spend.png\"\n        }\n      },\n      \"tiers\": [\n        {\n          \"qualifierValue\": 10,\n          \"awardValue\": 10\n        },\n        {\n          \"qualifierValue\": 20,\n          \"awardValue\": 20\n        },\n        {\n          \"qualifierValue\": 30,\n          \"awardValue\": 30\n        }\n      ],\n      \"canBeCombined\": true,\n      \"availability\": [\n        \"inStore\"\n      ],\n      \"offerLimitation\": \"noLimit\",\n      \"hasCustomLegal\": false,\n      \"mechanisms\": [\n        {\n          \"mechanismType\": \"noAction\"\n        }\n      ]\n    },\n    {\n      \"partnerId\": \"3db0b3e0-4134-453e-8381-08e90f2b5141\",\n      \"partnerName\": \"Rexall\",\n      \"offerType\": \"spend\",\n      \"qualifier\": \"storewide\",\n      \"awardType\": \"flatMiles\",\n      \"displayDate\": \"2019-02-22T00:00:00Z\",\n      \"startDate\": \"2019-02-22T00:00:00Z\",\n      \"endDate\": \"2019-02-22T23:59:00Z\",\n      \"displayPriority\": 0,\n      \"regions\": [\n        \"AB\",\n        \"BC\",\n        \"MB\",\n        \"NT\",\n        \"ON\",\n        \"SK\"\n      ],\n      \"image\": {\n        \"en-US\": {\n          \"path\": \"https://dev-post-public.s3.amazonaws.com/images/default/spend.png\"\n        }\n      },\n      \"tiers\": [\n        {\n          \"qualifierValue\": 10,\n          \"awardValue\": 10\n        },\n        {\n          \"qualifierValue\": 20,\n          \"awardValue\": 20\n        },\n        {\n          \"qualifierValue\": 30,\n          \"awardValue\": 30\n        }\n      ],\n      \"canBeCombined\": true,\n      \"availability\": [\n        \"inStore\"\n      ],\n      \"offerLimitation\": \"noLimit\",\n      \"hasCustomLegal\": false,\n      \"mechanisms\": [\n        {\n          \"mechanismType\": \"noAction\"\n        }\n      ]\n    },\n    {\n      \"partnerId\": \"3db0b3e0-4134-453e-8381-08e90f2b5141\",\n      \"partnerName\": \"Rexall\",\n      \"offerType\": \"spend\",\n      \"qualifier\": \"storewide\",\n      \"awardType\": \"flatMiles\",\n      \"displayDate\": \"2019-02-22T00:00:00Z\",\n      \"startDate\": \"2019-02-22T00:00:00Z\",\n      \"endDate\": \"2019-02-22T23:59:00Z\",\n      \"displayPriority\": 0,\n      \"regions\": [\n        \"AB\",\n        \"BC\",\n        \"MB\",\n        \"NT\",\n        \"ON\",\n        \"SK\"\n      ],\n      \"image\": {\n        \"en-US\": {\n          \"path\": \"https://dev-post-public.s3.amazonaws.com/images/default/spend.png\"\n        }\n      },\n      \"tiers\": [\n        {\n          \"qualifierValue\": 10,\n          \"awardValue\": 10\n        },\n        {\n          \"qualifierValue\": 20,\n          \"awardValue\": 20\n        },\n        {\n          \"qualifierValue\": 30,\n          \"awardValue\": 30\n        }\n      ],\n      \"canBeCombined\": true,\n      \"availability\": [\n        \"inStore\"\n      ],\n      \"offerLimitation\": \"noLimit\",\n      \"hasCustomLegal\": false,\n      \"mechanisms\": [\n        {\n          \"mechanismType\": \"noAction\"\n        }\n      ]\n    },\n    {\n      \"partnerId\": \"3db0b3e0-4134-453e-8381-08e90f2b5141\",\n      \"partnerName\": \"Rexall\",\n      \"offerType\": \"spend\",\n      \"qualifier\": \"storewide\",\n      \"awardType\": \"flatMiles\",\n      \"displayDate\": \"2019-02-22T00:00:00Z\",\n      \"startDate\": \"2019-02-22T00:00:00Z\",\n      \"endDate\": \"2019-02-22T23:59:00Z\",\n      \"displayPriority\": 0,\n      \"regions\": [\n        \"AB\",\n        \"BC\",\n        \"MB\",\n        \"NT\",\n        \"ON\",\n        \"SK\"\n      ],\n      \"image\": {\n        \"en-US\": {\n          \"path\": \"https://dev-post-public.s3.amazonaws.com/images/default/spend.png\"\n        }\n      },\n      \"tiers\": [\n        {\n          \"qualifierValue\": 10,\n          \"awardValue\": 10\n        },\n        {\n          \"qualifierValue\": 20,\n          \"awardValue\": 20\n        },\n        {\n          \"qualifierValue\": 30,\n          \"awardValue\": 30\n        }\n      ],\n      \"canBeCombined\": true,\n      \"availability\": [\n        \"inStore\"\n      ],\n      \"offerLimitation\": \"noLimit\",\n      \"hasCustomLegal\": false,\n      \"mechanisms\": [\n        {\n          \"mechanismType\": \"noAction\"\n        }\n      ]\n    },\n    {\n      \"partnerId\": \"3db0b3e0-4134-453e-8381-08e90f2b5141\",\n      \"partnerName\": \"Rexall\",\n      \"offerType\": \"spend\",\n      \"qualifier\": \"storewide\",\n      \"awardType\": \"flatMiles\",\n      \"displayDate\": \"2019-02-22T00:00:00Z\",\n      \"startDate\": \"2019-02-22T00:00:00Z\",\n      \"endDate\": \"2019-02-22T23:59:00Z\",\n      \"displayPriority\": 0,\n      \"regions\": [\n        \"AB\",\n        \"BC\",\n        \"MB\",\n        \"NT\",\n        \"ON\",\n        \"SK\"\n      ],\n      \"image\": {\n        \"en-US\": {\n          \"path\": \"https://dev-post-public.s3.amazonaws.com/images/default/spend.png\"\n        }\n      },\n      \"tiers\": [\n        {\n          \"qualifierValue\": 10,\n          \"awardValue\": 10\n        },\n        {\n          \"qualifierValue\": 20,\n          \"awardValue\": 20\n        },\n        {\n          \"qualifierValue\": 30,\n          \"awardValue\": 30\n        }\n      ],\n      \"canBeCombined\": true,\n      \"availability\": [\n        \"inStore\"\n      ],\n      \"offerLimitation\": \"noLimit\",\n      \"hasCustomLegal\": false,\n      \"mechanisms\": [\n        {\n          \"mechanismType\": \"noAction\"\n        }\n      ]\n    },\n    {\n      \"partnerId\": \"3db0b3e0-4134-453e-8381-08e90f2b5141\",\n      \"partnerName\": \"Rexall\",\n      \"offerType\": \"spend\",\n      \"qualifier\": \"storewide\",\n      \"awardType\": \"flatMiles\",\n      \"displayDate\": \"2019-02-22T00:00:00Z\",\n      \"startDate\": \"2019-02-22T00:00:00Z\",\n      \"endDate\": \"2019-02-22T23:59:00Z\",\n      \"displayPriority\": 0,\n      \"regions\": [\n        \"AB\",\n        \"BC\",\n        \"MB\",\n        \"NT\",\n        \"ON\",\n        \"SK\"\n      ],\n      \"image\": {\n        \"en-US\": {\n          \"path\": \"https://dev-post-public.s3.amazonaws.com/images/default/spend.png\"\n        }\n      },\n      \"tiers\": [\n        {\n          \"qualifierValue\": 10,\n          \"awardValue\": 10\n        },\n        {\n          \"qualifierValue\": 20,\n          \"awardValue\": 20\n        },\n        {\n          \"qualifierValue\": 30,\n          \"awardValue\": 30\n        }\n      ],\n      \"canBeCombined\": true,\n      \"availability\": [\n        \"inStore\"\n      ],\n      \"offerLimitation\": \"noLimit\",\n      \"hasCustomLegal\": false,\n      \"mechanisms\": [\n        {\n          \"mechanismType\": \"noAction\"\n        }\n      ]\n    },\n    {\n      \"partnerId\": \"3db0b3e0-4134-453e-8381-08e90f2b5141\",\n      \"partnerName\": \"Rexall\",\n      \"offerType\": \"spend\",\n      \"qualifier\": \"storewide\",\n      \"awardType\": \"flatMiles\",\n      \"displayDate\": \"2019-02-22T00:00:00Z\",\n      \"startDate\": \"2019-02-22T00:00:00Z\",\n      \"endDate\": \"2019-02-22T23:59:00Z\",\n      \"displayPriority\": 0,\n      \"regions\": [\n        \"AB\",\n        \"BC\",\n        \"MB\",\n        \"NT\",\n        \"ON\",\n        \"SK\"\n      ],\n      \"image\": {\n        \"en-US\": {\n          \"path\": \"https://dev-post-public.s3.amazonaws.com/images/default/spend.png\"\n        }\n      },\n      \"tiers\": [\n        {\n          \"qualifierValue\": 10,\n          \"awardValue\": 10\n        },\n        {\n          \"qualifierValue\": 20,\n          \"awardValue\": 20\n        },\n        {\n          \"qualifierValue\": 30,\n          \"awardValue\": 30\n        }\n      ],\n      \"canBeCombined\": true,\n      \"availability\": [\n        \"inStore\"\n      ],\n      \"offerLimitation\": \"noLimit\",\n      \"hasCustomLegal\": false,\n      \"mechanisms\": [\n        {\n          \"mechanismType\": \"noAction\"\n        }\n      ]\n    },\n    {\n      \"partnerId\": \"3db0b3e0-4134-453e-8381-08e90f2b5141\",\n      \"partnerName\": \"Rexall\",\n      \"offerType\": \"spend\",\n      \"qualifier\": \"storewide\",\n      \"awardType\": \"flatMiles\",\n      \"displayDate\": \"2019-02-22T00:00:00Z\",\n      \"startDate\": \"2019-02-22T00:00:00Z\",\n      \"endDate\": \"2019-02-22T23:59:00Z\",\n      \"displayPriority\": 0,\n      \"regions\": [\n        \"AB\",\n        \"BC\",\n        \"MB\",\n        \"NT\",\n        \"ON\",\n        \"SK\"\n      ],\n      \"image\": {\n        \"en-US\": {\n          \"path\": \"https://dev-post-public.s3.amazonaws.com/images/default/spend.png\"\n        }\n      },\n      \"tiers\": [\n        {\n          \"qualifierValue\": 10,\n          \"awardValue\": 10\n        },\n        {\n          \"qualifierValue\": 20,\n          \"awardValue\": 20\n        },\n        {\n          \"qualifierValue\": 30,\n          \"awardValue\": 30\n        }\n      ],\n      \"canBeCombined\": true,\n      \"availability\": [\n        \"inStore\"\n      ],\n      \"offerLimitation\": \"noLimit\",\n      \"hasCustomLegal\": false,\n      \"mechanisms\": [\n        {\n          \"mechanismType\": \"noAction\"\n        }\n      ]\n    },\n    {\n      \"partnerId\": \"3db0b3e0-4134-453e-8381-08e90f2b5141\",\n      \"partnerName\": \"Rexall\",\n      \"offerType\": \"spend\",\n      \"qualifier\": \"storewide\",\n      \"awardType\": \"flatMiles\",\n      \"displayDate\": \"2019-02-22T00:00:00Z\",\n      \"startDate\": \"2019-02-22T00:00:00Z\",\n      \"endDate\": \"2019-02-22T23:59:00Z\",\n      \"displayPriority\": 0,\n      \"regions\": [\n        \"AB\",\n        \"BC\",\n        \"MB\",\n        \"NT\",\n        \"ON\",\n        \"SK\"\n      ],\n      \"image\": {\n        \"en-US\": {\n          \"path\": \"https://dev-post-public.s3.amazonaws.com/images/default/spend.png\"\n        }\n      },\n      \"tiers\": [\n        {\n          \"qualifierValue\": 10,\n          \"awardValue\": 10\n        },\n        {\n          \"qualifierValue\": 20,\n          \"awardValue\": 20\n        },\n        {\n          \"qualifierValue\": 30,\n          \"awardValue\": 30\n        }\n      ],\n      \"canBeCombined\": true,\n      \"availability\": [\n        \"inStore\"\n      ],\n      \"offerLimitation\": \"noLimit\",\n      \"hasCustomLegal\": false,\n      \"mechanisms\": [\n        {\n          \"mechanismType\": \"noAction\"\n        }\n      ]\n    },\n    {\n      \"partnerId\": \"3db0b3e0-4134-453e-8381-08e90f2b5141\",\n      \"partnerName\": \"Rexall\",\n      \"offerType\": \"spend\",\n      \"qualifier\": \"storewide\",\n      \"awardType\": \"flatMiles\",\n      \"displayDate\": \"2019-02-22T00:00:00Z\",\n      \"startDate\": \"2019-02-22T00:00:00Z\",\n      \"endDate\": \"2019-02-22T23:59:00Z\",\n      \"displayPriority\": 0,\n      \"regions\": [\n        \"AB\",\n        \"BC\",\n        \"MB\",\n        \"NT\",\n        \"ON\",\n        \"SK\"\n      ],\n      \"image\": {\n        \"en-US\": {\n          \"path\": \"https://dev-post-public.s3.amazonaws.com/images/default/spend.png\"\n        }\n      },\n      \"tiers\": [\n        {\n          \"qualifierValue\": 10,\n          \"awardValue\": 10\n        },\n        {\n          \"qualifierValue\": 20,\n          \"awardValue\": 20\n        },\n        {\n          \"qualifierValue\": 30,\n          \"awardValue\": 30\n        }\n      ],\n      \"canBeCombined\": true,\n      \"availability\": [\n        \"inStore\"\n      ],\n      \"offerLimitation\": \"noLimit\",\n      \"hasCustomLegal\": false,\n      \"mechanisms\": [\n        {\n          \"mechanismType\": \"noAction\"\n        }\n      ]\n    },\n    {\n      \"partnerId\": \"3db0b3e0-4134-453e-8381-08e90f2b5141\",\n      \"partnerName\": \"Rexall\",\n      \"offerType\": \"spend\",\n      \"qualifier\": \"storewide\",\n      \"awardType\": \"flatMiles\",\n      \"displayDate\": \"2019-02-22T00:00:00Z\",\n      \"startDate\": \"2019-02-22T00:00:00Z\",\n      \"endDate\": \"2019-02-22T23:59:00Z\",\n      \"displayPriority\": 0,\n      \"regions\": [\n        \"AB\",\n        \"BC\",\n        \"MB\",\n        \"NT\",\n        \"ON\",\n        \"SK\"\n      ],\n      \"image\": {\n        \"en-US\": {\n          \"path\": \"https://dev-post-public.s3.amazonaws.com/images/default/spend.png\"\n        }\n      },\n      \"tiers\": [\n        {\n          \"qualifierValue\": 10,\n          \"awardValue\": 10\n        },\n        {\n          \"qualifierValue\": 20,\n          \"awardValue\": 20\n        },\n        {\n          \"qualifierValue\": 30,\n          \"awardValue\": 30\n        }\n      ],\n      \"canBeCombined\": true,\n      \"availability\": [\n        \"inStore\"\n      ],\n      \"offerLimitation\": \"noLimit\",\n      \"hasCustomLegal\": false,\n      \"mechanisms\": [\n        {\n          \"mechanismType\": \"noAction\"\n        }\n      ]\n    },\n    {\n      \"partnerId\": \"3db0b3e0-4134-453e-8381-08e90f2b5141\",\n      \"partnerName\": \"Rexall\",\n      \"offerType\": \"spend\",\n      \"qualifier\": \"storewide\",\n      \"awardType\": \"flatMiles\",\n      \"displayDate\": \"2019-02-22T00:00:00Z\",\n      \"startDate\": \"2019-02-22T00:00:00Z\",\n      \"endDate\": \"2019-02-22T23:59:00Z\",\n      \"displayPriority\": 0,\n      \"regions\": [\n        \"AB\",\n        \"BC\",\n        \"MB\",\n        \"NT\",\n        \"ON\",\n        \"SK\"\n      ],\n      \"image\": {\n        \"en-US\": {\n          \"path\": \"https://dev-post-public.s3.amazonaws.com/images/default/spend.png\"\n        }\n      },\n      \"tiers\": [\n        {\n          \"qualifierValue\": 10,\n          \"awardValue\": 10\n        },\n        {\n          \"qualifierValue\": 20,\n          \"awardValue\": 20\n        },\n        {\n          \"qualifierValue\": 30,\n          \"awardValue\": 30\n        }\n      ],\n      \"canBeCombined\": true,\n      \"availability\": [\n        \"inStore\"\n      ],\n      \"offerLimitation\": \"noLimit\",\n      \"hasCustomLegal\": false,\n      \"mechanisms\": [\n        {\n          \"mechanismType\": \"noAction\"\n        }\n      ]\n    },\n    {\n      \"partnerId\": \"3db0b3e0-4134-453e-8381-08e90f2b5141\",\n      \"partnerName\": \"Rexall\",\n      \"offerType\": \"spend\",\n      \"qualifier\": \"storewide\",\n      \"awardType\": \"flatMiles\",\n      \"displayDate\": \"2019-02-22T00:00:00Z\",\n      \"startDate\": \"2019-02-22T00:00:00Z\",\n      \"endDate\": \"2019-02-22T23:59:00Z\",\n      \"displayPriority\": 0,\n      \"regions\": [\n        \"AB\",\n        \"BC\",\n        \"MB\",\n        \"NT\",\n        \"ON\",\n        \"SK\"\n      ],\n      \"image\": {\n        \"en-US\": {\n          \"path\": \"https://dev-post-public.s3.amazonaws.com/images/default/spend.png\"\n        }\n      },\n      \"tiers\": [\n        {\n          \"qualifierValue\": 10,\n          \"awardValue\": 10\n        },\n        {\n          \"qualifierValue\": 20,\n          \"awardValue\": 20\n        },\n        {\n          \"qualifierValue\": 30,\n          \"awardValue\": 30\n        }\n      ],\n      \"canBeCombined\": true,\n      \"availability\": [\n        \"inStore\"\n      ],\n      \"offerLimitation\": \"noLimit\",\n      \"hasCustomLegal\": false,\n      \"mechanisms\": [\n        {\n          \"mechanismType\": \"noAction\"\n        }\n      ]\n    },\n    {\n      \"partnerId\": \"3db0b3e0-4134-453e-8381-08e90f2b5141\",\n      \"partnerName\": \"Rexall\",\n      \"offerType\": \"spend\",\n      \"qualifier\": \"storewide\",\n      \"awardType\": \"flatMiles\",\n      \"displayDate\": \"2019-02-22T00:00:00Z\",\n      \"startDate\": \"2019-02-22T00:00:00Z\",\n      \"endDate\": \"2019-02-22T23:59:00Z\",\n      \"displayPriority\": 0,\n      \"regions\": [\n        \"AB\",\n        \"BC\",\n        \"MB\",\n        \"NT\",\n        \"ON\",\n        \"SK\"\n      ],\n      \"image\": {\n        \"en-US\": {\n          \"path\": \"https://dev-post-public.s3.amazonaws.com/images/default/spend.png\"\n        }\n      },\n      \"tiers\": [\n        {\n          \"qualifierValue\": 10,\n          \"awardValue\": 10\n        },\n        {\n          \"qualifierValue\": 20,\n          \"awardValue\": 20\n        },\n        {\n          \"qualifierValue\": 30,\n          \"awardValue\": 30\n        }\n      ],\n      \"canBeCombined\": true,\n      \"availability\": [\n        \"inStore\"\n      ],\n      \"offerLimitation\": \"noLimit\",\n      \"hasCustomLegal\": false,\n      \"mechanisms\": [\n        {\n          \"mechanismType\": \"noAction\"\n        }\n      ]\n    }\n  ]\n}", "headers": "Content-Type: application/json\nx-user-email: <EMAIL>\n", "pathVariables": {}}, {"id": "0cec244a-97ed-42dc-a9ab-addd3ee75498", "name": "Create offer - Tiered Offer", "url": "{{basePath}}/offer-management-api/v3/offers", "description": "", "data": [], "dataOptions": null, "dataMode": "raw", "headerData": [{"key": "Content-Type", "value": "application/json", "enabled": true}, {"key": "x-user-email", "value": "<EMAIL>", "enabled": true}], "method": "POST", "pathVariableData": [], "queryParams": null, "auth": null, "events": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["var responseJSON = JSON.parse(responseBody);", "var offerId = responseJSON.id;", "console.log(offerId);", "postman.setEnvironmentVariable(\"offerId\", offerId);"]}}], "folder": null, "rawModeData": "{\n  \"partnerId\": \"3db0b3e0-4134-453e-8381-08e90f2b5141\",\n  \"partnerName\": \"Rexall\",\n  \"offerType\": \"spend\",\n  \"qualifier\": \"storewide\",\n  \"awardType\": \"flatMiles\",\n  \"displayDate\": \"2019-02-22T00:00:00Z\",\n  \"startDate\": \"2019-02-22T00:00:00Z\",\n  \"endDate\": \"2019-02-22T23:59:00Z\",\n  \"displayPriority\": 0,\n  \"regions\": [\n    \"AB\",\n    \"BC\",\n    \"MB\",\n    \"NT\",\n    \"ON\"\n  ],\n  \"image\": {\n    \"en-US\": {\n      \"path\": \"https://dev-post-public.s3.amazonaws.com/images/default/spend.png\"\n    }\n  },\n  \"tiers\": [\n    {\n      \"qualifierValue\": 10,\n      \"awardValue\": 10\n    },\n    {\n      \"qualifierValue\": 20,\n      \"awardValue\": 20\n    },\n    {\n      \"qualifierValue\": 30,\n      \"awardValue\": 30\n    }\n  ],\n  \"canBeCombined\": true,\n  \"availability\": [\n    \"inStore\"\n  ],\n  \"offerLimitation\": \"noLimit\",\n  \"hasCustomLegal\": false,\n  \"mechanisms\": [\n    {\n      \"mechanismType\": \"noAction\"\n    }\n  ]\n}", "headers": "Content-Type: application/json\nx-user-email: <EMAIL>\n", "pathVariables": {}}, {"id": "12a901f5-6c0f-42d4-944f-7f0cdd583fc0", "name": "Create Offer Bad Request", "url": "{{basePath}}/offer-management-api/v3/offers", "description": "", "data": [], "dataOptions": null, "dataMode": "raw", "headerData": [{"key": "Content-Type", "value": "application/json", "enabled": true}, {"key": "x-user-email", "value": "<EMAIL>", "enabled": true}], "method": "POST", "pathVariableData": [], "queryParams": null, "auth": null, "events": null, "folder": null, "rawModeData": "{\n  \"partnerId\": \"3db0b3e0-4134-453e-8381-08e90f2b5141\",\n  \"partnerName\": \"Rexall\",\n  \"offerType\": \"spend\",\n  \"qualifier\": \"storewide\",\n  \"awardType\": \"flatMiles\",\n  \"displayDate\": \"2019-02-22T00:00:00Z\",\n  \"startDate\": \"2019-02-22T00:00:00Z\",\n  \"endDate\": \"2019-02-22T23:59:00Z\",\n  \"displayPriority\": 0,\n  \"regions\": [\n    \"AB\",\n    \"BC\",\n    \"MB\",\n    \"NT\",\n    \"ON\",\n    \"SK\"\n  ],\n  \"image\": {\n    \"en-US\": {\n      \"path\": \"https://dev-post-public.s3.amazonaws.com/images/default/spend.png\"\n    }\n  },\n  \"tiers\": [\n    {\n      \"qualifierValue\": 10,\n      \"awardValue\": 10\n    },\n    {\n      \"qualifierValue\": 20,\n      \"awardValue\": 20\n    },\n    {\n      \"qualifierValue\": 30,\n      \"awardValue\": 30\n    },\n        {\n      \"qualifierValue\": 30,\n      \"awardValue\": 30\n    }\n  ],\n  \"canBeCombined\": true,\n  \"availability\": [\n    \"inStore\"\n  ],\n  \"offerLimitation\": \"noLimit\",\n  \"hasCustomLegal\": false,\n  \"mechanisms\": [\n    {\n      \"mechanismType\": \"noAction\"\n    }\n  ]\n}", "headers": "Content-Type: application/json\nx-user-email: <EMAIL>\n", "pathVariables": {}}, {"id": "3459babc-be43-4a0a-b629-adf99d809efe", "name": "Bulk Create 3", "url": "{{basePath}}/offer-management-api/v3/offers/bulk", "description": "", "data": [], "dataOptions": null, "dataMode": "raw", "headerData": [{"key": "Content-Type", "value": "application/json", "enabled": true}, {"key": "x-user-email", "value": "<EMAIL>", "enabled": true}], "method": "POST", "pathVariableData": [], "queryParams": null, "auth": null, "events": null, "folder": null, "rawModeData": "{\n  \"bulkName\": \"TestBatch\",\n  \"offers\": [\n    {\n      \"partnerId\": \"3db0b3e0-4134-453e-8381-08e90f2b5141\",\n      \"partnerName\": \"Rexall\",\n      \"offerType\": \"spend\",\n      \"qualifier\": \"storewide\",\n      \"awardType\": \"flatMiles\",\n      \"displayDate\": \"2019-02-22T00:00:00Z\",\n      \"startDate\": \"2019-02-22T00:00:00Z\",\n      \"endDate\": \"2019-02-22T23:59:00Z\",\n      \"displayPriority\": 0,\n      \"regions\": [\n        \"AB\",\n        \"BC\",\n        \"MB\",\n        \"NT\",\n        \"ON\",\n        \"SK\"\n      ],\n      \"image\": {\n        \"en-US\": {\n          \"path\": \"https://dev-post-public.s3.amazonaws.com/images/default/spend.png\"\n        }\n      },\n      \"tiers\": [\n        {\n          \"qualifierValue\": 10,\n          \"awardValue\": 10\n        },\n        {\n          \"qualifierValue\": 20,\n          \"awardValue\": 20\n        },\n        {\n          \"qualifierValue\": 30,\n          \"awardValue\": 30\n        }\n      ],\n      \"canBeCombined\": true,\n      \"availability\": [\n        \"inStore\"\n      ],\n      \"offerLimitation\": \"noLimit\",\n      \"hasCustomLegal\": false,\n      \"mechanisms\": [\n        {\n          \"mechanismType\": \"noAction\"\n        }\n      ]\n    },\n    {\n      \"partnerId\": \"3db0b3e0-4134-453e-8381-08e90f2b5141\",\n      \"partnerName\": \"Rexall\",\n      \"offerType\": \"spend\",\n      \"qualifier\": \"storewide\",\n      \"awardType\": \"flatMiles\",\n      \"displayDate\": \"2019-02-22T00:00:00Z\",\n      \"startDate\": \"2019-02-22T00:00:00Z\",\n      \"endDate\": \"2019-02-22T23:59:00Z\",\n      \"displayPriority\": 0,\n      \"regions\": [\n        \"AB\",\n        \"BC\",\n        \"MB\",\n        \"NT\",\n        \"ON\",\n        \"SK\"\n      ],\n      \"image\": {\n        \"en-US\": {\n          \"path\": \"https://dev-post-public.s3.amazonaws.com/images/default/spend.png\"\n        }\n      },\n      \"tiers\": [\n        {\n          \"qualifierValue\": 40,\n          \"awardValue\": 40\n        },\n        {\n          \"qualifierValue\": 50,\n          \"awardValue\": 50\n        },\n        {\n          \"qualifierValue\": 60,\n          \"awardValue\": 60\n        }\n      ],\n      \"canBeCombined\": true,\n      \"availability\": [\n        \"inStore\"\n      ],\n      \"offerLimitation\": \"noLimit\",\n      \"hasCustomLegal\": false,\n      \"mechanisms\": [\n        {\n          \"mechanismType\": \"noAction\"\n        }\n      ]\n    },\n    {\n      \"partnerId\": \"3db0b3e0-4134-453e-8381-08e90f2b5141\",\n      \"partnerName\": \"Rexall\",\n      \"offerType\": \"spend\",\n      \"qualifier\": \"storewide\",\n      \"awardType\": \"flatMiles\",\n      \"displayDate\": \"2019-02-22T00:00:00Z\",\n      \"startDate\": \"2019-02-22T00:00:00Z\",\n      \"endDate\": \"2019-02-22T23:59:00Z\",\n      \"displayPriority\": 0,\n      \"regions\": [\n        \"AB\",\n        \"BC\",\n        \"MB\",\n        \"NT\",\n        \"ON\",\n        \"SK\"\n      ],\n      \"image\": {\n        \"en-US\": {\n          \"path\": \"https://dev-post-public.s3.amazonaws.com/images/default/spend.png\"\n        }\n      },\n      \"tiers\": [\n        {\n          \"qualifierValue\": 60,\n          \"awardValue\": 60\n        },\n        {\n          \"qualifierValue\": 70,\n          \"awardValue\": 70\n        },\n        {\n          \"qualifierValue\": 80,\n          \"awardValue\": 80\n        }\n      ],\n      \"canBeCombined\": true,\n      \"availability\": [\n        \"inStore\"\n      ],\n      \"offerLimitation\": \"noLimit\",\n      \"hasCustomLegal\": false,\n      \"mechanisms\": [\n        {\n          \"mechanismType\": \"noAction\"\n        }\n      ]\n    }\n  ]\n}", "headers": "Content-Type: application/json\nx-user-email: <EMAIL>\n", "pathVariables": {}}, {"id": "41379a49-9c23-4695-a45f-305c2bb96719", "name": "Update Offer", "url": "{{basePath}}/offer-management-api/v3/offers/{{offerId}}", "description": "", "data": [], "dataOptions": null, "dataMode": "raw", "headerData": [{"key": "Content-Type", "value": "application/json", "enabled": true}, {"key": "x-user-email", "value": "<EMAIL>", "enabled": true}], "method": "PUT", "pathVariableData": [], "queryParams": null, "auth": null, "events": null, "folder": null, "rawModeData": "{\n  \"id\": \"{{offerId}}\",\n  \"partnerId\": \"3db0b3e0-4134-453e-8381-08e90f2b5141\",\n  \"partnerName\": \"Rexall\",\n  \"offerType\": \"spend\",\n  \"qualifier\": \"storewide\",\n  \"awardType\": \"flatMiles\",\n  \"displayDate\": \"2019-02-22T00:00:00Z\",\n  \"startDate\": \"2019-02-22T00:00:00Z\",\n  \"endDate\": \"2019-02-22T23:59:00Z\",\n  \"displayPriority\": 0,\n  \"regions\": [\n    \"AB\",\n    \"BC\",\n    \"MB\",\n    \"NT\",\n    \"ON\",\n    \"SK\"\n  ],\n  \"image\": {\n    \"en-US\": {\n      \"path\": \"https://dev-post-public.s3.amazonaws.com/images/default/spend.png\"\n    }\n  },\n  \"tiers\": [\n    {\n      \"qualifierValue\": 10,\n      \"awardValue\": 10\n    },\n    {\n      \"qualifierValue\": 20,\n      \"awardValue\": 20\n    },\n    {\n      \"qualifierValue\": 40,\n      \"awardValue\": 40\n    }\n  ],\n  \"canBeCombined\": true,\n  \"availability\": [\n    \"inStore\"\n  ],\n  \"offerLimitation\": \"noLimit\",\n  \"hasCustomLegal\": false,\n  \"mechanisms\": [\n    {\n      \"mechanismType\": \"noAction\"\n    }\n  ]\n}", "headers": "Content-Type: application/json\nx-user-email: <EMAIL>\n", "pathVariables": {}}, {"id": "4625c5e5-d4a9-4e99-88ac-e3f7f67e65e4", "name": "Create offer - Multi-Product Offer", "url": "{{basePath}}/offer-management-api/v3/offers", "description": "", "data": [], "dataOptions": null, "dataMode": "raw", "headerData": [{"key": "Content-Type", "value": "application/json", "enabled": true}, {"key": "x-user-email", "value": "<EMAIL>", "enabled": true}], "method": "POST", "pathVariableData": [], "queryParams": null, "auth": null, "events": null, "folder": null, "rawModeData": "{\n  \"regions\": [\n    \"AB\",\n    \"BC\",\n    \"MB\",\n    \"NT\",\n    \"ON\",\n    \"SK\"\n  ],\n  \"hasCustomLegal\": false,\n  \"endDate\": \"2019-02-22T23:59:00Z\",\n  \"awardType\": \"multiplierMiles\",\n  \"availability\": [\n    \"online\"\n  ],\n  \"partnerUrl\": {\n    \"en-US\": \"https://www.google.ca/\",\n    \"fr-CA\": \"https://www.google.ca/\"\n  },\n  \"canBeCombined\": true,\n  \"mechanisms\": [\n    {\n      \"mechanismType\": \"barcodeUPC\",\n      \"mechanismValue\": {\n      \t\"en-US\": \"32423434111\"\n      }\n    }\n  ],\n  \"image\": {\n    \"en-US\": {\n      \"path\": \"https://dev-post-public.s3.amazonaws.com/images/partner/3db0b3e0-4134-453e-8381-08e90f2b5141/offer/1550853330603b29f118d566c3c.jpg\"\n    }\n  },\n  \"partnerName\": \"Rexall\",\n  \"issuanceCode\": \"CPR4343\",\n  \"offerLimitation\": \"perCollectorPerTransaction\",\n  \"startDate\": \"2019-02-22T00:00:00Z\",\n  \"partnerId\": \"3db0b3e0-4134-453e-8381-08e90f2b5141\",\n  \"daysToApply\": 23,\n  \"offerType\": \"buy\",\n  \"qualifier\": \"product\",\n  \"exclusions\": {\n    \"fr-CA\": \"lottery\",\n    \"en-US\": \"lottery\"\n  },\n  \"displayDate\": \"2019-02-22T00:00:00Z\",\n  \"tiers\": [\n    {\n      \"content\": [\n        {\n          \"fr-CA\": \"Coke\",\n          \"en-US\": \"Coke\"\n        },\n        {\n          \"fr-CA\": \"Sprite\",\n          \"en-US\": \"Sprite\"\n        },\n        {\n          \"fr-CA\": \"Pepsi\",\n          \"en-US\": \"Pepsi\"\n        }\n      ],\n      \"qualifierValue\": 3,\n      \"awardValue\": 2\n    }\n  ],\n  \"displayPriority\": 0,\n  \"description\": {\n    \"fr-CA\": \"SKU # 123\",\n    \"en-US\": \"SKU # 123\"\n  },\n  \"partnerBaseEarnRate\": 20\n}", "headers": "Content-Type: application/json\nx-user-email: <EMAIL>\n", "pathVariables": {}}, {"id": "4c2e39da-06cf-4b65-97a7-f9ac85c7f5f8", "name": "Migrate Data Dump", "url": "{{basePath}}/offer-management-api/v3/migration/dump", "description": "", "data": [{"key": "file", "value": "prodOffers.json", "type": "file", "enabled": true}], "dataOptions": null, "dataMode": "params", "headerData": [{"key": "Content-Type", "value": "application/json", "enabled": true}, {"key": "x-user-email", "value": "<EMAIL>", "enabled": true}], "method": "POST", "pathVariableData": [], "queryParams": null, "auth": null, "events": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["var responseJSON = JSON.parse(responseBody);", "var offerId = responseJSON.id;", "console.log(offerId);", "postman.setEnvironmentVariable(\"offerId\", offerId);"]}}], "folder": null, "headers": "Content-Type: application/json\nx-user-email: <EMAIL>\n", "pathVariables": {}}, {"id": "54195f71-0369-4894-bcf4-1fdb50914cec", "name": "Create offer - MechanismType=couponCode", "url": "{{basePath}}/offer-management-api/v3/offers", "description": "", "data": [], "dataOptions": null, "dataMode": "raw", "headerData": [{"key": "Content-Type", "value": "application/json", "enabled": true}, {"key": "x-user-email", "value": "<EMAIL>", "enabled": true}], "method": "POST", "pathVariableData": [], "queryParams": null, "auth": null, "events": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["var responseJSON = JSON.parse(responseBody);", "var offerId = responseJSON.id;", "console.log(offerId);", "postman.setEnvironmentVariable(\"offerId\", offerId);"]}}], "folder": null, "rawModeData": "{\n  \"partnerId\": \"3db0b3e0-4134-453e-8381-08e90f2b5141\",\n  \"partnerName\": \"Rexall\",\n  \"offerType\": \"spend\",\n  \"qualifier\": \"storewide\",\n  \"awardType\": \"flatMiles\",\n  \"displayDate\": \"2019-02-22T00:00:00Z\",\n  \"startDate\": \"2019-02-22T00:00:00Z\",\n  \"endDate\": \"2019-02-22T23:59:00Z\",\n  \"displayPriority\": 0,\n  \"regions\": [\n    \"AB\",\n    \"BC\",\n    \"MB\",\n    \"NT\",\n    \"ON\"\n  ],\n  \"image\": {\n    \"en-US\": {\n      \"path\": \"https://dev-post-public.s3.amazonaws.com/images/default/spend.png\"\n    }\n  },\n  \"tiers\": [\n    {\n      \"qualifierValue\": 10,\n      \"awardValue\": 10\n    },\n    {\n      \"qualifierValue\": 20,\n      \"awardValue\": 20\n    },\n    {\n      \"qualifierValue\": 30,\n      \"awardValue\": 30\n    }\n  ],\n  \"canBeCombined\": true,\n  \"availability\": [\n    \"inStore\"\n  ],\n  \"offerLimitation\": \"noLimit\",\n  \"hasCustomLegal\": false,\n  \"mechanisms\": [\n    {\n      \"mechanismType\": \"couponCode\",\n      \"mechanismValue\": {\n      \t\"en-US\": \"WELCOME1234\",\n      \t\"fr-CA\": \"BONJOUR1234\"\n      }\n    }\n  ]\n}", "headers": "Content-Type: application/json\nx-user-email: <EMAIL>\n", "pathVariables": {}}, {"id": "57ddcb04-64e7-416b-b409-d7aa4e79d87f", "name": "Create Offer with <PERSON> Fields", "url": "{{basePath}}/offer-management-api/v3/offers", "description": "", "data": [], "dataOptions": null, "dataMode": "raw", "headerData": [{"key": "Content-Type", "value": "application/json", "enabled": true}, {"key": "x-user-email", "value": "<EMAIL>", "enabled": true}], "method": "POST", "pathVariableData": [], "queryParams": null, "auth": null, "events": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["var responseJSON = JSON.parse(responseBody);", "var offerId = responseJSON.id;", "console.log(offerId);", "postman.setEnvironmentVariable(\"offerId\", offerId);"]}}], "folder": null, "rawModeData": "{\n  \"partnerId\": \"3db0b3e0-4134-453e-8381-08e90f2b5141\",\n  \"duplicatedFrom\": \"3db0b3e0-4134-453e-8381-08e90f2b5141\",\n  \"partnerName\": \"Rexall\",\n  \"offerType\": \"buy\",\n  \"qualifier\": \"product\",\n  \"awardType\": \"flatMiles\",\n  \"displayDate\": \"2019-02-22T00:00:00Z\",\n  \"startDate\": \"2019-02-22T00:00:00Z\",\n  \"endDate\": \"2019-02-22T23:59:00Z\",\n  \"issuanceCode\": \"CPR4343\",\n  \"displayPriority\": 250,\n  \"regions\": [\n    \"AB\",\n    \"BC\",\n    \"MB\",\n    \"NT\",\n    \"ON\",\n    \"SK\"\n  ],\n  \"cashierInstruction\": {\n    \"en-US\": \"Enter it manually in the POS\"\n  },\n  \"includedLocations\": [\n    {\n      \"en-US\": \"AdditionalLocation\"\n    }\n  ],\n  \"excludedLocations\": [\n    {\n      \"en-US\": \"Excluded Location1\"\n    },\n    {\n      \"en-US\": \"Excluded Location2\"\n    }\n  ],\n  \"includedBanners\": [\n    {\n      \"en-US\": \"AdditionalBanner\"\n    }\n  ],\n  \"excludedBanners\": [\n    {\n      \"en-US\": \"ExcludedBanner\"\n    }\n  ],\n  \"image\": {\n    \"en-US\": {\n      \"path\": \"https://dev-post-public.s3.amazonaws.com/images/default/amCash.png\"\n    }\n  },\n  \"promoPriority\": 0,\n  \"mechanisms\": [\n    {\n      \"mechanismType\": \"couponCode\",\n      \"mechanismValue\": {\n        \"en-US\": \"SAVE1234\"\n      },\n      \"mechanismLabel\": {\n        \"en-US\": \"Show it to Cashier\"\n      },\n      \"mechanismText\": {\n        \"en-US\": \"Scan above\"\n      }\n    }\n  ],\n  \"tiers\": [\n    {\n      \"awardValue\": 20,\n      \"qualifierValue\": 95,\n      \"content\": [\n\t    {\n\t      \"en-US\": \"ExcludedBanner\"\n\t    }\n\t]\n    }\n  ],\n  \"canBeCombined\": false,\n  \"combinationsText\": {\n    \"en-US\": \"Cannot be combined with blah blah\"\n  },\n  \"exclusions\": {\n    \"en-US\": \"Lottery\"\n  },\n  \"availability\": [\n    \"inStore\",\n    \"online\"\n  ],\n  \"partnerUrl\": {\n  \t\"en-US\": \"http://google.ca/en\"\n  },\n  \"offerLimitation\": \"custom\",\n  \"offerLimitationText\": {\n    \"en-US\": \"One per moon cycle EN\"\n  },\n  \"daysToApply\": 20,\n  \"trademarkInfo\": {\n    \"en-US\": \"We are the best EN\"\n  },\n  \"description\": {\n    \"en-US\": \"Description\"\n  },\n  \"partnerLegalName\": {\n    \"en-US\": \"My Legal Name EN\"\n  },\n  \"hasCustomLegal\": false,\n  \"tags\": [\n    \"shopTheBlock\"\n  ]\n}", "headers": "Content-Type: application/json\nx-user-email: <EMAIL>\n", "pathVariables": {}}, {"id": "581f63af-7898-46a4-ab39-65f6c1327a03", "name": "Publish Updated Offer", "url": "{{basePath}}/offer-management-api/v3/offers/publish", "description": "", "data": [], "dataOptions": null, "dataMode": "raw", "headerData": [{"key": "Content-Type", "value": "application/json", "enabled": true}, {"key": "x-user-email", "value": "<EMAIL>", "enabled": true}], "method": "POST", "pathVariableData": [], "queryParams": null, "auth": null, "events": null, "folder": null, "rawModeData": "{\n  \"id\": \"{{offerId}}\"\n}", "headers": "Content-Type: application/json\nx-user-email: <EMAIL>\n", "pathVariables": {}}, {"id": "620d2211-0383-43d0-843f-84e6c98cfbf6", "name": "Publish Offer Bad Request", "url": "{{basePath}}/offer-management-api/v3/offers/publish", "description": "", "data": [], "dataOptions": null, "dataMode": "raw", "headerData": [{"key": "Content-Type", "value": "application/json", "enabled": true}, {"key": "x-user-email", "value": "<EMAIL>", "enabled": true}], "method": "POST", "pathVariableData": [], "queryParams": null, "auth": null, "events": null, "folder": null, "rawModeData": "{\n  \"id\": \"ghghgfjghjghjghjghjghjghjghjghjghj<PERSON>jkiukuik<PERSON>\"\n}", "headers": "Content-Type: application/json\nx-user-email: <EMAIL>\n", "pathVariables": {}}, {"id": "74ddd7d9-a5bd-4536-8daf-3afbb7a0c7be", "name": "Get All Offers", "url": "{{basePath}}/offer-management-api/v3/offers", "description": "", "data": null, "dataOptions": null, "dataMode": null, "headerData": [{"key": "Content-Type", "value": "application/json", "enabled": true}, {"key": "x-user-email", "value": "<EMAIL>", "enabled": true}], "method": "GET", "pathVariableData": [], "queryParams": null, "auth": null, "events": null, "folder": null, "headers": "Content-Type: application/json\nx-user-email: <EMAIL>\n", "pathVariables": {}}, {"id": "76996a29-abb3-4ee8-802e-b92025eb1ddc", "name": "Publish PLU Offer", "url": "{{basePath}}/offer-management-api/v3/offers/publish", "description": "", "data": [], "dataOptions": null, "dataMode": "raw", "headerData": [{"key": "Content-Type", "value": "application/json", "enabled": true}, {"key": "x-user-email", "value": "<EMAIL>", "enabled": true}], "method": "POST", "pathVariableData": [], "queryParams": null, "auth": null, "events": null, "folder": null, "rawModeData": "{\n  \"id\": \"{{offerId}}\"\n}", "headers": "Content-Type: application/json\nx-user-email: <EMAIL>\n", "pathVariables": {}}, {"id": "781177a6-6dd0-47be-9525-02a213885840", "name": "Update Offer with <PERSON> Fields", "url": "{{basePath}}/offer-management-api/v3/offers", "description": "", "data": [], "dataOptions": null, "dataMode": "raw", "headerData": [{"key": "Content-Type", "value": "application/json", "enabled": true}, {"key": "x-user-email", "value": "<EMAIL>", "enabled": true}], "method": "POST", "pathVariableData": [], "queryParams": null, "auth": null, "events": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["var responseJSON = JSON.parse(responseBody);", "var offerId = responseJSON.id;", "console.log(offerId);", "postman.setEnvironmentVariable(\"offerId\", offerId);"]}}], "folder": null, "rawModeData": "{\n  \"partnerId\": \"3db0b3e0-4134-453e-8381-08e90f2b5141\",\n  \"duplicatedFrom\": \"3db0b3e0-4134-453e-8381-08e90f2b5141\",\n  \"partnerName\": \"Rexall\",\n  \"offerType\": \"buy\",\n  \"qualifier\": \"product\",\n  \"awardType\": \"flatMiles\",\n  \"displayDate\": \"2019-02-22T00:00:00Z\",\n  \"startDate\": \"2019-02-22T00:00:00Z\",\n  \"endDate\": \"2019-02-22T23:59:00Z\",\n  \"issuanceCode\": \"CPR4343\",\n  \"displayPriority\": 250,\n  \"regions\": [\n    \"AB\",\n    \"BC\",\n    \"MB\",\n    \"NT\",\n    \"ON\",\n    \"SK\"\n  ],\n  \"cashierInstruction\": {\n    \"en-US\": \"Enter it manually in the POS\"\n  },\n  \"includedLocations\": [\n    {\n      \"en-US\": \"AdditionalLocation\"\n    }\n  ],\n  \"excludedLocations\": [\n    {\n      \"en-US\": \"Excluded Location1\"\n    },\n    {\n      \"en-US\": \"Excluded Location2\"\n    }\n  ],\n  \"includedBanners\": [\n    {\n      \"en-US\": \"AdditionalBanner\"\n    }\n  ],\n  \"excludedBanners\": [\n    {\n      \"en-US\": \"ExcludedBanner\"\n    }\n  ],\n  \"image\": {\n    \"en-US\": {\n      \"path\": \"https://dev-post-public.s3.amazonaws.com/images/default/amCash.png\"\n    }\n  },\n  \"promoPriority\": 0,\n  \"mechanisms\": [\n    {\n      \"mechanismType\": \"couponCode\",\n      \"mechanismValue\": {\n        \"en-US\": \"SAVE1234\"\n      },\n      \"mechanismLabel\": {\n        \"en-US\": \"Show it to Cashier\"\n      },\n      \"mechanismText\": {\n        \"en-US\": \"Scan above\"\n      }\n    }\n  ],\n  \"tiers\": [\n    {\n      \"awardValue\": 20,\n      \"qualifierValue\": 95,\n      \"content\": [\n\t    {\n\t      \"en-US\": \"ExcludedBanner\"\n\t    }\n\t]\n    }\n  ],\n  \"canBeCombined\": false,\n  \"combinationsText\": {\n    \"en-US\": \"Cannot be combined with blah blah\"\n  },\n  \"exclusions\": {\n    \"en-US\": \"Lottery\"\n  },\n  \"availability\": [\n    \"inStore\",\n    \"online\"\n  ],\n  \"partnerUrl\": {\n  \t\"en-US\": \"http://google.ca/en\"\n  },\n  \"offerLimitation\": \"custom\",\n  \"offerLimitationText\": {\n    \"en-US\": \"One per moon cycle EN\"\n  },\n  \"daysToApply\": 20,\n  \"trademarkInfo\": {\n    \"en-US\": \"We are the best EN\"\n  },\n  \"description\": {\n    \"en-US\": \"Description\"\n  },\n  \"partnerLegalName\": {\n    \"en-US\": \"My Legal Name EN\"\n  },\n  \"hasCustomLegal\": false,\n  \"tags\": [\n    \"shopTheBlock\"\n  ]\n}", "headers": "Content-Type: application/json\nx-user-email: <EMAIL>\n", "pathVariables": {}}, {"id": "8deefbda-5466-4e22-809f-e4c3faa57f6b", "name": "Publish Coupon Code Offer", "url": "{{basePath}}/offer-management-api/v3/offers/publish", "description": "", "data": [], "dataOptions": null, "dataMode": "raw", "headerData": [{"key": "Content-Type", "value": "application/json", "enabled": true}, {"key": "x-user-email", "value": "<EMAIL>", "enabled": true}], "method": "POST", "pathVariableData": [], "queryParams": null, "auth": null, "events": null, "folder": null, "rawModeData": "{\n  \"id\": \"{{offerId}}\"\n}", "headers": "Content-Type: application/json\nx-user-email: <EMAIL>\n", "pathVariables": {}}, {"id": "8f059c1d-08ed-4951-a6f5-081c58675caf", "name": "Create offer - MechanismType=plu", "url": "{{basePath}}/offer-management-api/v3/offers", "description": "", "data": [], "dataOptions": null, "dataMode": "raw", "headerData": [{"key": "Content-Type", "value": "application/json", "enabled": true}, {"key": "x-user-email", "value": "<EMAIL>", "enabled": true}], "method": "POST", "pathVariableData": [], "queryParams": null, "auth": null, "events": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["var responseJSON = JSON.parse(responseBody);", "var offerId = responseJSON.id;", "console.log(offerId);", "postman.setEnvironmentVariable(\"offerId\", offerId);"]}}], "folder": null, "rawModeData": "{\n  \"partnerId\": \"3db0b3e0-4134-453e-8381-08e90f2b5141\",\n  \"partnerName\": \"Rexall\",\n  \"offerType\": \"spend\",\n  \"qualifier\": \"storewide\",\n  \"awardType\": \"flatMiles\",\n  \"displayDate\": \"2019-02-22T00:00:00Z\",\n  \"startDate\": \"2019-02-22T00:00:00Z\",\n  \"endDate\": \"2019-02-22T23:59:00Z\",\n  \"displayPriority\": 0,\n  \"regions\": [\n    \"AB\",\n    \"BC\",\n    \"MB\",\n    \"NT\",\n    \"ON\"\n  ],\n  \"image\": {\n    \"en-US\": {\n      \"path\": \"https://dev-post-public.s3.amazonaws.com/images/default/spend.png\"\n    }\n  },\n  \"tiers\": [\n    {\n      \"qualifierValue\": 10,\n      \"awardValue\": 10\n    },\n    {\n      \"qualifierValue\": 20,\n      \"awardValue\": 20\n    },\n    {\n      \"qualifierValue\": 30,\n      \"awardValue\": 30\n    }\n  ],\n  \"canBeCombined\": true,\n  \"availability\": [\n    \"inStore\"\n  ],\n  \"offerLimitation\": \"noLimit\",\n  \"hasCustomLegal\": false,\n  \"mechanisms\": [\n    {\n      \"mechanismType\": \"plu\",\n      \"mechanismValue\": {\n      \t\"en-US\": \"12345c25-457e-46f9-9744-188fb6118ed0\"\n      }\n    }\n  ]\n}", "headers": "Content-Type: application/json\nx-user-email: <EMAIL>\n", "pathVariables": {}}, {"id": "91b633d4-5680-4432-9530-2f262bbb755c", "name": "Update Offer Bad Request", "url": "{{basePath}}/offer-management-api/v3/offers/{{offerId}}", "description": "", "data": [], "dataOptions": null, "dataMode": "raw", "headerData": [{"key": "Content-Type", "value": "application/json", "enabled": true}, {"key": "x-user-email", "value": "<EMAIL>", "enabled": true}], "method": "PUT", "pathVariableData": [], "queryParams": null, "auth": null, "events": null, "folder": null, "rawModeData": "{\n  \"id\": \"{{offerId}}\",\n  \"partnerId\": \"3db0b3e0-4134-453e-8381-08e90f2b5141\",\n  \"partnerName\": \"Rexall\",\n  \"offerType\": \"spend\",\n  \"qualifier\": \"storewides\",\n  \"awardType\": \"flatMiless\",\n  \"displayDate\": \"2019-02-22T00:00:00Z\",\n  \"startDate\": \"2019-02-22T00:00:00Z\",\n  \"endDate\": \"2019-02-22T23:59:00Z\",\n  \"displayPriority\": 0,\n  \"regions\": [\n    \"AB\",\n    \"BC\",\n    \"MB\",\n    \"NT\",\n    \"ON\",\n    \"SK\"\n  ],\n  \"image\": {\n    \"en-US\": {\n      \"path\": \"https://dev-post-public.s3.amazonaws.com/images/default/spend.png\"\n    }\n  },\n  \"tiers\": [\n    {\n      \"qualifierValue\": 10,\n      \"awardValue\": 10\n    },\n    {\n      \"qualifierValue\": 20,\n      \"awardValue\": 20\n    },\n    {\n      \"qualifierValue\": 40,\n      \"awardValue\": 40\n    }\n  ],\n  \"canBeCombined\": true,\n  \"availability\": [\n    \"inStore\"\n  ],\n  \"offerLimitation\": \"noLimit\",\n  \"hasCustomLegal\": false,\n  \"mechanisms\": [\n    {\n      \"mechanismType\": \"noAction\"\n    }\n  ]\n}", "headers": "Content-Type: application/json\nx-user-email: <EMAIL>\n", "pathVariables": {}}, {"id": "991fb3ff-b9d0-4620-a85c-fc968b8e6441", "name": "Create offer - Tiered Product Offer", "url": "{{basePath}}/offer-management-api/v3/offers", "description": "", "data": [], "dataOptions": null, "dataMode": "raw", "headerData": [{"key": "Content-Type", "value": "application/json", "enabled": true}, {"key": "x-user-email", "value": "<EMAIL>", "enabled": true}], "method": "POST", "pathVariableData": [], "queryParams": null, "auth": null, "events": null, "folder": null, "rawModeData": "{\n  \"regions\": [\n    \"AB\",\n    \"BC\",\n    \"MB\",\n    \"NT\",\n    \"ON\",\n    \"SK\"\n  ],\n  \"hasCustomLegal\": false,\n  \"endDate\": \"2019-02-22T23:59:00Z\",\n  \"awardType\": \"multiplierMiles\",\n  \"availability\": [\n    \"online\"\n  ],\n  \"partnerUrl\": {\n    \"en-US\": \"https://www.google.ca/\",\n    \"fr-CA\": \"https://www.google.ca/\"\n  },\n  \"canBeCombined\": true,\n  \"mechanisms\": [\n    {\n      \"mechanismType\": \"barcodeUPC\",\n      \"mechanismValue\": {\n      \t\"en-US\": \"43242342344\"\n      }\n    }\n  ],\n  \"image\": {\n    \"en-US\": {\n      \"path\": \"https://dev-post-public.s3.amazonaws.com/images/partner/3db0b3e0-4134-453e-8381-08e90f2b5141/offer/1550853330603b29f118d566c3c.jpg\"\n    }\n  },\n  \"partnerName\": \"Rexall\",\n  \"issuanceCode\": \"CPR4343\",\n  \"offerLimitation\": \"perCollectorPerTransaction\",\n  \"startDate\": \"2019-02-22T00:00:00Z\",\n  \"partnerId\": \"3db0b3e0-4134-453e-8381-08e90f2b5141\",\n  \"daysToApply\": 23,\n  \"offerType\": \"buy\",\n  \"qualifier\": \"product\",\n  \"exclusions\": {\n    \"fr-CA\": \"lottery\",\n    \"en-US\": \"lottery\"\n  },\n  \"displayDate\": \"2019-02-22T00:00:00Z\",\n  \"tiers\": [\n    {\n      \"content\": [\n        {\n          \"fr-CA\": \"Coke Diet\",\n          \"en-US\": \"Coke Diet\"\n        }\n      ],\n      \"qualifierValue\": 2,\n      \"awardValue\": 10\n    },\n        {\n      \"content\": [\n        {\n          \"fr-CA\": \"Coke Zero\",\n          \"en-US\": \"Coke Zero\"\n        }\n      ],\n      \"qualifierValue\": 2,\n      \"awardValue\": 20\n    },\n        {\n      \"content\": [\n        {\n          \"fr-CA\": \"Coke Regular\",\n          \"en-US\": \"Coke Regular\"\n        }\n      ],\n      \"qualifierValue\": 2,\n      \"awardValue\": 30\n    }\n  ],\n  \"displayPriority\": 0,\n  \"description\": {\n    \"fr-CA\": \"SKU # 123\",\n    \"en-US\": \"SKU # 123\"\n  },\n  \"partnerBaseEarnRate\": 20,\n \"massOffer\":true,\n  \"offerCategory1\": \"CatOne\",\n  \"offerCategory2\": \"CatTwo\",\n  \"offerCategory3\": \"CatThree\",\n  \"productName\": \"productName\",\n  \"productBrand\": \"productBrand\"\n}", "headers": "Content-Type: application/json\nx-user-email: <EMAIL>\n", "pathVariables": {}}, {"id": "bcdf681a-573e-4103-9837-01606bd0b629", "name": "Get All Jobs", "url": "{{basePath}}/offer-management-api/v3/offers/jobs", "description": "", "data": null, "dataOptions": null, "dataMode": null, "headerData": [{"key": "x-user-email", "value": "<EMAIL>", "enabled": true}], "method": "GET", "pathVariableData": [], "queryParams": null, "auth": null, "events": null, "folder": null, "headers": "x-user-email: <EMAIL>\n", "pathVariables": {}}, {"id": "c4989688-c902-4c1a-8b40-d43be01f0d4a", "name": "Publish offer", "url": "{{basePath}}/offer-management-api/v3/offers/publish", "description": "", "data": [], "dataOptions": null, "dataMode": "raw", "headerData": [{"key": "Content-Type", "value": "application/json", "enabled": true}, {"key": "x-user-email", "value": "<EMAIL>", "enabled": true}], "method": "POST", "pathVariableData": [], "queryParams": null, "auth": null, "events": null, "folder": null, "rawModeData": "{\n  \"id\": \"{{offerId}}\"\n}", "headers": "Content-Type: application/json\nx-user-email: <EMAIL>\n", "pathVariables": {}}, {"id": "d1420b5a-0f43-4bf2-8892-b8a64778a7ee", "name": "Delete Offer", "url": "{{basePath}}/offer-management-api/v3/offers/delete-multiple", "description": "", "data": [], "dataOptions": {"raw": {"language": "json"}}, "dataMode": "raw", "headerData": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "description": "", "type": "text"}, {"key": "x-user-email", "value": "<EMAIL>", "description": "", "type": "text", "enabled": true}], "method": "POST", "pathVariableData": [], "queryParams": [], "auth": null, "events": [], "folder": null, "rawModeData": "[\"{{offerId}}\"]", "headers": "Content-Type: application/json\nx-user-email: <EMAIL>\n", "pathVariables": {}}, {"id": "d7bbdbbb-eb30-4d5b-9ccf-1491abdda0c4", "name": "Migrate Data", "url": "{{basePath}}/offer-management-api/v3/migration/migrate", "description": "", "data": [{"key": "file", "value": "prodOffers.json", "type": "file", "enabled": true}], "dataOptions": null, "dataMode": "params", "headerData": [{"key": "Content-Type", "value": "application/json", "enabled": true}, {"key": "x-user-email", "value": "<EMAIL>", "enabled": true}], "method": "POST", "pathVariableData": [], "queryParams": null, "auth": null, "events": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["var responseJSON = JSON.parse(responseBody);", "var messages = responseJSON.messages;", "var ids = [];", "for (var i = 0; i < messages.length; i++) {", "    var splitArray = messages[i].split(\"id=\")", "    var id = splitArray[splitArray.length - 1]", "    if (ids.indexOf(id) > 0) {", "        console.log(\"Found Duplicate\")", "    }", "    //console.log(id)", "    ids.push(id)", "}", "console.log(messages.length);", "console.log(ids.length);", "console.log(ids);"]}}], "folder": null, "headers": "Content-Type: application/json\nx-user-email: <EMAIL>\n", "pathVariables": {}}, {"id": "fb6cf10e-2635-407f-bbf3-e007447f63f7", "name": "Bulk Create 50 Offers", "url": "{{basePath}}/offer-management-api/v3/offers/bulk", "description": "", "data": [], "dataOptions": null, "dataMode": "raw", "headerData": [{"key": "Content-Type", "value": "application/json", "enabled": true}, {"key": "x-user-email", "value": "<EMAIL>", "enabled": true}], "method": "POST", "pathVariableData": [], "queryParams": null, "auth": null, "events": null, "folder": null, "rawModeData": "{\n  \"bulkName\": \"TestBatch\",\n  \"offers\": [\n    {\n      \"partnerId\": \"3db0b3e0-4134-453e-8381-08e90f2b5141\",\n      \"partnerName\": \"Rexall\",\n      \"offerType\": \"spend\",\n      \"qualifier\": \"storewide\",\n      \"awardType\": \"flatMiles\",\n      \"displayDate\": \"2019-02-22T00:00:00Z\",\n      \"startDate\": \"2019-02-22T00:00:00Z\",\n      \"endDate\": \"2019-02-22T23:59:00Z\",\n      \"displayPriority\": 0,\n      \"regions\": [\n        \"AB\",\n        \"BC\",\n        \"MB\",\n        \"NT\",\n        \"ON\",\n        \"SK\"\n      ],\n      \"image\": {\n        \"en-US\": {\n          \"path\": \"https://dev-post-public.s3.amazonaws.com/images/default/spend.png\"\n        }\n      },\n      \"tiers\": [\n        {\n          \"qualifierValue\": 10,\n          \"awardValue\": 10\n        },\n        {\n          \"qualifierValue\": 20,\n          \"awardValue\": 20\n        },\n        {\n          \"qualifierValue\": 30,\n          \"awardValue\": 30\n        }\n      ],\n      \"canBeCombined\": true,\n      \"availability\": [\n        \"inStore\"\n      ],\n      \"offerLimitation\": \"noLimit\",\n      \"hasCustomLegal\": false,\n      \"mechanisms\": [\n        {\n          \"mechanismType\": \"noAction\"\n        }\n      ]\n    },\n    {\n      \"partnerId\": \"3db0b3e0-4134-453e-8381-08e90f2b5141\",\n      \"partnerName\": \"Rexall\",\n      \"offerType\": \"spend\",\n      \"qualifier\": \"storewide\",\n      \"awardType\": \"flatMiles\",\n      \"displayDate\": \"2019-02-22T00:00:00Z\",\n      \"startDate\": \"2019-02-22T00:00:00Z\",\n      \"endDate\": \"2019-02-22T23:59:00Z\",\n      \"displayPriority\": 0,\n      \"regions\": [\n        \"AB\",\n        \"BC\",\n        \"MB\",\n        \"NT\",\n        \"ON\",\n        \"SK\"\n      ],\n      \"image\": {\n        \"en-US\": {\n          \"path\": \"https://dev-post-public.s3.amazonaws.com/images/default/spend.png\"\n        }\n      },\n      \"tiers\": [\n        {\n          \"qualifierValue\": 10,\n          \"awardValue\": 10\n        },\n        {\n          \"qualifierValue\": 20,\n          \"awardValue\": 20\n        },\n        {\n          \"qualifierValue\": 30,\n          \"awardValue\": 30\n        }\n      ],\n      \"canBeCombined\": true,\n      \"availability\": [\n        \"inStore\"\n      ],\n      \"offerLimitation\": \"noLimit\",\n      \"hasCustomLegal\": false,\n      \"mechanisms\": [\n        {\n          \"mechanismType\": \"noAction\"\n        }\n      ]\n    },\n    {\n      \"partnerId\": \"3db0b3e0-4134-453e-8381-08e90f2b5141\",\n      \"partnerName\": \"Rexall\",\n      \"offerType\": \"spend\",\n      \"qualifier\": \"storewide\",\n      \"awardType\": \"flatMiles\",\n      \"displayDate\": \"2019-02-22T00:00:00Z\",\n      \"startDate\": \"2019-02-22T00:00:00Z\",\n      \"endDate\": \"2019-02-22T23:59:00Z\",\n      \"displayPriority\": 0,\n      \"regions\": [\n        \"AB\",\n        \"BC\",\n        \"MB\",\n        \"NT\",\n        \"ON\",\n        \"SK\"\n      ],\n      \"image\": {\n        \"en-US\": {\n          \"path\": \"https://dev-post-public.s3.amazonaws.com/images/default/spend.png\"\n        }\n      },\n      \"tiers\": [\n        {\n          \"qualifierValue\": 10,\n          \"awardValue\": 10\n        },\n        {\n          \"qualifierValue\": 20,\n          \"awardValue\": 20\n        },\n        {\n          \"qualifierValue\": 30,\n          \"awardValue\": 30\n        }\n      ],\n      \"canBeCombined\": true,\n      \"availability\": [\n        \"inStore\"\n      ],\n      \"offerLimitation\": \"noLimit\",\n      \"hasCustomLegal\": false,\n      \"mechanisms\": [\n        {\n          \"mechanismType\": \"noAction\"\n        }\n      ]\n    },\n    {\n      \"partnerId\": \"3db0b3e0-4134-453e-8381-08e90f2b5141\",\n      \"partnerName\": \"Rexall\",\n      \"offerType\": \"spend\",\n      \"qualifier\": \"storewide\",\n      \"awardType\": \"flatMiles\",\n      \"displayDate\": \"2019-02-22T00:00:00Z\",\n      \"startDate\": \"2019-02-22T00:00:00Z\",\n      \"endDate\": \"2019-02-22T23:59:00Z\",\n      \"displayPriority\": 0,\n      \"regions\": [\n        \"AB\",\n        \"BC\",\n        \"MB\",\n        \"NT\",\n        \"ON\",\n        \"SK\"\n      ],\n      \"image\": {\n        \"en-US\": {\n          \"path\": \"https://dev-post-public.s3.amazonaws.com/images/default/spend.png\"\n        }\n      },\n      \"tiers\": [\n        {\n          \"qualifierValue\": 10,\n          \"awardValue\": 10\n        },\n        {\n          \"qualifierValue\": 20,\n          \"awardValue\": 20\n        },\n        {\n          \"qualifierValue\": 30,\n          \"awardValue\": 30\n        }\n      ],\n      \"canBeCombined\": true,\n      \"availability\": [\n        \"inStore\"\n      ],\n      \"offerLimitation\": \"noLimit\",\n      \"hasCustomLegal\": false,\n      \"mechanisms\": [\n        {\n          \"mechanismType\": \"noAction\"\n        }\n      ]\n    },\n    {\n      \"partnerId\": \"3db0b3e0-4134-453e-8381-08e90f2b5141\",\n      \"partnerName\": \"Rexall\",\n      \"offerType\": \"spend\",\n      \"qualifier\": \"storewide\",\n      \"awardType\": \"flatMiles\",\n      \"displayDate\": \"2019-02-22T00:00:00Z\",\n      \"startDate\": \"2019-02-22T00:00:00Z\",\n      \"endDate\": \"2019-02-22T23:59:00Z\",\n      \"displayPriority\": 0,\n      \"regions\": [\n        \"AB\",\n        \"BC\",\n        \"MB\",\n        \"NT\",\n        \"ON\",\n        \"SK\"\n      ],\n      \"image\": {\n        \"en-US\": {\n          \"path\": \"https://dev-post-public.s3.amazonaws.com/images/default/spend.png\"\n        }\n      },\n      \"tiers\": [\n        {\n          \"qualifierValue\": 10,\n          \"awardValue\": 10\n        },\n        {\n          \"qualifierValue\": 20,\n          \"awardValue\": 20\n        },\n        {\n          \"qualifierValue\": 30,\n          \"awardValue\": 30\n        }\n      ],\n      \"canBeCombined\": true,\n      \"availability\": [\n        \"inStore\"\n      ],\n      \"offerLimitation\": \"noLimit\",\n      \"hasCustomLegal\": false,\n      \"mechanisms\": [\n        {\n          \"mechanismType\": \"noAction\"\n        }\n      ]\n    },\n    {\n      \"partnerId\": \"3db0b3e0-4134-453e-8381-08e90f2b5141\",\n      \"partnerName\": \"Rexall\",\n      \"offerType\": \"spend\",\n      \"qualifier\": \"storewide\",\n      \"awardType\": \"flatMiles\",\n      \"displayDate\": \"2019-02-22T00:00:00Z\",\n      \"startDate\": \"2019-02-22T00:00:00Z\",\n      \"endDate\": \"2019-02-22T23:59:00Z\",\n      \"displayPriority\": 0,\n      \"regions\": [\n        \"AB\",\n        \"BC\",\n        \"MB\",\n        \"NT\",\n        \"ON\",\n        \"SK\"\n      ],\n      \"image\": {\n        \"en-US\": {\n          \"path\": \"https://dev-post-public.s3.amazonaws.com/images/default/spend.png\"\n        }\n      },\n      \"tiers\": [\n        {\n          \"qualifierValue\": 10,\n          \"awardValue\": 10\n        },\n        {\n          \"qualifierValue\": 20,\n          \"awardValue\": 20\n        },\n        {\n          \"qualifierValue\": 30,\n          \"awardValue\": 30\n        }\n      ],\n      \"canBeCombined\": true,\n      \"availability\": [\n        \"inStore\"\n      ],\n      \"offerLimitation\": \"noLimit\",\n      \"hasCustomLegal\": false,\n      \"mechanisms\": [\n        {\n          \"mechanismType\": \"noAction\"\n        }\n      ]\n    },\n    {\n      \"partnerId\": \"3db0b3e0-4134-453e-8381-08e90f2b5141\",\n      \"partnerName\": \"Rexall\",\n      \"offerType\": \"spend\",\n      \"qualifier\": \"storewide\",\n      \"awardType\": \"flatMiles\",\n      \"displayDate\": \"2019-02-22T00:00:00Z\",\n      \"startDate\": \"2019-02-22T00:00:00Z\",\n      \"endDate\": \"2019-02-22T23:59:00Z\",\n      \"displayPriority\": 0,\n      \"regions\": [\n        \"AB\",\n        \"BC\",\n        \"MB\",\n        \"NT\",\n        \"ON\",\n        \"SK\"\n      ],\n      \"image\": {\n        \"en-US\": {\n          \"path\": \"https://dev-post-public.s3.amazonaws.com/images/default/spend.png\"\n        }\n      },\n      \"tiers\": [\n        {\n          \"qualifierValue\": 10,\n          \"awardValue\": 10\n        },\n        {\n          \"qualifierValue\": 20,\n          \"awardValue\": 20\n        },\n        {\n          \"qualifierValue\": 30,\n          \"awardValue\": 30\n        }\n      ],\n      \"canBeCombined\": true,\n      \"availability\": [\n        \"inStore\"\n      ],\n      \"offerLimitation\": \"noLimit\",\n      \"hasCustomLegal\": false,\n      \"mechanisms\": [\n        {\n          \"mechanismType\": \"noAction\"\n        }\n      ]\n    },\n    {\n      \"partnerId\": \"3db0b3e0-4134-453e-8381-08e90f2b5141\",\n      \"partnerName\": \"Rexall\",\n      \"offerType\": \"spend\",\n      \"qualifier\": \"storewide\",\n      \"awardType\": \"flatMiles\",\n      \"displayDate\": \"2019-02-22T00:00:00Z\",\n      \"startDate\": \"2019-02-22T00:00:00Z\",\n      \"endDate\": \"2019-02-22T23:59:00Z\",\n      \"displayPriority\": 0,\n      \"regions\": [\n        \"AB\",\n        \"BC\",\n        \"MB\",\n        \"NT\",\n        \"ON\",\n        \"SK\"\n      ],\n      \"image\": {\n        \"en-US\": {\n          \"path\": \"https://dev-post-public.s3.amazonaws.com/images/default/spend.png\"\n        }\n      },\n      \"tiers\": [\n        {\n          \"qualifierValue\": 10,\n          \"awardValue\": 10\n        },\n        {\n          \"qualifierValue\": 20,\n          \"awardValue\": 20\n        },\n        {\n          \"qualifierValue\": 30,\n          \"awardValue\": 30\n        }\n      ],\n      \"canBeCombined\": true,\n      \"availability\": [\n        \"inStore\"\n      ],\n      \"offerLimitation\": \"noLimit\",\n      \"hasCustomLegal\": false,\n      \"mechanisms\": [\n        {\n          \"mechanismType\": \"noAction\"\n        }\n      ]\n    },\n    {\n      \"partnerId\": \"3db0b3e0-4134-453e-8381-08e90f2b5141\",\n      \"partnerName\": \"Rexall\",\n      \"offerType\": \"spend\",\n      \"qualifier\": \"storewide\",\n      \"awardType\": \"flatMiles\",\n      \"displayDate\": \"2019-02-22T00:00:00Z\",\n      \"startDate\": \"2019-02-22T00:00:00Z\",\n      \"endDate\": \"2019-02-22T23:59:00Z\",\n      \"displayPriority\": 0,\n      \"regions\": [\n        \"AB\",\n        \"BC\",\n        \"MB\",\n        \"NT\",\n        \"ON\",\n        \"SK\"\n      ],\n      \"image\": {\n        \"en-US\": {\n          \"path\": \"https://dev-post-public.s3.amazonaws.com/images/default/spend.png\"\n        }\n      },\n      \"tiers\": [\n        {\n          \"qualifierValue\": 10,\n          \"awardValue\": 10\n        },\n        {\n          \"qualifierValue\": 20,\n          \"awardValue\": 20\n        },\n        {\n          \"qualifierValue\": 30,\n          \"awardValue\": 30\n        }\n      ],\n      \"canBeCombined\": true,\n      \"availability\": [\n        \"inStore\"\n      ],\n      \"offerLimitation\": \"noLimit\",\n      \"hasCustomLegal\": false,\n      \"mechanisms\": [\n        {\n          \"mechanismType\": \"noAction\"\n        }\n      ]\n    },\n    {\n      \"partnerId\": \"3db0b3e0-4134-453e-8381-08e90f2b5141\",\n      \"partnerName\": \"Rexall\",\n      \"offerType\": \"spend\",\n      \"qualifier\": \"storewide\",\n      \"awardType\": \"flatMiles\",\n      \"displayDate\": \"2019-02-22T00:00:00Z\",\n      \"startDate\": \"2019-02-22T00:00:00Z\",\n      \"endDate\": \"2019-02-22T23:59:00Z\",\n      \"displayPriority\": 0,\n      \"regions\": [\n        \"AB\",\n        \"BC\",\n        \"MB\",\n        \"NT\",\n        \"ON\",\n        \"SK\"\n      ],\n      \"image\": {\n        \"en-US\": {\n          \"path\": \"https://dev-post-public.s3.amazonaws.com/images/default/spend.png\"\n        }\n      },\n      \"tiers\": [\n        {\n          \"qualifierValue\": 10,\n          \"awardValue\": 10\n        },\n        {\n          \"qualifierValue\": 20,\n          \"awardValue\": 20\n        },\n        {\n          \"qualifierValue\": 30,\n          \"awardValue\": 30\n        }\n      ],\n      \"canBeCombined\": true,\n      \"availability\": [\n        \"inStore\"\n      ],\n      \"offerLimitation\": \"noLimit\",\n      \"hasCustomLegal\": false,\n      \"mechanisms\": [\n        {\n          \"mechanismType\": \"noAction\"\n        }\n      ]\n    },\n    {\n      \"partnerId\": \"3db0b3e0-4134-453e-8381-08e90f2b5141\",\n      \"partnerName\": \"Rexall\",\n      \"offerType\": \"spend\",\n      \"qualifier\": \"storewide\",\n      \"awardType\": \"flatMiles\",\n      \"displayDate\": \"2019-02-22T00:00:00Z\",\n      \"startDate\": \"2019-02-22T00:00:00Z\",\n      \"endDate\": \"2019-02-22T23:59:00Z\",\n      \"displayPriority\": 0,\n      \"regions\": [\n        \"AB\",\n        \"BC\",\n        \"MB\",\n        \"NT\",\n        \"ON\",\n        \"SK\"\n      ],\n      \"image\": {\n        \"en-US\": {\n          \"path\": \"https://dev-post-public.s3.amazonaws.com/images/default/spend.png\"\n        }\n      },\n      \"tiers\": [\n        {\n          \"qualifierValue\": 10,\n          \"awardValue\": 10\n        },\n        {\n          \"qualifierValue\": 20,\n          \"awardValue\": 20\n        },\n        {\n          \"qualifierValue\": 30,\n          \"awardValue\": 30\n        }\n      ],\n      \"canBeCombined\": true,\n      \"availability\": [\n        \"inStore\"\n      ],\n      \"offerLimitation\": \"noLimit\",\n      \"hasCustomLegal\": false,\n      \"mechanisms\": [\n        {\n          \"mechanismType\": \"noAction\"\n        }\n      ]\n    },\n    {\n      \"partnerId\": \"3db0b3e0-4134-453e-8381-08e90f2b5141\",\n      \"partnerName\": \"Rexall\",\n      \"offerType\": \"spend\",\n      \"qualifier\": \"storewide\",\n      \"awardType\": \"flatMiles\",\n      \"displayDate\": \"2019-02-22T00:00:00Z\",\n      \"startDate\": \"2019-02-22T00:00:00Z\",\n      \"endDate\": \"2019-02-22T23:59:00Z\",\n      \"displayPriority\": 0,\n      \"regions\": [\n        \"AB\",\n        \"BC\",\n        \"MB\",\n        \"NT\",\n        \"ON\",\n        \"SK\"\n      ],\n      \"image\": {\n        \"en-US\": {\n          \"path\": \"https://dev-post-public.s3.amazonaws.com/images/default/spend.png\"\n        }\n      },\n      \"tiers\": [\n        {\n          \"qualifierValue\": 10,\n          \"awardValue\": 10\n        },\n        {\n          \"qualifierValue\": 20,\n          \"awardValue\": 20\n        },\n        {\n          \"qualifierValue\": 30,\n          \"awardValue\": 30\n        }\n      ],\n      \"canBeCombined\": true,\n      \"availability\": [\n        \"inStore\"\n      ],\n      \"offerLimitation\": \"noLimit\",\n      \"hasCustomLegal\": false,\n      \"mechanisms\": [\n        {\n          \"mechanismType\": \"noAction\"\n        }\n      ]\n    },\n    {\n      \"partnerId\": \"3db0b3e0-4134-453e-8381-08e90f2b5141\",\n      \"partnerName\": \"Rexall\",\n      \"offerType\": \"spend\",\n      \"qualifier\": \"storewide\",\n      \"awardType\": \"flatMiles\",\n      \"displayDate\": \"2019-02-22T00:00:00Z\",\n      \"startDate\": \"2019-02-22T00:00:00Z\",\n      \"endDate\": \"2019-02-22T23:59:00Z\",\n      \"displayPriority\": 0,\n      \"regions\": [\n        \"AB\",\n        \"BC\",\n        \"MB\",\n        \"NT\",\n        \"ON\",\n        \"SK\"\n      ],\n      \"image\": {\n        \"en-US\": {\n          \"path\": \"https://dev-post-public.s3.amazonaws.com/images/default/spend.png\"\n        }\n      },\n      \"tiers\": [\n        {\n          \"qualifierValue\": 10,\n          \"awardValue\": 10\n        },\n        {\n          \"qualifierValue\": 20,\n          \"awardValue\": 20\n        },\n        {\n          \"qualifierValue\": 30,\n          \"awardValue\": 30\n        }\n      ],\n      \"canBeCombined\": true,\n      \"availability\": [\n        \"inStore\"\n      ],\n      \"offerLimitation\": \"noLimit\",\n      \"hasCustomLegal\": false,\n      \"mechanisms\": [\n        {\n          \"mechanismType\": \"noAction\"\n        }\n      ]\n    },\n    {\n      \"partnerId\": \"3db0b3e0-4134-453e-8381-08e90f2b5141\",\n      \"partnerName\": \"Rexall\",\n      \"offerType\": \"spend\",\n      \"qualifier\": \"storewide\",\n      \"awardType\": \"flatMiles\",\n      \"displayDate\": \"2019-02-22T00:00:00Z\",\n      \"startDate\": \"2019-02-22T00:00:00Z\",\n      \"endDate\": \"2019-02-22T23:59:00Z\",\n      \"displayPriority\": 0,\n      \"regions\": [\n        \"AB\",\n        \"BC\",\n        \"MB\",\n        \"NT\",\n        \"ON\",\n        \"SK\"\n      ],\n      \"image\": {\n        \"en-US\": {\n          \"path\": \"https://dev-post-public.s3.amazonaws.com/images/default/spend.png\"\n        }\n      },\n      \"tiers\": [\n        {\n          \"qualifierValue\": 10,\n          \"awardValue\": 10\n        },\n        {\n          \"qualifierValue\": 20,\n          \"awardValue\": 20\n        },\n        {\n          \"qualifierValue\": 30,\n          \"awardValue\": 30\n        }\n      ],\n      \"canBeCombined\": true,\n      \"availability\": [\n        \"inStore\"\n      ],\n      \"offerLimitation\": \"noLimit\",\n      \"hasCustomLegal\": false,\n      \"mechanisms\": [\n        {\n          \"mechanismType\": \"noAction\"\n        }\n      ]\n    },\n    {\n      \"partnerId\": \"3db0b3e0-4134-453e-8381-08e90f2b5141\",\n      \"partnerName\": \"Rexall\",\n      \"offerType\": \"spend\",\n      \"qualifier\": \"storewide\",\n      \"awardType\": \"flatMiles\",\n      \"displayDate\": \"2019-02-22T00:00:00Z\",\n      \"startDate\": \"2019-02-22T00:00:00Z\",\n      \"endDate\": \"2019-02-22T23:59:00Z\",\n      \"displayPriority\": 0,\n      \"regions\": [\n        \"AB\",\n        \"BC\",\n        \"MB\",\n        \"NT\",\n        \"ON\",\n        \"SK\"\n      ],\n      \"image\": {\n        \"en-US\": {\n          \"path\": \"https://dev-post-public.s3.amazonaws.com/images/default/spend.png\"\n        }\n      },\n      \"tiers\": [\n        {\n          \"qualifierValue\": 10,\n          \"awardValue\": 10\n        },\n        {\n          \"qualifierValue\": 20,\n          \"awardValue\": 20\n        },\n        {\n          \"qualifierValue\": 30,\n          \"awardValue\": 30\n        }\n      ],\n      \"canBeCombined\": true,\n      \"availability\": [\n        \"inStore\"\n      ],\n      \"offerLimitation\": \"noLimit\",\n      \"hasCustomLegal\": false,\n      \"mechanisms\": [\n        {\n          \"mechanismType\": \"noAction\"\n        }\n      ]\n    },\n    {\n      \"partnerId\": \"3db0b3e0-4134-453e-8381-08e90f2b5141\",\n      \"partnerName\": \"Rexall\",\n      \"offerType\": \"spend\",\n      \"qualifier\": \"storewide\",\n      \"awardType\": \"flatMiles\",\n      \"displayDate\": \"2019-02-22T00:00:00Z\",\n      \"startDate\": \"2019-02-22T00:00:00Z\",\n      \"endDate\": \"2019-02-22T23:59:00Z\",\n      \"displayPriority\": 0,\n      \"regions\": [\n        \"AB\",\n        \"BC\",\n        \"MB\",\n        \"NT\",\n        \"ON\",\n        \"SK\"\n      ],\n      \"image\": {\n        \"en-US\": {\n          \"path\": \"https://dev-post-public.s3.amazonaws.com/images/default/spend.png\"\n        }\n      },\n      \"tiers\": [\n        {\n          \"qualifierValue\": 10,\n          \"awardValue\": 10\n        },\n        {\n          \"qualifierValue\": 20,\n          \"awardValue\": 20\n        },\n        {\n          \"qualifierValue\": 30,\n          \"awardValue\": 30\n        }\n      ],\n      \"canBeCombined\": true,\n      \"availability\": [\n        \"inStore\"\n      ],\n      \"offerLimitation\": \"noLimit\",\n      \"hasCustomLegal\": false,\n      \"mechanisms\": [\n        {\n          \"mechanismType\": \"noAction\"\n        }\n      ]\n    },\n    {\n      \"partnerId\": \"3db0b3e0-4134-453e-8381-08e90f2b5141\",\n      \"partnerName\": \"Rexall\",\n      \"offerType\": \"spend\",\n      \"qualifier\": \"storewide\",\n      \"awardType\": \"flatMiles\",\n      \"displayDate\": \"2019-02-22T00:00:00Z\",\n      \"startDate\": \"2019-02-22T00:00:00Z\",\n      \"endDate\": \"2019-02-22T23:59:00Z\",\n      \"displayPriority\": 0,\n      \"regions\": [\n        \"AB\",\n        \"BC\",\n        \"MB\",\n        \"NT\",\n        \"ON\",\n        \"SK\"\n      ],\n      \"image\": {\n        \"en-US\": {\n          \"path\": \"https://dev-post-public.s3.amazonaws.com/images/default/spend.png\"\n        }\n      },\n      \"tiers\": [\n        {\n          \"qualifierValue\": 10,\n          \"awardValue\": 10\n        },\n        {\n          \"qualifierValue\": 20,\n          \"awardValue\": 20\n        },\n        {\n          \"qualifierValue\": 30,\n          \"awardValue\": 30\n        }\n      ],\n      \"canBeCombined\": true,\n      \"availability\": [\n        \"inStore\"\n      ],\n      \"offerLimitation\": \"noLimit\",\n      \"hasCustomLegal\": false,\n      \"mechanisms\": [\n        {\n          \"mechanismType\": \"noAction\"\n        }\n      ]\n    },\n    {\n      \"partnerId\": \"3db0b3e0-4134-453e-8381-08e90f2b5141\",\n      \"partnerName\": \"Rexall\",\n      \"offerType\": \"spend\",\n      \"qualifier\": \"storewide\",\n      \"awardType\": \"flatMiles\",\n      \"displayDate\": \"2019-02-22T00:00:00Z\",\n      \"startDate\": \"2019-02-22T00:00:00Z\",\n      \"endDate\": \"2019-02-22T23:59:00Z\",\n      \"displayPriority\": 0,\n      \"regions\": [\n        \"AB\",\n        \"BC\",\n        \"MB\",\n        \"NT\",\n        \"ON\",\n        \"SK\"\n      ],\n      \"image\": {\n        \"en-US\": {\n          \"path\": \"https://dev-post-public.s3.amazonaws.com/images/default/spend.png\"\n        }\n      },\n      \"tiers\": [\n        {\n          \"qualifierValue\": 10,\n          \"awardValue\": 10\n        },\n        {\n          \"qualifierValue\": 20,\n          \"awardValue\": 20\n        },\n        {\n          \"qualifierValue\": 30,\n          \"awardValue\": 30\n        }\n      ],\n      \"canBeCombined\": true,\n      \"availability\": [\n        \"inStore\"\n      ],\n      \"offerLimitation\": \"noLimit\",\n      \"hasCustomLegal\": false,\n      \"mechanisms\": [\n        {\n          \"mechanismType\": \"noAction\"\n        }\n      ]\n    },\n    {\n      \"partnerId\": \"3db0b3e0-4134-453e-8381-08e90f2b5141\",\n      \"partnerName\": \"Rexall\",\n      \"offerType\": \"spend\",\n      \"qualifier\": \"storewide\",\n      \"awardType\": \"flatMiles\",\n      \"displayDate\": \"2019-02-22T00:00:00Z\",\n      \"startDate\": \"2019-02-22T00:00:00Z\",\n      \"endDate\": \"2019-02-22T23:59:00Z\",\n      \"displayPriority\": 0,\n      \"regions\": [\n        \"AB\",\n        \"BC\",\n        \"MB\",\n        \"NT\",\n        \"ON\",\n        \"SK\"\n      ],\n      \"image\": {\n        \"en-US\": {\n          \"path\": \"https://dev-post-public.s3.amazonaws.com/images/default/spend.png\"\n        }\n      },\n      \"tiers\": [\n        {\n          \"qualifierValue\": 10,\n          \"awardValue\": 10\n        },\n        {\n          \"qualifierValue\": 20,\n          \"awardValue\": 20\n        },\n        {\n          \"qualifierValue\": 30,\n          \"awardValue\": 30\n        }\n      ],\n      \"canBeCombined\": true,\n      \"availability\": [\n        \"inStore\"\n      ],\n      \"offerLimitation\": \"noLimit\",\n      \"hasCustomLegal\": false,\n      \"mechanisms\": [\n        {\n          \"mechanismType\": \"noAction\"\n        }\n      ]\n    },\n    {\n      \"partnerId\": \"3db0b3e0-4134-453e-8381-08e90f2b5141\",\n      \"partnerName\": \"Rexall\",\n      \"offerType\": \"spend\",\n      \"qualifier\": \"storewide\",\n      \"awardType\": \"flatMiles\",\n      \"displayDate\": \"2019-02-22T00:00:00Z\",\n      \"startDate\": \"2019-02-22T00:00:00Z\",\n      \"endDate\": \"2019-02-22T23:59:00Z\",\n      \"displayPriority\": 0,\n      \"regions\": [\n        \"AB\",\n        \"BC\",\n        \"MB\",\n        \"NT\",\n        \"ON\",\n        \"SK\"\n      ],\n      \"image\": {\n        \"en-US\": {\n          \"path\": \"https://dev-post-public.s3.amazonaws.com/images/default/spend.png\"\n        }\n      },\n      \"tiers\": [\n        {\n          \"qualifierValue\": 10,\n          \"awardValue\": 10\n        },\n        {\n          \"qualifierValue\": 20,\n          \"awardValue\": 20\n        },\n        {\n          \"qualifierValue\": 30,\n          \"awardValue\": 30\n        }\n      ],\n      \"canBeCombined\": true,\n      \"availability\": [\n        \"inStore\"\n      ],\n      \"offerLimitation\": \"noLimit\",\n      \"hasCustomLegal\": false,\n      \"mechanisms\": [\n        {\n          \"mechanismType\": \"noAction\"\n        }\n      ]\n    },\n    {\n      \"partnerId\": \"3db0b3e0-4134-453e-8381-08e90f2b5141\",\n      \"partnerName\": \"Rexall\",\n      \"offerType\": \"spend\",\n      \"qualifier\": \"storewide\",\n      \"awardType\": \"flatMiles\",\n      \"displayDate\": \"2019-02-22T00:00:00Z\",\n      \"startDate\": \"2019-02-22T00:00:00Z\",\n      \"endDate\": \"2019-02-22T23:59:00Z\",\n      \"displayPriority\": 0,\n      \"regions\": [\n        \"AB\",\n        \"BC\",\n        \"MB\",\n        \"NT\",\n        \"ON\",\n        \"SK\"\n      ],\n      \"image\": {\n        \"en-US\": {\n          \"path\": \"https://dev-post-public.s3.amazonaws.com/images/default/spend.png\"\n        }\n      },\n      \"tiers\": [\n        {\n          \"qualifierValue\": 10,\n          \"awardValue\": 10\n        },\n        {\n          \"qualifierValue\": 20,\n          \"awardValue\": 20\n        },\n        {\n          \"qualifierValue\": 30,\n          \"awardValue\": 30\n        }\n      ],\n      \"canBeCombined\": true,\n      \"availability\": [\n        \"inStore\"\n      ],\n      \"offerLimitation\": \"noLimit\",\n      \"hasCustomLegal\": false,\n      \"mechanisms\": [\n        {\n          \"mechanismType\": \"noAction\"\n        }\n      ]\n    },\n    {\n      \"partnerId\": \"3db0b3e0-4134-453e-8381-08e90f2b5141\",\n      \"partnerName\": \"Rexall\",\n      \"offerType\": \"spend\",\n      \"qualifier\": \"storewide\",\n      \"awardType\": \"flatMiles\",\n      \"displayDate\": \"2019-02-22T00:00:00Z\",\n      \"startDate\": \"2019-02-22T00:00:00Z\",\n      \"endDate\": \"2019-02-22T23:59:00Z\",\n      \"displayPriority\": 0,\n      \"regions\": [\n        \"AB\",\n        \"BC\",\n        \"MB\",\n        \"NT\",\n        \"ON\",\n        \"SK\"\n      ],\n      \"image\": {\n        \"en-US\": {\n          \"path\": \"https://dev-post-public.s3.amazonaws.com/images/default/spend.png\"\n        }\n      },\n      \"tiers\": [\n        {\n          \"qualifierValue\": 10,\n          \"awardValue\": 10\n        },\n        {\n          \"qualifierValue\": 20,\n          \"awardValue\": 20\n        },\n        {\n          \"qualifierValue\": 30,\n          \"awardValue\": 30\n        }\n      ],\n      \"canBeCombined\": true,\n      \"availability\": [\n        \"inStore\"\n      ],\n      \"offerLimitation\": \"noLimit\",\n      \"hasCustomLegal\": false,\n      \"mechanisms\": [\n        {\n          \"mechanismType\": \"noAction\"\n        }\n      ]\n    },\n    {\n      \"partnerId\": \"3db0b3e0-4134-453e-8381-08e90f2b5141\",\n      \"partnerName\": \"Rexall\",\n      \"offerType\": \"spend\",\n      \"qualifier\": \"storewide\",\n      \"awardType\": \"flatMiles\",\n      \"displayDate\": \"2019-02-22T00:00:00Z\",\n      \"startDate\": \"2019-02-22T00:00:00Z\",\n      \"endDate\": \"2019-02-22T23:59:00Z\",\n      \"displayPriority\": 0,\n      \"regions\": [\n        \"AB\",\n        \"BC\",\n        \"MB\",\n        \"NT\",\n        \"ON\",\n        \"SK\"\n      ],\n      \"image\": {\n        \"en-US\": {\n          \"path\": \"https://dev-post-public.s3.amazonaws.com/images/default/spend.png\"\n        }\n      },\n      \"tiers\": [\n        {\n          \"qualifierValue\": 10,\n          \"awardValue\": 10\n        },\n        {\n          \"qualifierValue\": 20,\n          \"awardValue\": 20\n        },\n        {\n          \"qualifierValue\": 30,\n          \"awardValue\": 30\n        }\n      ],\n      \"canBeCombined\": true,\n      \"availability\": [\n        \"inStore\"\n      ],\n      \"offerLimitation\": \"noLimit\",\n      \"hasCustomLegal\": false,\n      \"mechanisms\": [\n        {\n          \"mechanismType\": \"noAction\"\n        }\n      ]\n    },\n    {\n      \"partnerId\": \"3db0b3e0-4134-453e-8381-08e90f2b5141\",\n      \"partnerName\": \"Rexall\",\n      \"offerType\": \"spend\",\n      \"qualifier\": \"storewide\",\n      \"awardType\": \"flatMiles\",\n      \"displayDate\": \"2019-02-22T00:00:00Z\",\n      \"startDate\": \"2019-02-22T00:00:00Z\",\n      \"endDate\": \"2019-02-22T23:59:00Z\",\n      \"displayPriority\": 0,\n      \"regions\": [\n        \"AB\",\n        \"BC\",\n        \"MB\",\n        \"NT\",\n        \"ON\",\n        \"SK\"\n      ],\n      \"image\": {\n        \"en-US\": {\n          \"path\": \"https://dev-post-public.s3.amazonaws.com/images/default/spend.png\"\n        }\n      },\n      \"tiers\": [\n        {\n          \"qualifierValue\": 10,\n          \"awardValue\": 10\n        },\n        {\n          \"qualifierValue\": 20,\n          \"awardValue\": 20\n        },\n        {\n          \"qualifierValue\": 30,\n          \"awardValue\": 30\n        }\n      ],\n      \"canBeCombined\": true,\n      \"availability\": [\n        \"inStore\"\n      ],\n      \"offerLimitation\": \"noLimit\",\n      \"hasCustomLegal\": false,\n      \"mechanisms\": [\n        {\n          \"mechanismType\": \"noAction\"\n        }\n      ]\n    },\n    {\n      \"partnerId\": \"3db0b3e0-4134-453e-8381-08e90f2b5141\",\n      \"partnerName\": \"Rexall\",\n      \"offerType\": \"spend\",\n      \"qualifier\": \"storewide\",\n      \"awardType\": \"flatMiles\",\n      \"displayDate\": \"2019-02-22T00:00:00Z\",\n      \"startDate\": \"2019-02-22T00:00:00Z\",\n      \"endDate\": \"2019-02-22T23:59:00Z\",\n      \"displayPriority\": 0,\n      \"regions\": [\n        \"AB\",\n        \"BC\",\n        \"MB\",\n        \"NT\",\n        \"ON\",\n        \"SK\"\n      ],\n      \"image\": {\n        \"en-US\": {\n          \"path\": \"https://dev-post-public.s3.amazonaws.com/images/default/spend.png\"\n        }\n      },\n      \"tiers\": [\n        {\n          \"qualifierValue\": 10,\n          \"awardValue\": 10\n        },\n        {\n          \"qualifierValue\": 20,\n          \"awardValue\": 20\n        },\n        {\n          \"qualifierValue\": 30,\n          \"awardValue\": 30\n        }\n      ],\n      \"canBeCombined\": true,\n      \"availability\": [\n        \"inStore\"\n      ],\n      \"offerLimitation\": \"noLimit\",\n      \"hasCustomLegal\": false,\n      \"mechanisms\": [\n        {\n          \"mechanismType\": \"noAction\"\n        }\n      ]\n    },\n    {\n      \"partnerId\": \"3db0b3e0-4134-453e-8381-08e90f2b5141\",\n      \"partnerName\": \"Rexall\",\n      \"offerType\": \"spend\",\n      \"qualifier\": \"storewide\",\n      \"awardType\": \"flatMiles\",\n      \"displayDate\": \"2019-02-22T00:00:00Z\",\n      \"startDate\": \"2019-02-22T00:00:00Z\",\n      \"endDate\": \"2019-02-22T23:59:00Z\",\n      \"displayPriority\": 0,\n      \"regions\": [\n        \"AB\",\n        \"BC\",\n        \"MB\",\n        \"NT\",\n        \"ON\",\n        \"SK\"\n      ],\n      \"image\": {\n        \"en-US\": {\n          \"path\": \"https://dev-post-public.s3.amazonaws.com/images/default/spend.png\"\n        }\n      },\n      \"tiers\": [\n        {\n          \"qualifierValue\": 10,\n          \"awardValue\": 10\n        },\n        {\n          \"qualifierValue\": 20,\n          \"awardValue\": 20\n        },\n        {\n          \"qualifierValue\": 30,\n          \"awardValue\": 30\n        }\n      ],\n      \"canBeCombined\": true,\n      \"availability\": [\n        \"inStore\"\n      ],\n      \"offerLimitation\": \"noLimit\",\n      \"hasCustomLegal\": false,\n      \"mechanisms\": [\n        {\n          \"mechanismType\": \"noAction\"\n        }\n      ]\n    },\n    {\n      \"partnerId\": \"3db0b3e0-4134-453e-8381-08e90f2b5141\",\n      \"partnerName\": \"Rexall\",\n      \"offerType\": \"spend\",\n      \"qualifier\": \"storewide\",\n      \"awardType\": \"flatMiles\",\n      \"displayDate\": \"2019-02-22T00:00:00Z\",\n      \"startDate\": \"2019-02-22T00:00:00Z\",\n      \"endDate\": \"2019-02-22T23:59:00Z\",\n      \"displayPriority\": 0,\n      \"regions\": [\n        \"AB\",\n        \"BC\",\n        \"MB\",\n        \"NT\",\n        \"ON\",\n        \"SK\"\n      ],\n      \"image\": {\n        \"en-US\": {\n          \"path\": \"https://dev-post-public.s3.amazonaws.com/images/default/spend.png\"\n        }\n      },\n      \"tiers\": [\n        {\n          \"qualifierValue\": 10,\n          \"awardValue\": 10\n        },\n        {\n          \"qualifierValue\": 20,\n          \"awardValue\": 20\n        },\n        {\n          \"qualifierValue\": 30,\n          \"awardValue\": 30\n        }\n      ],\n      \"canBeCombined\": true,\n      \"availability\": [\n        \"inStore\"\n      ],\n      \"offerLimitation\": \"noLimit\",\n      \"hasCustomLegal\": false,\n      \"mechanisms\": [\n        {\n          \"mechanismType\": \"noAction\"\n        }\n      ]\n    },\n    {\n      \"partnerId\": \"3db0b3e0-4134-453e-8381-08e90f2b5141\",\n      \"partnerName\": \"Rexall\",\n      \"offerType\": \"spend\",\n      \"qualifier\": \"storewide\",\n      \"awardType\": \"flatMiles\",\n      \"displayDate\": \"2019-02-22T00:00:00Z\",\n      \"startDate\": \"2019-02-22T00:00:00Z\",\n      \"endDate\": \"2019-02-22T23:59:00Z\",\n      \"displayPriority\": 0,\n      \"regions\": [\n        \"AB\",\n        \"BC\",\n        \"MB\",\n        \"NT\",\n        \"ON\",\n        \"SK\"\n      ],\n      \"image\": {\n        \"en-US\": {\n          \"path\": \"https://dev-post-public.s3.amazonaws.com/images/default/spend.png\"\n        }\n      },\n      \"tiers\": [\n        {\n          \"qualifierValue\": 10,\n          \"awardValue\": 10\n        },\n        {\n          \"qualifierValue\": 20,\n          \"awardValue\": 20\n        },\n        {\n          \"qualifierValue\": 30,\n          \"awardValue\": 30\n        }\n      ],\n      \"canBeCombined\": true,\n      \"availability\": [\n        \"inStore\"\n      ],\n      \"offerLimitation\": \"noLimit\",\n      \"hasCustomLegal\": false,\n      \"mechanisms\": [\n        {\n          \"mechanismType\": \"noAction\"\n        }\n      ]\n    },\n    {\n      \"partnerId\": \"3db0b3e0-4134-453e-8381-08e90f2b5141\",\n      \"partnerName\": \"Rexall\",\n      \"offerType\": \"spend\",\n      \"qualifier\": \"storewide\",\n      \"awardType\": \"flatMiles\",\n      \"displayDate\": \"2019-02-22T00:00:00Z\",\n      \"startDate\": \"2019-02-22T00:00:00Z\",\n      \"endDate\": \"2019-02-22T23:59:00Z\",\n      \"displayPriority\": 0,\n      \"regions\": [\n        \"AB\",\n        \"BC\",\n        \"MB\",\n        \"NT\",\n        \"ON\",\n        \"SK\"\n      ],\n      \"image\": {\n        \"en-US\": {\n          \"path\": \"https://dev-post-public.s3.amazonaws.com/images/default/spend.png\"\n        }\n      },\n      \"tiers\": [\n        {\n          \"qualifierValue\": 10,\n          \"awardValue\": 10\n        },\n        {\n          \"qualifierValue\": 20,\n          \"awardValue\": 20\n        },\n        {\n          \"qualifierValue\": 30,\n          \"awardValue\": 30\n        }\n      ],\n      \"canBeCombined\": true,\n      \"availability\": [\n        \"inStore\"\n      ],\n      \"offerLimitation\": \"noLimit\",\n      \"hasCustomLegal\": false,\n      \"mechanisms\": [\n        {\n          \"mechanismType\": \"noAction\"\n        }\n      ]\n    },\n    {\n      \"partnerId\": \"3db0b3e0-4134-453e-8381-08e90f2b5141\",\n      \"partnerName\": \"Rexall\",\n      \"offerType\": \"spend\",\n      \"qualifier\": \"storewide\",\n      \"awardType\": \"flatMiles\",\n      \"displayDate\": \"2019-02-22T00:00:00Z\",\n      \"startDate\": \"2019-02-22T00:00:00Z\",\n      \"endDate\": \"2019-02-22T23:59:00Z\",\n      \"displayPriority\": 0,\n      \"regions\": [\n        \"AB\",\n        \"BC\",\n        \"MB\",\n        \"NT\",\n        \"ON\",\n        \"SK\"\n      ],\n      \"image\": {\n        \"en-US\": {\n          \"path\": \"https://dev-post-public.s3.amazonaws.com/images/default/spend.png\"\n        }\n      },\n      \"tiers\": [\n        {\n          \"qualifierValue\": 10,\n          \"awardValue\": 10\n        },\n        {\n          \"qualifierValue\": 20,\n          \"awardValue\": 20\n        },\n        {\n          \"qualifierValue\": 30,\n          \"awardValue\": 30\n        }\n      ],\n      \"canBeCombined\": true,\n      \"availability\": [\n        \"inStore\"\n      ],\n      \"offerLimitation\": \"noLimit\",\n      \"hasCustomLegal\": false,\n      \"mechanisms\": [\n        {\n          \"mechanismType\": \"noAction\"\n        }\n      ]\n    },\n    {\n      \"partnerId\": \"3db0b3e0-4134-453e-8381-08e90f2b5141\",\n      \"partnerName\": \"Rexall\",\n      \"offerType\": \"spend\",\n      \"qualifier\": \"storewide\",\n      \"awardType\": \"flatMiles\",\n      \"displayDate\": \"2019-02-22T00:00:00Z\",\n      \"startDate\": \"2019-02-22T00:00:00Z\",\n      \"endDate\": \"2019-02-22T23:59:00Z\",\n      \"displayPriority\": 0,\n      \"regions\": [\n        \"AB\",\n        \"BC\",\n        \"MB\",\n        \"NT\",\n        \"ON\",\n        \"SK\"\n      ],\n      \"image\": {\n        \"en-US\": {\n          \"path\": \"https://dev-post-public.s3.amazonaws.com/images/default/spend.png\"\n        }\n      },\n      \"tiers\": [\n        {\n          \"qualifierValue\": 10,\n          \"awardValue\": 10\n        },\n        {\n          \"qualifierValue\": 20,\n          \"awardValue\": 20\n        },\n        {\n          \"qualifierValue\": 30,\n          \"awardValue\": 30\n        }\n      ],\n      \"canBeCombined\": true,\n      \"availability\": [\n        \"inStore\"\n      ],\n      \"offerLimitation\": \"noLimit\",\n      \"hasCustomLegal\": false,\n      \"mechanisms\": [\n        {\n          \"mechanismType\": \"noAction\"\n        }\n      ]\n    },\n    {\n      \"partnerId\": \"3db0b3e0-4134-453e-8381-08e90f2b5141\",\n      \"partnerName\": \"Rexall\",\n      \"offerType\": \"spend\",\n      \"qualifier\": \"storewide\",\n      \"awardType\": \"flatMiles\",\n      \"displayDate\": \"2019-02-22T00:00:00Z\",\n      \"startDate\": \"2019-02-22T00:00:00Z\",\n      \"endDate\": \"2019-02-22T23:59:00Z\",\n      \"displayPriority\": 0,\n      \"regions\": [\n        \"AB\",\n        \"BC\",\n        \"MB\",\n        \"NT\",\n        \"ON\",\n        \"SK\"\n      ],\n      \"image\": {\n        \"en-US\": {\n          \"path\": \"https://dev-post-public.s3.amazonaws.com/images/default/spend.png\"\n        }\n      },\n      \"tiers\": [\n        {\n          \"qualifierValue\": 10,\n          \"awardValue\": 10\n        },\n        {\n          \"qualifierValue\": 20,\n          \"awardValue\": 20\n        },\n        {\n          \"qualifierValue\": 30,\n          \"awardValue\": 30\n        }\n      ],\n      \"canBeCombined\": true,\n      \"availability\": [\n        \"inStore\"\n      ],\n      \"offerLimitation\": \"noLimit\",\n      \"hasCustomLegal\": false,\n      \"mechanisms\": [\n        {\n          \"mechanismType\": \"noAction\"\n        }\n      ]\n    },\n    {\n      \"partnerId\": \"3db0b3e0-4134-453e-8381-08e90f2b5141\",\n      \"partnerName\": \"Rexall\",\n      \"offerType\": \"spend\",\n      \"qualifier\": \"storewide\",\n      \"awardType\": \"flatMiles\",\n      \"displayDate\": \"2019-02-22T00:00:00Z\",\n      \"startDate\": \"2019-02-22T00:00:00Z\",\n      \"endDate\": \"2019-02-22T23:59:00Z\",\n      \"displayPriority\": 0,\n      \"regions\": [\n        \"AB\",\n        \"BC\",\n        \"MB\",\n        \"NT\",\n        \"ON\",\n        \"SK\"\n      ],\n      \"image\": {\n        \"en-US\": {\n          \"path\": \"https://dev-post-public.s3.amazonaws.com/images/default/spend.png\"\n        }\n      },\n      \"tiers\": [\n        {\n          \"qualifierValue\": 10,\n          \"awardValue\": 10\n        },\n        {\n          \"qualifierValue\": 20,\n          \"awardValue\": 20\n        },\n        {\n          \"qualifierValue\": 30,\n          \"awardValue\": 30\n        }\n      ],\n      \"canBeCombined\": true,\n      \"availability\": [\n        \"inStore\"\n      ],\n      \"offerLimitation\": \"noLimit\",\n      \"hasCustomLegal\": false,\n      \"mechanisms\": [\n        {\n          \"mechanismType\": \"noAction\"\n        }\n      ]\n    },\n    {\n      \"partnerId\": \"3db0b3e0-4134-453e-8381-08e90f2b5141\",\n      \"partnerName\": \"Rexall\",\n      \"offerType\": \"spend\",\n      \"qualifier\": \"storewide\",\n      \"awardType\": \"flatMiles\",\n      \"displayDate\": \"2019-02-22T00:00:00Z\",\n      \"startDate\": \"2019-02-22T00:00:00Z\",\n      \"endDate\": \"2019-02-22T23:59:00Z\",\n      \"displayPriority\": 0,\n      \"regions\": [\n        \"AB\",\n        \"BC\",\n        \"MB\",\n        \"NT\",\n        \"ON\",\n        \"SK\"\n      ],\n      \"image\": {\n        \"en-US\": {\n          \"path\": \"https://dev-post-public.s3.amazonaws.com/images/default/spend.png\"\n        }\n      },\n      \"tiers\": [\n        {\n          \"qualifierValue\": 10,\n          \"awardValue\": 10\n        },\n        {\n          \"qualifierValue\": 20,\n          \"awardValue\": 20\n        },\n        {\n          \"qualifierValue\": 30,\n          \"awardValue\": 30\n        }\n      ],\n      \"canBeCombined\": true,\n      \"availability\": [\n        \"inStore\"\n      ],\n      \"offerLimitation\": \"noLimit\",\n      \"hasCustomLegal\": false,\n      \"mechanisms\": [\n        {\n          \"mechanismType\": \"noAction\"\n        }\n      ]\n    },\n    {\n      \"partnerId\": \"3db0b3e0-4134-453e-8381-08e90f2b5141\",\n      \"partnerName\": \"Rexall\",\n      \"offerType\": \"spend\",\n      \"qualifier\": \"storewide\",\n      \"awardType\": \"flatMiles\",\n      \"displayDate\": \"2019-02-22T00:00:00Z\",\n      \"startDate\": \"2019-02-22T00:00:00Z\",\n      \"endDate\": \"2019-02-22T23:59:00Z\",\n      \"displayPriority\": 0,\n      \"regions\": [\n        \"AB\",\n        \"BC\",\n        \"MB\",\n        \"NT\",\n        \"ON\",\n        \"SK\"\n      ],\n      \"image\": {\n        \"en-US\": {\n          \"path\": \"https://dev-post-public.s3.amazonaws.com/images/default/spend.png\"\n        }\n      },\n      \"tiers\": [\n        {\n          \"qualifierValue\": 10,\n          \"awardValue\": 10\n        },\n        {\n          \"qualifierValue\": 20,\n          \"awardValue\": 20\n        },\n        {\n          \"qualifierValue\": 30,\n          \"awardValue\": 30\n        }\n      ],\n      \"canBeCombined\": true,\n      \"availability\": [\n        \"inStore\"\n      ],\n      \"offerLimitation\": \"noLimit\",\n      \"hasCustomLegal\": false,\n      \"mechanisms\": [\n        {\n          \"mechanismType\": \"noAction\"\n        }\n      ]\n    },\n    {\n      \"partnerId\": \"3db0b3e0-4134-453e-8381-08e90f2b5141\",\n      \"partnerName\": \"Rexall\",\n      \"offerType\": \"spend\",\n      \"qualifier\": \"storewide\",\n      \"awardType\": \"flatMiles\",\n      \"displayDate\": \"2019-02-22T00:00:00Z\",\n      \"startDate\": \"2019-02-22T00:00:00Z\",\n      \"endDate\": \"2019-02-22T23:59:00Z\",\n      \"displayPriority\": 0,\n      \"regions\": [\n        \"AB\",\n        \"BC\",\n        \"MB\",\n        \"NT\",\n        \"ON\",\n        \"SK\"\n      ],\n      \"image\": {\n        \"en-US\": {\n          \"path\": \"https://dev-post-public.s3.amazonaws.com/images/default/spend.png\"\n        }\n      },\n      \"tiers\": [\n        {\n          \"qualifierValue\": 10,\n          \"awardValue\": 10\n        },\n        {\n          \"qualifierValue\": 20,\n          \"awardValue\": 20\n        },\n        {\n          \"qualifierValue\": 30,\n          \"awardValue\": 30\n        }\n      ],\n      \"canBeCombined\": true,\n      \"availability\": [\n        \"inStore\"\n      ],\n      \"offerLimitation\": \"noLimit\",\n      \"hasCustomLegal\": false,\n      \"mechanisms\": [\n        {\n          \"mechanismType\": \"noAction\"\n        }\n      ]\n    },\n    {\n      \"partnerId\": \"3db0b3e0-4134-453e-8381-08e90f2b5141\",\n      \"partnerName\": \"Rexall\",\n      \"offerType\": \"spend\",\n      \"qualifier\": \"storewide\",\n      \"awardType\": \"flatMiles\",\n      \"displayDate\": \"2019-02-22T00:00:00Z\",\n      \"startDate\": \"2019-02-22T00:00:00Z\",\n      \"endDate\": \"2019-02-22T23:59:00Z\",\n      \"displayPriority\": 0,\n      \"regions\": [\n        \"AB\",\n        \"BC\",\n        \"MB\",\n        \"NT\",\n        \"ON\",\n        \"SK\"\n      ],\n      \"image\": {\n        \"en-US\": {\n          \"path\": \"https://dev-post-public.s3.amazonaws.com/images/default/spend.png\"\n        }\n      },\n      \"tiers\": [\n        {\n          \"qualifierValue\": 10,\n          \"awardValue\": 10\n        },\n        {\n          \"qualifierValue\": 20,\n          \"awardValue\": 20\n        },\n        {\n          \"qualifierValue\": 30,\n          \"awardValue\": 30\n        }\n      ],\n      \"canBeCombined\": true,\n      \"availability\": [\n        \"inStore\"\n      ],\n      \"offerLimitation\": \"noLimit\",\n      \"hasCustomLegal\": false,\n      \"mechanisms\": [\n        {\n          \"mechanismType\": \"noAction\"\n        }\n      ]\n    },\n    {\n      \"partnerId\": \"3db0b3e0-4134-453e-8381-08e90f2b5141\",\n      \"partnerName\": \"Rexall\",\n      \"offerType\": \"spend\",\n      \"qualifier\": \"storewide\",\n      \"awardType\": \"flatMiles\",\n      \"displayDate\": \"2019-02-22T00:00:00Z\",\n      \"startDate\": \"2019-02-22T00:00:00Z\",\n      \"endDate\": \"2019-02-22T23:59:00Z\",\n      \"displayPriority\": 0,\n      \"regions\": [\n        \"AB\",\n        \"BC\",\n        \"MB\",\n        \"NT\",\n        \"ON\",\n        \"SK\"\n      ],\n      \"image\": {\n        \"en-US\": {\n          \"path\": \"https://dev-post-public.s3.amazonaws.com/images/default/spend.png\"\n        }\n      },\n      \"tiers\": [\n        {\n          \"qualifierValue\": 10,\n          \"awardValue\": 10\n        },\n        {\n          \"qualifierValue\": 20,\n          \"awardValue\": 20\n        },\n        {\n          \"qualifierValue\": 30,\n          \"awardValue\": 30\n        }\n      ],\n      \"canBeCombined\": true,\n      \"availability\": [\n        \"inStore\"\n      ],\n      \"offerLimitation\": \"noLimit\",\n      \"hasCustomLegal\": false,\n      \"mechanisms\": [\n        {\n          \"mechanismType\": \"noAction\"\n        }\n      ]\n    },\n    {\n      \"partnerId\": \"3db0b3e0-4134-453e-8381-08e90f2b5141\",\n      \"partnerName\": \"Rexall\",\n      \"offerType\": \"spend\",\n      \"qualifier\": \"storewide\",\n      \"awardType\": \"flatMiles\",\n      \"displayDate\": \"2019-02-22T00:00:00Z\",\n      \"startDate\": \"2019-02-22T00:00:00Z\",\n      \"endDate\": \"2019-02-22T23:59:00Z\",\n      \"displayPriority\": 0,\n      \"regions\": [\n        \"AB\",\n        \"BC\",\n        \"MB\",\n        \"NT\",\n        \"ON\",\n        \"SK\"\n      ],\n      \"image\": {\n        \"en-US\": {\n          \"path\": \"https://dev-post-public.s3.amazonaws.com/images/default/spend.png\"\n        }\n      },\n      \"tiers\": [\n        {\n          \"qualifierValue\": 10,\n          \"awardValue\": 10\n        },\n        {\n          \"qualifierValue\": 20,\n          \"awardValue\": 20\n        },\n        {\n          \"qualifierValue\": 30,\n          \"awardValue\": 30\n        }\n      ],\n      \"canBeCombined\": true,\n      \"availability\": [\n        \"inStore\"\n      ],\n      \"offerLimitation\": \"noLimit\",\n      \"hasCustomLegal\": false,\n      \"mechanisms\": [\n        {\n          \"mechanismType\": \"noAction\"\n        }\n      ]\n    },\n    {\n      \"partnerId\": \"3db0b3e0-4134-453e-8381-08e90f2b5141\",\n      \"partnerName\": \"Rexall\",\n      \"offerType\": \"spend\",\n      \"qualifier\": \"storewide\",\n      \"awardType\": \"flatMiles\",\n      \"displayDate\": \"2019-02-22T00:00:00Z\",\n      \"startDate\": \"2019-02-22T00:00:00Z\",\n      \"endDate\": \"2019-02-22T23:59:00Z\",\n      \"displayPriority\": 0,\n      \"regions\": [\n        \"AB\",\n        \"BC\",\n        \"MB\",\n        \"NT\",\n        \"ON\",\n        \"SK\"\n      ],\n      \"image\": {\n        \"en-US\": {\n          \"path\": \"https://dev-post-public.s3.amazonaws.com/images/default/spend.png\"\n        }\n      },\n      \"tiers\": [\n        {\n          \"qualifierValue\": 10,\n          \"awardValue\": 10\n        },\n        {\n          \"qualifierValue\": 20,\n          \"awardValue\": 20\n        },\n        {\n          \"qualifierValue\": 30,\n          \"awardValue\": 30\n        }\n      ],\n      \"canBeCombined\": true,\n      \"availability\": [\n        \"inStore\"\n      ],\n      \"offerLimitation\": \"noLimit\",\n      \"hasCustomLegal\": false,\n      \"mechanisms\": [\n        {\n          \"mechanismType\": \"noAction\"\n        }\n      ]\n    },\n    {\n      \"partnerId\": \"3db0b3e0-4134-453e-8381-08e90f2b5141\",\n      \"partnerName\": \"Rexall\",\n      \"offerType\": \"spend\",\n      \"qualifier\": \"storewide\",\n      \"awardType\": \"flatMiles\",\n      \"displayDate\": \"2019-02-22T00:00:00Z\",\n      \"startDate\": \"2019-02-22T00:00:00Z\",\n      \"endDate\": \"2019-02-22T23:59:00Z\",\n      \"displayPriority\": 0,\n      \"regions\": [\n        \"AB\",\n        \"BC\",\n        \"MB\",\n        \"NT\",\n        \"ON\",\n        \"SK\"\n      ],\n      \"image\": {\n        \"en-US\": {\n          \"path\": \"https://dev-post-public.s3.amazonaws.com/images/default/spend.png\"\n        }\n      },\n      \"tiers\": [\n        {\n          \"qualifierValue\": 10,\n          \"awardValue\": 10\n        },\n        {\n          \"qualifierValue\": 20,\n          \"awardValue\": 20\n        },\n        {\n          \"qualifierValue\": 30,\n          \"awardValue\": 30\n        }\n      ],\n      \"canBeCombined\": true,\n      \"availability\": [\n        \"inStore\"\n      ],\n      \"offerLimitation\": \"noLimit\",\n      \"hasCustomLegal\": false,\n      \"mechanisms\": [\n        {\n          \"mechanismType\": \"noAction\"\n        }\n      ]\n    },\n    {\n      \"partnerId\": \"3db0b3e0-4134-453e-8381-08e90f2b5141\",\n      \"partnerName\": \"Rexall\",\n      \"offerType\": \"spend\",\n      \"qualifier\": \"storewide\",\n      \"awardType\": \"flatMiles\",\n      \"displayDate\": \"2019-02-22T00:00:00Z\",\n      \"startDate\": \"2019-02-22T00:00:00Z\",\n      \"endDate\": \"2019-02-22T23:59:00Z\",\n      \"displayPriority\": 0,\n      \"regions\": [\n        \"AB\",\n        \"BC\",\n        \"MB\",\n        \"NT\",\n        \"ON\",\n        \"SK\"\n      ],\n      \"image\": {\n        \"en-US\": {\n          \"path\": \"https://dev-post-public.s3.amazonaws.com/images/default/spend.png\"\n        }\n      },\n      \"tiers\": [\n        {\n          \"qualifierValue\": 10,\n          \"awardValue\": 10\n        },\n        {\n          \"qualifierValue\": 20,\n          \"awardValue\": 20\n        },\n        {\n          \"qualifierValue\": 30,\n          \"awardValue\": 30\n        }\n      ],\n      \"canBeCombined\": true,\n      \"availability\": [\n        \"inStore\"\n      ],\n      \"offerLimitation\": \"noLimit\",\n      \"hasCustomLegal\": false,\n      \"mechanisms\": [\n        {\n          \"mechanismType\": \"noAction\"\n        }\n      ]\n    },\n    {\n      \"partnerId\": \"3db0b3e0-4134-453e-8381-08e90f2b5141\",\n      \"partnerName\": \"Rexall\",\n      \"offerType\": \"spend\",\n      \"qualifier\": \"storewide\",\n      \"awardType\": \"flatMiles\",\n      \"displayDate\": \"2019-02-22T00:00:00Z\",\n      \"startDate\": \"2019-02-22T00:00:00Z\",\n      \"endDate\": \"2019-02-22T23:59:00Z\",\n      \"displayPriority\": 0,\n      \"regions\": [\n        \"AB\",\n        \"BC\",\n        \"MB\",\n        \"NT\",\n        \"ON\",\n        \"SK\"\n      ],\n      \"image\": {\n        \"en-US\": {\n          \"path\": \"https://dev-post-public.s3.amazonaws.com/images/default/spend.png\"\n        }\n      },\n      \"tiers\": [\n        {\n          \"qualifierValue\": 10,\n          \"awardValue\": 10\n        },\n        {\n          \"qualifierValue\": 20,\n          \"awardValue\": 20\n        },\n        {\n          \"qualifierValue\": 30,\n          \"awardValue\": 30\n        }\n      ],\n      \"canBeCombined\": true,\n      \"availability\": [\n        \"inStore\"\n      ],\n      \"offerLimitation\": \"noLimit\",\n      \"hasCustomLegal\": false,\n      \"mechanisms\": [\n        {\n          \"mechanismType\": \"noAction\"\n        }\n      ]\n    },\n    {\n      \"partnerId\": \"3db0b3e0-4134-453e-8381-08e90f2b5141\",\n      \"partnerName\": \"Rexall\",\n      \"offerType\": \"spend\",\n      \"qualifier\": \"storewide\",\n      \"awardType\": \"flatMiles\",\n      \"displayDate\": \"2019-02-22T00:00:00Z\",\n      \"startDate\": \"2019-02-22T00:00:00Z\",\n      \"endDate\": \"2019-02-22T23:59:00Z\",\n      \"displayPriority\": 0,\n      \"regions\": [\n        \"AB\",\n        \"BC\",\n        \"MB\",\n        \"NT\",\n        \"ON\",\n        \"SK\"\n      ],\n      \"image\": {\n        \"en-US\": {\n          \"path\": \"https://dev-post-public.s3.amazonaws.com/images/default/spend.png\"\n        }\n      },\n      \"tiers\": [\n        {\n          \"qualifierValue\": 10,\n          \"awardValue\": 10\n        },\n        {\n          \"qualifierValue\": 20,\n          \"awardValue\": 20\n        },\n        {\n          \"qualifierValue\": 30,\n          \"awardValue\": 30\n        }\n      ],\n      \"canBeCombined\": true,\n      \"availability\": [\n        \"inStore\"\n      ],\n      \"offerLimitation\": \"noLimit\",\n      \"hasCustomLegal\": false,\n      \"mechanisms\": [\n        {\n          \"mechanismType\": \"noAction\"\n        }\n      ]\n    },\n    {\n      \"partnerId\": \"3db0b3e0-4134-453e-8381-08e90f2b5141\",\n      \"partnerName\": \"Rexall\",\n      \"offerType\": \"spend\",\n      \"qualifier\": \"storewide\",\n      \"awardType\": \"flatMiles\",\n      \"displayDate\": \"2019-02-22T00:00:00Z\",\n      \"startDate\": \"2019-02-22T00:00:00Z\",\n      \"endDate\": \"2019-02-22T23:59:00Z\",\n      \"displayPriority\": 0,\n      \"regions\": [\n        \"AB\",\n        \"BC\",\n        \"MB\",\n        \"NT\",\n        \"ON\",\n        \"SK\"\n      ],\n      \"image\": {\n        \"en-US\": {\n          \"path\": \"https://dev-post-public.s3.amazonaws.com/images/default/spend.png\"\n        }\n      },\n      \"tiers\": [\n        {\n          \"qualifierValue\": 10,\n          \"awardValue\": 10\n        },\n        {\n          \"qualifierValue\": 20,\n          \"awardValue\": 20\n        },\n        {\n          \"qualifierValue\": 30,\n          \"awardValue\": 30\n        }\n      ],\n      \"canBeCombined\": true,\n      \"availability\": [\n        \"inStore\"\n      ],\n      \"offerLimitation\": \"noLimit\",\n      \"hasCustomLegal\": false,\n      \"mechanisms\": [\n        {\n          \"mechanismType\": \"noAction\"\n        }\n      ]\n    },\n    {\n      \"partnerId\": \"3db0b3e0-4134-453e-8381-08e90f2b5141\",\n      \"partnerName\": \"Rexall\",\n      \"offerType\": \"spend\",\n      \"qualifier\": \"storewide\",\n      \"awardType\": \"flatMiles\",\n      \"displayDate\": \"2019-02-22T00:00:00Z\",\n      \"startDate\": \"2019-02-22T00:00:00Z\",\n      \"endDate\": \"2019-02-22T23:59:00Z\",\n      \"displayPriority\": 0,\n      \"regions\": [\n        \"AB\",\n        \"BC\",\n        \"MB\",\n        \"NT\",\n        \"ON\",\n        \"SK\"\n      ],\n      \"image\": {\n        \"en-US\": {\n          \"path\": \"https://dev-post-public.s3.amazonaws.com/images/default/spend.png\"\n        }\n      },\n      \"tiers\": [\n        {\n          \"qualifierValue\": 10,\n          \"awardValue\": 10\n        },\n        {\n          \"qualifierValue\": 20,\n          \"awardValue\": 20\n        },\n        {\n          \"qualifierValue\": 30,\n          \"awardValue\": 30\n        }\n      ],\n      \"canBeCombined\": true,\n      \"availability\": [\n        \"inStore\"\n      ],\n      \"offerLimitation\": \"noLimit\",\n      \"hasCustomLegal\": false,\n      \"mechanisms\": [\n        {\n          \"mechanismType\": \"noAction\"\n        }\n      ]\n    },\n    {\n      \"partnerId\": \"3db0b3e0-4134-453e-8381-08e90f2b5141\",\n      \"partnerName\": \"Rexall\",\n      \"offerType\": \"spend\",\n      \"qualifier\": \"storewide\",\n      \"awardType\": \"flatMiles\",\n      \"displayDate\": \"2019-02-22T00:00:00Z\",\n      \"startDate\": \"2019-02-22T00:00:00Z\",\n      \"endDate\": \"2019-02-22T23:59:00Z\",\n      \"displayPriority\": 0,\n      \"regions\": [\n        \"AB\",\n        \"BC\",\n        \"MB\",\n        \"NT\",\n        \"ON\",\n        \"SK\"\n      ],\n      \"image\": {\n        \"en-US\": {\n          \"path\": \"https://dev-post-public.s3.amazonaws.com/images/default/spend.png\"\n        }\n      },\n      \"tiers\": [\n        {\n          \"qualifierValue\": 10,\n          \"awardValue\": 10\n        },\n        {\n          \"qualifierValue\": 20,\n          \"awardValue\": 20\n        },\n        {\n          \"qualifierValue\": 30,\n          \"awardValue\": 30\n        }\n      ],\n      \"canBeCombined\": true,\n      \"availability\": [\n        \"inStore\"\n      ],\n      \"offerLimitation\": \"noLimit\",\n      \"hasCustomLegal\": false,\n      \"mechanisms\": [\n        {\n          \"mechanismType\": \"noAction\"\n        }\n      ]\n    },\n    {\n      \"partnerId\": \"3db0b3e0-4134-453e-8381-08e90f2b5141\",\n      \"partnerName\": \"Rexall\",\n      \"offerType\": \"spend\",\n      \"qualifier\": \"storewide\",\n      \"awardType\": \"flatMiles\",\n      \"displayDate\": \"2019-02-22T00:00:00Z\",\n      \"startDate\": \"2019-02-22T00:00:00Z\",\n      \"endDate\": \"2019-02-22T23:59:00Z\",\n      \"displayPriority\": 0,\n      \"regions\": [\n        \"AB\",\n        \"BC\",\n        \"MB\",\n        \"NT\",\n        \"ON\",\n        \"SK\"\n      ],\n      \"image\": {\n        \"en-US\": {\n          \"path\": \"https://dev-post-public.s3.amazonaws.com/images/default/spend.png\"\n        }\n      },\n      \"tiers\": [\n        {\n          \"qualifierValue\": 10,\n          \"awardValue\": 10\n        },\n        {\n          \"qualifierValue\": 20,\n          \"awardValue\": 20\n        },\n        {\n          \"qualifierValue\": 30,\n          \"awardValue\": 30\n        }\n      ],\n      \"canBeCombined\": true,\n      \"availability\": [\n        \"inStore\"\n      ],\n      \"offerLimitation\": \"noLimit\",\n      \"hasCustomLegal\": false,\n      \"mechanisms\": [\n        {\n          \"mechanismType\": \"noAction\"\n        }\n      ]\n    },\n    {\n      \"partnerId\": \"3db0b3e0-4134-453e-8381-08e90f2b5141\",\n      \"partnerName\": \"Rexall\",\n      \"offerType\": \"spend\",\n      \"qualifier\": \"storewide\",\n      \"awardType\": \"flatMiles\",\n      \"displayDate\": \"2019-02-22T00:00:00Z\",\n      \"startDate\": \"2019-02-22T00:00:00Z\",\n      \"endDate\": \"2019-02-22T23:59:00Z\",\n      \"displayPriority\": 0,\n      \"regions\": [\n        \"AB\",\n        \"BC\",\n        \"MB\",\n        \"NT\",\n        \"ON\",\n        \"SK\"\n      ],\n      \"image\": {\n        \"en-US\": {\n          \"path\": \"https://dev-post-public.s3.amazonaws.com/images/default/spend.png\"\n        }\n      },\n      \"tiers\": [\n        {\n          \"qualifierValue\": 10,\n          \"awardValue\": 10\n        },\n        {\n          \"qualifierValue\": 20,\n          \"awardValue\": 20\n        },\n        {\n          \"qualifierValue\": 30,\n          \"awardValue\": 30\n        }\n      ],\n      \"canBeCombined\": true,\n      \"availability\": [\n        \"inStore\"\n      ],\n      \"offerLimitation\": \"noLimit\",\n      \"hasCustomLegal\": false,\n      \"mechanisms\": [\n        {\n          \"mechanismType\": \"noAction\"\n        }\n      ]\n    },\n    {\n      \"partnerId\": \"3db0b3e0-4134-453e-8381-08e90f2b5141\",\n      \"partnerName\": \"Rexall\",\n      \"offerType\": \"spend\",\n      \"qualifier\": \"storewide\",\n      \"awardType\": \"flatMiles\",\n      \"displayDate\": \"2019-02-22T00:00:00Z\",\n      \"startDate\": \"2019-02-22T00:00:00Z\",\n      \"endDate\": \"2019-02-22T23:59:00Z\",\n      \"displayPriority\": 0,\n      \"regions\": [\n        \"AB\",\n        \"BC\",\n        \"MB\",\n        \"NT\",\n        \"ON\",\n        \"SK\"\n      ],\n      \"image\": {\n        \"en-US\": {\n          \"path\": \"https://dev-post-public.s3.amazonaws.com/images/default/spend.png\"\n        }\n      },\n      \"tiers\": [\n        {\n          \"qualifierValue\": 10,\n          \"awardValue\": 10\n        },\n        {\n          \"qualifierValue\": 20,\n          \"awardValue\": 20\n        },\n        {\n          \"qualifierValue\": 30,\n          \"awardValue\": 30\n        }\n      ],\n      \"canBeCombined\": true,\n      \"availability\": [\n        \"inStore\"\n      ],\n      \"offerLimitation\": \"noLimit\",\n      \"hasCustomLegal\": false,\n      \"mechanisms\": [\n        {\n          \"mechanismType\": \"noAction\"\n        }\n      ]\n    }\n  ]\n}", "headers": "Content-Type: application/json\nx-user-email: <EMAIL>\n", "pathVariables": {}}]}