{"id": "c26d7eeb-88b7-45f9-b18a-de87cfbdb735", "name": "Rexall Pharma Plus", "sponsorCodes": ["RXAL"], "type": ["in-store", "cash", "load+go"], "priority": 80, "fullLogo": [{"title": "@2x", "file": {"url": "//images.ctfassets.net/7m10wxejlkq7/45ahn2PmCsowG2uu8MCUa2/2ed566fd0e36a33c1b57b367e8c28864/Rexall_PharmaPlus.png", "details": {"size": 17986, "image": {"width": 512, "height": 512}}, "fileName": "Rexall PharmaPlus.png", "contentType": "image/png"}}], "webWhiteLogo": [{"title": "@1x", "file": {"url": "//images.ctfassets.net/7m10wxejlkq7/32xeN6ZMNigMicsQicOUma/9645aeb27c412fadd73cfe482a59ef32/pharmaPlus_white_1x.png", "details": {"size": 8912, "image": {"width": 281, "height": 140}}, "fileName": "<EMAIL>", "contentType": "image/png"}}, {"title": "@2x", "file": {"url": "//images.ctfassets.net/7m10wxejlkq7/1dvqXmBjUuAMaM24uamqgQ/a5318b5d0060d6a8e42c480a404b94cb/pharmaPlus_white_2x.png", "details": {"size": 18127, "image": {"width": 562, "height": 279}}, "fileName": "<EMAIL>", "contentType": "image/png"}}, {"title": "@3x", "file": {"url": "//images.ctfassets.net/7m10wxejlkq7/1V0Xqi5V7KWOQ4YUws2S8m/de62eddc0ed6642ae76073856877cc58/pharmaPlus_white_3x.png", "details": {"size": 28133, "image": {"width": 843, "height": 418}}, "fileName": "<EMAIL>", "contentType": "image/png"}}], "webColourLogo": [{"title": "@1x", "file": {"url": "//images.ctfassets.net/7m10wxejlkq7/22nqUBg7wYe0QikqasAmuO/12df3c44f7e165513672daa7c4b68353/pharmaPlus_colour_1x.png", "details": {"size": 9768, "image": {"width": 281, "height": 140}}, "fileName": "<EMAIL>", "contentType": "image/png"}}, {"title": "@2x", "file": {"url": "//images.ctfassets.net/7m10wxejlkq7/3bA0kh4UBiQOQ6GWqYQ08E/d6b0199879a56af60e19b418e38a9c67/pharmaPlus_colour_2x.png", "details": {"size": 19755, "image": {"width": 562, "height": 279}}, "fileName": "<EMAIL>", "contentType": "image/png"}}, {"title": "@3x", "file": {"url": "//images.ctfassets.net/7m10wxejlkq7/4OttutYDPyg8cCowowuIKs/ab8ea81ada915e039ddf131766ac8176/pharmaPlus_colour_3x.png", "details": {"size": 30464, "image": {"width": 843, "height": 418}}, "fileName": "<EMAIL>", "contentType": "image/png"}}], "baseEarnRate": "1 Mile for every $20 spent per transaction", "maxCashMiles": "Use AIR MILES Cash Miles here! Up to $750 (7,125 Cash Miles) towards your purchases in-store per Collector Cash Account, per day. ", "regions": ["ab", "bc", "mb", "nt", "on", "sk"], "regionOverrides": [{"name": "Rexall Pharma Plus", "brandKey": "pharmaPlus", "fullLogo": [{"title": "@2x", "file": {"url": "//images.ctfassets.net/7m10wxejlkq7/45ahn2PmCsowG2uu8MCUa2/2ed566fd0e36a33c1b57b367e8c28864/Rexall_PharmaPlus.png", "details": {"size": 17986, "image": {"width": 512, "height": 512}}, "fileName": "Rexall PharmaPlus.png", "contentType": "image/png"}}], "regions": ["mb", "nt", "on"], "webColourLogo": [{"title": "@1x", "file": {"url": "//images.ctfassets.net/7m10wxejlkq7/22nqUBg7wYe0QikqasAmuO/12df3c44f7e165513672daa7c4b68353/pharmaPlus_colour_1x.png", "details": {"size": 9768, "image": {"width": 281, "height": 140}}, "fileName": "<EMAIL>", "contentType": "image/png"}}, {"title": "@2x", "file": {"url": "//images.ctfassets.net/7m10wxejlkq7/3bA0kh4UBiQOQ6GWqYQ08E/d6b0199879a56af60e19b418e38a9c67/pharmaPlus_colour_2x.png", "details": {"size": 19755, "image": {"width": 562, "height": 279}}, "fileName": "<EMAIL>", "contentType": "image/png"}}, {"title": "@3x", "file": {"url": "//images.ctfassets.net/7m10wxejlkq7/4OttutYDPyg8cCowowuIKs/ab8ea81ada915e039ddf131766ac8176/pharmaPlus_colour_3x.png", "details": {"size": 30464, "image": {"width": 843, "height": 418}}, "fileName": "<EMAIL>", "contentType": "image/png"}}], "webWhiteLogo": [{"title": "@1x", "file": {"url": "//images.ctfassets.net/7m10wxejlkq7/32xeN6ZMNigMicsQicOUma/9645aeb27c412fadd73cfe482a59ef32/pharmaPlus_white_1x.png", "details": {"size": 8912, "image": {"width": 281, "height": 140}}, "fileName": "<EMAIL>", "contentType": "image/png"}}, {"title": "@2x", "file": {"url": "//images.ctfassets.net/7m10wxejlkq7/1dvqXmBjUuAMaM24uamqgQ/a5318b5d0060d6a8e42c480a404b94cb/pharmaPlus_white_2x.png", "details": {"size": 18127, "image": {"width": 562, "height": 279}}, "fileName": "<EMAIL>", "contentType": "image/png"}}, {"title": "@3x", "file": {"url": "//images.ctfassets.net/7m10wxejlkq7/1V0Xqi5V7KWOQ4YUws2S8m/de62eddc0ed6642ae76073856877cc58/pharmaPlus_white_3x.png", "details": {"size": 28133, "image": {"width": 843, "height": 418}}, "fileName": "<EMAIL>", "contentType": "image/png"}}]}, {"name": "Rexall Drug Store", "brandKey": "drugStore", "regions": ["ab", "bc", "sk"], "webColourLogo": [{"title": "@1x", "file": {"url": "//images.ctfassets.net/7m10wxejlkq7/79ABHiH1zqOAAiWe0CKQEs/63335043c209bde6d086de373d15578b/drugStore_colour_1x.png", "details": {"size": 8486, "image": {"width": 281, "height": 139}}, "fileName": "<EMAIL>", "contentType": "image/png"}}, {"title": "@2x", "file": {"url": "//images.ctfassets.net/7m10wxejlkq7/5yBKzUrrNuqCuUu6Agu8Qk/587c274af40e5a2658015e30c93bae06/drugStore_colour_2x.png", "details": {"size": 17490, "image": {"width": 562, "height": 279}}, "fileName": "<EMAIL>", "contentType": "image/png"}}, {"title": "@3x", "file": {"url": "//images.ctfassets.net/7m10wxejlkq7/5UvfgtGxAQiQ8wc66w6ame/b4a4ee736b0216245045a33342b311bc/drugStore_colour_3x.png", "details": {"size": 39095, "image": {"width": 843, "height": 418}}, "fileName": "<EMAIL>", "contentType": "image/png"}}], "webWhiteLogo": [{"title": "@1x", "file": {"url": "//images.ctfassets.net/7m10wxejlkq7/6DDNOP9AzKaOoAm4OQAcK8/1dffc616fd5bf1f3fda0fb8e75621c4a/drugStore_white_1x.png", "details": {"size": 8345, "image": {"width": 281, "height": 139}}, "fileName": "<EMAIL>", "contentType": "image/png"}}, {"title": "@2x", "file": {"url": "//images.ctfassets.net/7m10wxejlkq7/1krXug3K0kCakMM2Cm6YGI/a61a3ae4c4d9db6ca2f0ace9e2dbb2fb/drugStore_white_2x.png", "details": {"size": 17452, "image": {"width": 562, "height": 279}}, "fileName": "<EMAIL>", "contentType": "image/png"}}, {"title": "@3x", "file": {"url": "//images.ctfassets.net/7m10wxejlkq7/RxCBUMpNAcYM64gQU2q8w/81cb336fb4b78b020dc32b36dd8dee41/drugStore_white_3x.png", "details": {"size": 24095, "image": {"width": 843, "height": 418}}, "fileName": "<EMAIL>", "contentType": "image/png"}}]}]}