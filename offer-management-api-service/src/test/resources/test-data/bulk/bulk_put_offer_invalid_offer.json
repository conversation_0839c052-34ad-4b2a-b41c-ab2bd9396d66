{"bulkName": "SecondOfferIsInvalidBatch", "offers": [{"id": "5678dde5-e204-439a-8a05-c6d4ee087ba5", "partnerId": "3db0b3e0-4134-453e-8381-08e90f2b5141", "partnerName": "<PERSON><PERSON>", "offerType": "buy", "programType": "airmilesshops", "qualifier": "product", "awardType": "flatMiles", "displayDate": "2019-02-22T00:00:00Z", "startDate": "2019-02-22T00:00:00Z", "endDate": "2019-02-22T23:59:00Z", "displayPriority": 0, "regions": ["AB", "BC", "MB", "NT", "ON", "SK"], "image": {"en-US": {"path": "https://dev-post-public.s3.amazonaws.com/images/default/spend.png"}}, "tiers": [{"qualifierValue": 15, "awardValue": 15}, {"qualifierValue": 25, "awardValue": 25}, {"qualifierValue": 40, "awardValue": 40}], "canBeCombined": true, "availability": ["inStore"], "offerLimitation": "noLimit", "hasCustomLegal": false, "mechanisms": [{"mechanismType": "noAction"}]}, {"id": "22342c25-457e-46f9-9744-188fb6118ed0", "partnerId": "30b767d3-179a-4280-8ab5-9749b430986d", "partnerName": "<PERSON><PERSON>", "offerType": "spend", "programType": "airmilesshops", "qualifier": "custom", "awardType": "flatMiles", "displayDate": "2019-02-22T00:00:00Z", "startDate": "2019-02-22T00:00:00Z", "endDate": "2019-02-22T23:59:00Z", "displayPriority": 2000, "regions": ["AB", "BC", "MB", "NT", "ON", "SK"], "image": {"en-US": {"path": "https://dev-post-public.s3.amazonaws.com/images/default/spend.png"}}, "tiers": [{"qualifierValue": 10, "awardValue": 10}, {"qualifierValue": 20, "awardValue": 20}, {"qualifierValue": 30, "awardValue": 30}], "canBeCombined": true, "availability": ["inStore"], "offerLimitation": "noLimit", "hasCustomLegal": false, "mechanisms": [{"mechanismType": "noAction"}]}]}