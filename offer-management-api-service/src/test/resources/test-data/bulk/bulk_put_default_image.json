{"bulkName": "BulkWithDefaultImages", "offers": [{"active": true, "availability": ["inStore"], "awardType": "cashDiscount", "baseCashRedemption": 95, "canBeCombined": true, "createdAt": "2019-11-07T15:55:14.886Z", "createdBy": "<EMAIL>", "displayDate": "2019-02-22T00:00:00.000Z", "displayPriority": 0, "endDate": "2019-02-22T23:59:00.000Z", "hasCustomLegal": false, "id": "903c313f-6b32-4ce8-9bdc-876364455e96", "massOffer": true, "eventBasedOffer": false, "mechanisms": [{"mechanismType": "noAction"}], "offerCategory1": "1ba636e3-799d-44c3-9c84-42a3df6430b0", "offerLimitation": "noLimit", "offerType": "amCashDiscount", "programType": "airmilesshops", "partnerId": "3db0b3e0-4134-453e-8381-08e90f2b5141", "partnerName": "<PERSON><PERSON>", "qualifier": "cashDiscount", "regions": ["BC", "NT", "AB", "ON", "MB"], "startDate": "2019-02-22T00:00:00.000Z", "tiers": [{"qualifierValue": 15, "awardValue": 15}, {"qualifierValue": 25, "awardValue": 25}, {"qualifierValue": 40, "awardValue": 40}]}, {"active": true, "availability": ["inStore"], "awardType": "flatMiles", "baseCashRedemption": 95, "canBeCombined": true, "displayDate": "2019-02-22T00:00:00.000Z", "displayPriority": 0, "endDate": "2019-02-22T23:59:00.000Z", "hasCustomLegal": false, "id": "7167df2e-f276-4e8a-a9e7-9d042714140d", "massOffer": true, "eventBasedOffer": false, "mechanisms": [{"mechanismType": "noAction"}], "offerCategory1": "1ba636e3-799d-44c3-9c84-42a3df6430b0", "offerLimitation": "noLimit", "offerType": "amCashEarn", "programType": "airmilesshops", "partnerId": "3db0b3e0-4134-453e-8381-08e90f2b5141", "partnerName": "<PERSON><PERSON>", "qualifier": "cashRedemption", "regions": ["BC", "NT", "AB", "ON", "MB"], "startDate": "2019-02-22T00:00:00.000Z", "tiers": [{"qualifierValue": 15, "awardValue": 15}, {"qualifierValue": 25, "awardValue": 25}, {"qualifierValue": 40, "awardValue": 40}]}, {"active": true, "availability": ["inStore"], "awardType": "flatMiles", "baseCashRedemption": 95, "canBeCombined": true, "displayDate": "2019-02-22T00:00:00.000Z", "displayPriority": 0, "endDate": "2019-02-22T23:59:00.000Z", "hasCustomLegal": false, "id": "54651a76-bb0e-4678-864f-91b9b0ecb01a", "massOffer": true, "eventBasedOffer": false, "mechanisms": [{"mechanismType": "noAction"}], "offerCategory1": "1ba636e3-799d-44c3-9c84-42a3df6430b0", "offerLimitation": "noLimit", "offerType": "custom", "partnerId": "3db0b3e0-4134-453e-8381-08e90f2b5141", "partnerName": "<PERSON><PERSON>", "qualifier": "category", "regions": ["BC", "NT", "AB", "ON", "MB"], "startDate": "2019-02-22T00:00:00.000Z", "tiers": [{"qualifierValue": 15, "awardValue": 15}, {"qualifierValue": 25, "awardValue": 25}, {"qualifierValue": 40, "awardValue": 40}]}, {"active": true, "availability": ["inStore"], "awardType": "flatMiles", "baseCashRedemption": 95, "canBeCombined": true, "displayDate": "2019-02-22T00:00:00.000Z", "displayPriority": 0, "endDate": "2019-02-22T23:59:00.000Z", "hasCustomLegal": false, "id": "91e01fa1-5412-4302-8e96-6e6c4206aa43", "massOffer": true, "eventBasedOffer": false, "mechanisms": [{"mechanismType": "noAction"}], "offerCategory1": "1ba636e3-799d-44c3-9c84-42a3df6430b0", "offerLimitation": "noLimit", "offerType": "custom", "programType": "airmilesshops", "partnerId": "3db0b3e0-4134-453e-8381-08e90f2b5141", "partnerName": "<PERSON><PERSON>", "qualifier": "storewide", "regions": ["BC", "NT", "AB", "ON", "MB"], "startDate": "2019-02-22T00:00:00.000Z", "tiers": [{"qualifierValue": 15, "awardValue": 15}, {"qualifierValue": 25, "awardValue": 25}, {"qualifierValue": 40, "awardValue": 40}]}, {"active": true, "availability": ["inStore"], "awardType": "flatMiles", "baseCashRedemption": 95, "canBeCombined": true, "displayDate": "2019-02-22T00:00:00.000Z", "displayPriority": 0, "endDate": "2019-02-22T23:59:00.000Z", "hasCustomLegal": false, "id": "2b4e723f-e7d7-4be2-8254-e0157748baa2", "massOffer": true, "eventBasedOffer": false, "mechanisms": [{"mechanismType": "noAction"}], "offerCategory1": "1ba636e3-799d-44c3-9c84-42a3df6430b0", "offerLimitation": "noLimit", "offerType": "spend", "programType": "airmilesshops", "partnerId": "3db0b3e0-4134-453e-8381-08e90f2b5141", "partnerName": "<PERSON><PERSON>", "qualifier": "storewide", "regions": ["BC", "NT", "AB", "ON", "MB"], "startDate": "2019-02-22T00:00:00.000Z", "tiers": [{"qualifierValue": 15, "awardValue": 15}, {"qualifierValue": 25, "awardValue": 25}, {"qualifierValue": 40, "awardValue": 40}]}]}