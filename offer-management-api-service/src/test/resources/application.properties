aws.region=us-east-1
aws.secret.id="local-offer-management-api-resources"
spring.datasource.masteruser.secret=local-offer-management-api-resources-master-user
spring.datasource.adminuser.secret=local-offer-management-api-resources-admin-user
spring.datasource.appuser.secret=local-offer-management-api-resources-app-user
spring.datasource.reportinguser.secret=local-offer-management-api-resources-reporting-user
spring.datasource.driverClassName=org.h2.Driver
database.writer.endpoint=mem
database.name=testdb
spring.flyway.enabled=false
spring.mvc.pathmatch.matching-strategy=ANT_PATH_MATCHER
partner.id.bank.montreal=22a2cdfd-ff82-45f6-bc94-c14a3a533922