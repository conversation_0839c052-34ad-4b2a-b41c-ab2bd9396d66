<configuration>
    <logger name="com.amazonaws.services.dynamodbv2" level="ERROR"/>
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>[%level] %date{ISO8601} %msg\n</pattern>
        </encoder>
    </appender>
    <appender name="FILE" class="ch.qos.logback.core.FileAppender">
        <file>/tmp/test.log</file>
        <append>true</append>
        <encoder>
            <pattern>[%level] %date{ISO8601} %msg\n</pattern>
        </encoder>
    </appender>

    <root level="info">
        <appender-ref ref="STDOUT"/>
        <!--<appender-ref ref="FILE" />-->
    </root>
</configuration>
