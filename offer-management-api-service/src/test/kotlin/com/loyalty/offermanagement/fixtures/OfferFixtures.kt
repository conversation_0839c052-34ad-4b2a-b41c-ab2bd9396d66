package com.loyalty.offermanagement.fixtures

import com.loyalty.offermanagement.entities.Offer
import com.loyalty.offermanagement.models.v3.inout.OfferIO
import org.mockito.Mockito
import java.time.Instant
import java.util.UUID

object OfferFixtures {

    fun createOffer(
        id: UUID = UUID.randomUUID(),
        createdAt: Instant = Instant.now(),
        publishedAt: Instant = Instant.now(),
        updatedAt: Instant = publishedAt
    ): Offer {
        val offer = Mockito.mock(Offer::class.java)
        Mockito.`when`(offer.id).thenReturn(id)
        Mockito.`when`(offer.createdAt).thenReturn(createdAt)
        Mockito.`when`(offer.publishedAt).thenReturn(publishedAt)
        Mockito.`when`(offer.updatedAt).thenReturn(updatedAt)
        return offer
    }

    fun mockOfferIO(id: UUID = UUID.randomUUID()): OfferIO {
        val offerIO = Mockito.mock(OfferIO::class.java)
        Mockito.`when`(offerIO.id).thenReturn(id.toString())
        return offerIO
    }

    fun threeOffersSortedByCreatedAtAsc(now: Instant = Instant.now()): List<Offer> =
        listOf(
            createOffer(
                UUID.fromString("b87a20f0-8966-42d3-b9b1-17b5c04b4c5f"),
                createdAt = now.minusSeconds(7200)
            ),
            createOffer(
                UUID.fromString("d4c7ff7d-6aa7-4d55-8d2f-06e3a8a4899b"),
                createdAt = now.minusSeconds(3600)
            ),
            createOffer(
                UUID.fromString("b489a3ef-509c-4564-a355-2144d8fb6b97"),
                createdAt = now.minusSeconds(1800)
            )
        )

    fun threeOffersSortedByPublishedAtAsc(now: Instant = Instant.now()): List<Offer> =
        listOf(
            createOffer(
                UUID.fromString("588a5950-8906-49b1-b18f-d1da43c5a31e"),
                publishedAt = now.minusSeconds(10800)
            ),
            createOffer(
                UUID.fromString("c0af4f71-6447-4b40-8069-01534ff5ad08"),
                publishedAt = now.minusSeconds(7200)
            ),
            createOffer(
                UUID.fromString("732a2995-6a1d-46c5-a07c-3d9b0e6683f8"),
                publishedAt = now.minusSeconds(3600)
            )
        )

    fun threeOffersSortedByUpdatedAtAsc(now: Instant = Instant.now()): List<Offer> =
        listOf(
            createOffer(
                UUID.fromString("298c2a5e-8342-4316-b7f2-89cbd51074a9"),
                updatedAt = now.minusSeconds(3600)
            ),
            createOffer(
                UUID.fromString("ba2c6b75-8492-4dad-b2f4-6f46d7dbe319"),
                updatedAt = now.minusSeconds(1800)
            ),
            createOffer(
                UUID.fromString("947edab8-c441-4adb-bb3c-34c73a7a3da3"),
                updatedAt = now.minusSeconds(600)
            )
        )
}