@file:Suppress(
        "<PERSON><PERSON><PERSON><PERSON>",
        "<PERSON><PERSON><PERSON><PERSON>",
        "FunctionNaming"
)

package com.loyalty.offermanagement.functional.v3

import com.jayway.jsonpath.JsonPath
import com.loyalty.offermanagement.functional.OfferManagementFunctionalTest
import com.loyalty.offermanagement.functional.common.GenerateData
import com.loyalty.offermanagement.functional.common.Helpers
import com.loyalty.offermanagement.functional.common.RDSLocalFixture
import com.loyalty.offermanagement.models.v3.inout.BatchJobIO
import org.junit.Assert
import org.junit.jupiter.api.BeforeAll
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Test
import org.junit.runner.RunWith
import org.mockito.junit.MockitoJUnitRunner
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.HttpEntity
import org.springframework.http.HttpMethod
import org.springframework.http.HttpStatus

@RunWith(MockitoJUnitRunner::class)

class BatchJobsTest : OfferManagementFunctionalTest() {
    @Autowired
    private lateinit var rdsLocalFixture: RDSLocalFixture

    @BeforeAll
    fun beforeAll() {
        rdsLocalFixture.dropBatchJobs()
        rdsLocalFixture.dropOffers()
        rdsLocalFixture.storeBatchOffers(
                "/test-data/bulk/data/spendAndGet.json",
                "/test-data/bulk/data/spendAndGetHighValues.json",
                "/test-data/bulk/data/spendAndGetNonTier.json"
        )
    }

    @Test
    @DisplayName("Get BulkJobs - Missing Headers - Bad Request")
    fun getJobs_unauthorized_request() {
        val resp = testRestTemplate.exchange("/offer-management-api/v3/offers/jobs",
                HttpMethod.GET, HttpEntity(null, null), String::class.java)

        Assert.assertEquals(HttpStatus.BAD_REQUEST, resp.statusCode)
    }

    @Test
    @DisplayName("Get BulkJobs - Happy Path - A Correct List and Items")
    fun getJobs_HappyPath_request() {
        val specificId = "47224121-a83c-4435-9cdc-19906810e1af"
        val specificIBatchTotalOffers = 3
        val specificBatchName = "UnitBulk"
        val specificBatchCreatedBy = "<EMAIL>"
        val resp = testRestTemplate.exchange("/offer-management-api/v3/offers/jobs",
                HttpMethod.GET, HttpEntity(null, GenerateData.createHeader()), String::class.java)
        val document = Helpers.createJsonDocument(resp.body)

        Assert.assertEquals(HttpStatus.OK, resp.statusCode)

        val batchIds: kotlin.collections.List<String> = JsonPath.read(document, "@[*].id")
        Assert.assertEquals(2, batchIds.size)
        Assert.assertTrue(batchIds.contains(specificId))

        val specificBatch: kotlin.collections.List<BatchJobIO> = JsonPath.read(document,
                "$[?(@.id=='47224121-a83c-4435-9cdc-19906810e1af')]")
        Assert.assertEquals(1, specificBatch.size)

        val name = JsonPath.read<String>(specificBatch[0], "$.name")
        Assert.assertEquals(specificBatchName, name)

        val createdBy = JsonPath.read<String>(specificBatch[0], "$.createdBy")
        Assert.assertEquals(specificBatchCreatedBy, createdBy)

        val totalOffers = JsonPath.read<Int>(specificBatch[0], "$.totalOffers")
        Assert.assertEquals(specificIBatchTotalOffers, totalOffers)
    }
}
