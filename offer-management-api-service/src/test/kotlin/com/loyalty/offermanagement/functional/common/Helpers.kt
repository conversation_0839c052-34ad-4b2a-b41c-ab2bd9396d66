@file:Suppress(
        "MagicNumber"
)

package com.loyalty.offermanagement.functional.common

import com.fasterxml.jackson.databind.ObjectMapper
import com.jayway.jsonpath.Configuration
import com.loyalty.offermanagement.entities.Offer
import org.jtwig.JtwigModel
import org.jtwig.JtwigTemplate
import org.mockito.Mockito
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import java.time.temporal.ChronoUnit
import java.util.Random

class Helpers private constructor() {
    companion object {
        private var jtwigModel = JtwigModel.newModel().with(
                "TODAY_DATE", DateTimeFormatter.ISO_DATE_TIME.format(getDateAtMidnight())
        ).with(
                "TOMORROW_DATE", DateTimeFormatter.ISO_DATE_TIME.format(getFutureDate())
        ).with(
                "YESTERDAY_DATE", DateTimeFormatter.ISO_DATE_TIME.format(getPastDate())
        ).with(
                "FEW_DAYS_IN_FUTURE_DATE", DateTimeFormatter.ISO_DATE_TIME.format(
                getFutureDate((Random().nextInt(50 - 2) + 2).toLong()))
        ).with(
                "FEW_DAYS_IN_PAST_DATE", DateTimeFormatter.ISO_DATE_TIME.format(
                getPastDate((Random().nextInt(50 - 2) + 2).toLong()))
        )

        fun isValidUUID(value: String) = value.isNotEmpty() &&
                "[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}".toRegex().matches(value)

        fun createJsonDocument(json: String?): Any = Configuration.defaultConfiguration().jsonProvider().parse(json)

        fun <T> anyMatcher(type: Class<T>): T = Mockito.any<T>(type)

        fun <T> readFileAndConvert(mapper: ObjectMapper, filePath: String, valueType: Class<T>): T {
            val template = JtwigTemplate.classpathTemplate(filePath)
            val result = template.render(Helpers.jtwigModel)
            return mapper.readValue(result, valueType)
        }

        fun readQueryParamsAndConvert(paramString: String): String {
            // will provide beginning of Yesterday till end of tomorrow, remember we end 23:59
            return paramString.replace("PASTDATE", getDate(-2).toString())
                    .replace("FUTUREDATE", getDate(1).toString())
        }

        fun readFileAndConvertOfferRDS(mapper: ObjectMapper, filePath: String): Offer {
            val offerRDS = readFileAndConvert(mapper, filePath, Offer::class.java)

            offerRDS.tiers.map {
                it.offer = offerRDS
                it.content.map { content ->
                    content.tier = it
                }
            }

            offerRDS.mechanisms.map {
                it.offer = offerRDS
            }

            return offerRDS
        }

        fun getDateAtMidnight(): LocalDateTime {
            return LocalDateTime.now().withHour(0).withMinute(0)
        }

        fun getPastDate(numDays: Long = 1): LocalDateTime = getDateAtMidnight().minus(numDays, ChronoUnit.DAYS)

        fun getFutureDate(numDays: Long = 1): LocalDateTime = getDateAtMidnight().plus(numDays, ChronoUnit.DAYS)

        fun getDate(numDays: Long = 1): LocalDate = LocalDate.now().plusDays(numDays)
    }
}
