@file:Suppress(
        "FunctionNaming"
)

package com.loyalty.offermanagement.functional

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import com.loyalty.nova.common.events.definitions.data.v3.OfferPublishedEventData
import com.loyalty.offermanagement.models.offerv2.OfferV2
import com.loyalty.offermanagement.models.v3.*
import com.loyalty.offermanagement.models.v3.inout.MechanismIO
import com.loyalty.offermanagement.models.v3.inout.OfferIO
import com.loyalty.offermanagement.services.OfferTransformService
import org.junit.Assert
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import java.io.InputStream
import java.util.Collections
import java.util.UUID
import kotlin.test.assertFailsWith

class OfferTransformServiceTest : OfferManagementFunctionalTest() {
    val expectOfferIOsStream: InputStream by lazy { this::class.java.getResourceAsStream("/extractors/expectOfferIOs.json") }

    val expectOfferIOs by lazy { this.objectMapper.readValue<List<OfferIO>>(this.expectOfferIOsStream) }

    val expectOfferIOsJson by lazy { this.objectMapper.writeValueAsString(this.expectOfferIOs) }

    val publishedOfferIOsStream: InputStream by lazy { this::class.java.getResourceAsStream("/extractors/publishedOfferIOs.json") }

    val publishedOfferIOs by lazy { this.objectMapper.readValue<List<OfferIO>>(this.publishedOfferIOsStream) }

    val expectOfferV2sStream: InputStream by lazy { this::class.java.getResourceAsStream("/extractors/expectOfferV2s.json") }

    val expectOfferV2s by lazy { this.objectMapper.readValue<List<OfferV2>>(this.expectOfferV2sStream) }

    val expectOfferV2sJson by lazy { this.objectMapper.writeValueAsString(this.expectOfferV2s) }

    val expectOfferV3sStream: InputStream by lazy { this::class.java.getResourceAsStream("/extractors/expectOfferV3s.json") }

    val expectOfferV3s by lazy { this.objectMapper.readValue<List<OfferPublishedEventData>>(this.expectOfferV3sStream) }

    val expectOfferV3sJson by lazy { this.objectMapper.writeValueAsString(this.expectOfferV3s) }

    @Autowired
    lateinit var objectMapper: ObjectMapper

    @Autowired
    lateinit var offerTransformService: OfferTransformService

    @Test
    fun test_IO_to_RDS_to_IO() {
        val actualOfferIOsJson = this.expectOfferIOs.toList()
                .map(this.offerTransformService::transform)
                .map(this.offerTransformService::transform)
                .let(this.objectMapper::writeValueAsString)

        Assert.assertEquals(this.expectOfferIOsJson, actualOfferIOsJson)
    }

    @Test
    fun test_IO_to_V2() {
        val actualOfferV2sJson = this.expectOfferIOs.toList()
                .map(this.offerTransformService::transformOfferV2)
                .let(this.objectMapper::writeValueAsString)

        Assert.assertEquals(this.expectOfferV2sJson, actualOfferV2sJson)
    }

    @Test
    fun test_IO_to_V3() {
        val actualOfferV3sJson = this.publishedOfferIOs.toList()
                .map(this.offerTransformService::transformOfferV3)
                .let(this.objectMapper::writeValueAsString)

        Assert.assertEquals(this.expectOfferV3sJson, actualOfferV3sJson)
    }

    @Test
    fun transformOfferV2_whenMechanismTypeIsOptIn_throwsException() {
        assertFailsWith(Exception::class) {
            var offerIO = OfferIO(
                    partnerId = UUID.randomUUID().toString(),
                    partnerName = UUID.randomUUID().toString(),
                    offerType = OfferType.values()[0],
                    qualifier = Qualifier.values()[0],
                    awardType = AwardType.values()[0],
                    programType = ProgramType.values()[0],
                    image = null,
                    mechanisms = Collections.singletonList(MechanismIO(mechanismType = MechanismType.OptIn)),
                    tiers = Collections.emptyList(),
                    regions = Collections.emptyList()
            )
            offerTransformService.transformOfferV2(offerIO)
        }
    }
}
