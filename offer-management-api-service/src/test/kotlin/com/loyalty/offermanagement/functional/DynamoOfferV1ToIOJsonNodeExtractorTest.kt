@file:Suppress(
        "FunctionNaming",
        "MagicNumber"
)

package com.loyalty.offermanagement.functional

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import com.loyalty.offermanagement.extractors.DynamoOfferV1ToIOJsonNodeExtractor
import com.loyalty.offermanagement.extractors.extractItems
import com.loyalty.offermanagement.models.v3.inout.OfferIO
import org.junit.Assert
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import kotlin.test.assertFailsWith

class DynamoOfferV1ToIOJsonNodeExtractorTest : OfferManagementFunctionalTest() {
    val actualOfferV1sStream by lazy { this::class.java.getResourceAsStream("/extractors/actualOfferV1s.json") }

    val expectOfferIOsStream by lazy { this::class.java.getResourceAsStream("/extractors/expectOfferIOs.json") }

    val expectOfferIOs by lazy { this.objectMapper.readValue<List<OfferIO>>(this.expectOfferIOsStream) }

    val expectOfferIOsJson by lazy { this.objectMapper.writeValueAsString(this.expectOfferIOs) }

    @Autowired
    lateinit var objectMapper: ObjectMapper

    @Test
    fun test_extractItems_andPerformValidationChecks() {
        val jsonNodeExtractor = DynamoOfferV1ToIOJsonNodeExtractor()
        val actualOfferIOResults = jsonNodeExtractor.extractItems(this.actualOfferV1sStream, this.objectMapper)

        Assert.assertEquals(8, actualOfferIOResults.size)
        Assert.assertEquals(8, actualOfferIOResults.filter(Result<OfferIO>::isSuccess).size)
        Assert.assertEquals(0, actualOfferIOResults.filter(Result<OfferIO>::isFailure).size)

        val actualOfferIOs = actualOfferIOResults.mapNotNull(Result<OfferIO>::getOrNull)
        val actualOfferIOsJson = this.objectMapper.writeValueAsString(actualOfferIOs)

        Assert.assertEquals(this.expectOfferIOsJson, actualOfferIOsJson)
    }

    @Test
    fun parseMechanismType_whenValueIsOptIn_returnsCorrectEnum() {
        assertFailsWith(Exception::class) {
            val jsonNodeExtractor = DynamoOfferV1ToIOJsonNodeExtractor()
            jsonNodeExtractor.parseMechanismType("optIn", "", "")
        }
    }
}
