@file:Suppress(
        "LongMethod"
)

package com.loyalty.offermanagement.functional.common

import com.fasterxml.jackson.databind.ObjectMapper
import com.loyalty.offermanagement.entities.BatchJob
import com.loyalty.offermanagement.entities.Offer
import com.loyalty.offermanagement.models.v3.inout.OfferIO
import com.loyalty.offermanagement.repository.BatchJobRepository
import com.loyalty.offermanagement.repository.OfferRepository
import com.loyalty.offermanagement.services.OfferTransformService
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Repository
import java.time.Instant
import java.util.Optional
import java.util.UUID

@Repository
class RDSLocalFixture {
    @Autowired
    private lateinit var mapper: ObjectMapper

    @Autowired
    private lateinit var offerRepository: OfferRepository

    @Autowired
    private lateinit var bulkJobRepository: BatchJobRepository

    @Autowired
    private lateinit var offerTransformService: OfferTransformService

    fun storeOffers(vararg offersFilePaths: String) {
        for (offerJsonPath in offersFilePaths) {
            val offerRDS = Helpers.readFileAndConvertOfferRDS(mapper, offerJsonPath)
            offerRepository.save(offerRDS)
        }
    }

    fun storeBatchOffers(vararg offersFilePaths: String) {
        var id = UUID.fromString("47224121-a83c-4435-9cdc-19906810e1af")
        val createdBy = "<EMAIL>"
        val createdAt = Instant.now()
        val batchName = "UnitBulk"

        val offersList = mutableListOf<Offer>()
        for (offerJsonPath in offersFilePaths) {
            val offerRDS = Helpers.readFileAndConvertOfferRDS(mapper, offerJsonPath)
            offersList.add(offerRDS)
        }

        val mockBulk1 = BatchJob(id = id, batchName = batchName, createdAt = createdAt, createdBy = createdBy)
        mockBulk1.offers = offersList
        bulkJobRepository.save(mockBulk1)

        id = UUID.randomUUID()
        val offerRDS = Helpers.readFileAndConvertOfferRDS(
                mapper, "/test-data/bulk/data/spendAndGetAnother.json")

        val mockBulk2 = BatchJob(id = id, batchName = "some_$batchName", createdAt = createdAt, createdBy = createdBy)
        mockBulk2.offers += offerRDS
        bulkJobRepository.save(mockBulk2)
    }

    fun getBatchJobById(id: String): Optional<BatchJob> {
        return bulkJobRepository.findById(UUID.fromString(id))
    }

    fun dropBatchJobs() {
        bulkJobRepository.deleteAll()
    }

    fun storePublishingOffer(vararg offersFilePaths: String) {
        for (offerJsonPath in offersFilePaths) {
            val inputStream = this.javaClass.getResourceAsStream(offerJsonPath)
            val offerIO = mapper.readValue(inputStream, OfferIO::class.java)

            val theIdentifier = offerJsonPath.substringAfterLast("_").substringBeforeLast(".")
            val idToUse = "$theIdentifier-a684-4afc-9864-b9ae771c70dd"
            offerIO.id = idToUse
            val offerRDS = offerTransformService.transform(offerIO)

            offerRepository.save(offerRDS)
        }
    }

    fun dropOffers() {
        offerRepository.deleteAll()
    }
}
