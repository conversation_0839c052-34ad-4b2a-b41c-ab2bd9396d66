@file:Suppress(
        "FunctionNaming",
        "LongMeth<PERSON>",
        "LargeClass"
)

package com.loyalty.offermanagement.functional.v3

import com.fasterxml.jackson.databind.ObjectMapper
import com.loyalty.offermanagement.functional.OfferManagementFunctionalTest
import com.loyalty.offermanagement.functional.common.GenerateData
import com.loyalty.offermanagement.functional.common.Helpers
import com.loyalty.offermanagement.functional.common.RDSLocalFixture
import com.loyalty.offermanagement.models.v3.OfferStatus
import com.loyalty.offermanagement.models.v3.inout.IdIO
import com.loyalty.offermanagement.models.v3.inout.OfferIO
import com.loyalty.offermanagement.services.OfferTransformService
import org.junit.Assert
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Test
import org.junit.runner.RunWith
import org.mockito.junit.MockitoJUnitRunner
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.HttpEntity
import org.springframework.http.HttpMethod
import org.springframework.http.HttpStatus
import java.util.UUID

@RunWith(MockitoJUnitRunner::class)

class PublishTest : OfferManagementFunctionalTest() {
    @Autowired
    private lateinit var rdsLocalFixture: RDSLocalFixture

    @Autowired
    private lateinit var mapper: ObjectMapper

    @Autowired
    private lateinit var offerTransformService: OfferTransformService

    @BeforeEach
    fun beforeEach() {
        rdsLocalFixture.dropBatchJobs()
        rdsLocalFixture.dropOffers()
        rdsLocalFixture.storePublishingOffer(
                "/test-data/publish_offer_data_0aaaa0bb.json",
                "/test-data/publish_offer_data_1aaaa2bb.json",
                "/test-data/publish_offer_published_2bbbb3cc.json",
                "/test-data/publish_offer_updated_3cccc4dd.json"
        )
    }

    @Test
    @DisplayName("Publish Offer - Missing Headers - Bad Request")
    fun publishOffer_unauthorized_request() {
        val publishId = IdIO("78d40164-053f-4e8c-bc5e-27a308669851")
        val resp = testRestTemplate.exchange("/offer-management-api/v3/offers/publish",
                HttpMethod.POST, HttpEntity(publishId), String::class.java)

        Assert.assertEquals(HttpStatus.BAD_REQUEST, resp.statusCode)
    }

    @Test
    @DisplayName("Publish Offer - Schema Validation - long id - Bad Request")
    fun publishOffer_schemaValidationError_request() {
        val publishId = IdIO("78d40164-053f-4e8c-bc5e-27a30866985143435435454")
        val resp = testRestTemplate.exchange("/offer-management-api/v3/offers/publish",
                HttpMethod.POST, HttpEntity(publishId), String::class.java)

        Assert.assertEquals(HttpStatus.BAD_REQUEST, resp.statusCode)
    }

    @Test
    @DisplayName("Publish Offer - Non-existent Id - Returns HttpStatus.NOT_FOUND")
    fun publishOffer_nonExistentId_404() {
        val idToUse = UUID.randomUUID().toString()

        val resp = testRestTemplate.exchange("/offer-management-api/v3/offers/publish",
                HttpMethod.GET, HttpEntity(idToUse, GenerateData.createHeader()), String::class.java)

        Assert.assertEquals(HttpStatus.BAD_REQUEST, resp.statusCode)
    }

    @Test
    @DisplayName("Publish Draft Targeted Offer (NOT massOffer) - Happy Path ")
    fun publishOffer_notMass_Skips_v2() {
        val id = "0aaaa0bb"
        val idToUse = "$id-a684-4afc-9864-b9ae771c70dd"

        val resp = testRestTemplate.exchange("/offer-management-api/v3/offers/$idToUse",
                HttpMethod.GET, HttpEntity(null, GenerateData.createHeader()), String::class.java)
        Assert.assertEquals(HttpStatus.OK, resp.statusCode)

        val publishingOffer = Helpers.createJsonDocument(resp.body)
        val publishingOfferIO = mapper.convertValue(publishingOffer, OfferIO::class.java)
        val publishingOfferV2 = offerTransformService.transformOfferV2(publishingOfferIO)
        println("Publishing Offer:")
        println(mapper.writeValueAsString(publishingOffer))
        println("Publishing Offer IO")
        println(mapper.writeValueAsString(publishingOfferIO))
        println("Publishing Offer V2")
        println(mapper.writeValueAsString(publishingOfferV2))

        Assert.assertEquals(OfferStatus.DRAFT, publishingOfferIO.status)
        Assert.assertNull(publishingOfferIO.contentfulId)
        Assert.assertNull(publishingOfferIO.detailsId)

        GenerateData.mockCreateV2(publishingOfferV2, offerV2Service)
        GenerateData.mockPublishV2(idToUse, offerV2Service)
        GenerateData.mockCategoryInfo(publishingOfferIO.offerCategory1!!, offerCategoryServicevice)
        GenerateData.mockPromotionInfo(publishingOfferIO.tags[0], promotionService)

        val doingPublish = testRestTemplate.exchange("/offer-management-api/v3/offers/publish",
                HttpMethod.POST, HttpEntity(IdIO(idToUse), GenerateData.createHeader()), String::class.java)
        Assert.assertEquals(HttpStatus.NO_CONTENT, doingPublish.statusCode)

        val respAfter = testRestTemplate.exchange("/offer-management-api/v3/offers/$idToUse",
                HttpMethod.GET, HttpEntity(null, GenerateData.createHeader()), String::class.java)
        val publishedOffer = Helpers.createJsonDocument(respAfter.body)
        val publishedOfferIO = mapper.convertValue(publishedOffer, OfferIO::class.java)

        Assert.assertNull(publishedOfferIO.contentfulId)
        Assert.assertNull(publishedOfferIO.detailsId)
        Assert.assertNotNull(publishedOfferIO.publishedAt)
        Assert.assertEquals(publishedOfferIO.publishedBy, "<EMAIL>")
    }

    @Test
    @DisplayName("Publish Draft Offer - Happy Path ")
    fun publishOffer_happyPath_DraftOfferPublished() {
        val id = "1aaaa2bb"
        val idToUse = "$id-a684-4afc-9864-b9ae771c70dd"

        val resp = testRestTemplate.exchange("/offer-management-api/v3/offers/$idToUse",
                HttpMethod.GET, HttpEntity(null, GenerateData.createHeader()), String::class.java)
        Assert.assertEquals(HttpStatus.OK, resp.statusCode)

        val publishingOffer = Helpers.createJsonDocument(resp.body)
        val publishingOfferIO = mapper.convertValue(publishingOffer, OfferIO::class.java)
        val publishingOfferV2 = offerTransformService.transformOfferV2(publishingOfferIO)

        Assert.assertEquals(OfferStatus.DRAFT, publishingOfferIO.status)
        Assert.assertNull(publishingOfferIO.contentfulId)
        Assert.assertNull(publishingOfferIO.detailsId)

        GenerateData.mockCreateV2(publishingOfferV2, offerV2Service)
        GenerateData.mockPublishV2(idToUse, offerV2Service)

        val doingPublish = testRestTemplate.exchange("/offer-management-api/v3/offers/publish",
                HttpMethod.POST, HttpEntity(IdIO(idToUse), GenerateData.createHeader()), String::class.java)
        Assert.assertEquals(HttpStatus.NO_CONTENT, doingPublish.statusCode)

        val respAfter = testRestTemplate.exchange("/offer-management-api/v3/offers/$idToUse",
                HttpMethod.GET, HttpEntity(null, GenerateData.createHeader()), String::class.java)
        val publishedOffer = Helpers.createJsonDocument(respAfter.body)
        val publishedOfferIO = mapper.convertValue(publishedOffer, OfferIO::class.java)

        Assert.assertNotNull(publishedOfferIO.publishedAt)
        Assert.assertEquals(publishedOfferIO.publishedBy, "<EMAIL>")
    }

    @Test
    @DisplayName("Re-Publish Published Offer - Happy Path ")
    fun publishOffer_happyPath_PublishedOfferPublished() {
        val id = "2bbbb3cc"
        val idToUse = "$id-a684-4afc-9864-b9ae771c70dd"
        val newPublisher = "<EMAIL>"

        val resp = testRestTemplate.exchange("/offer-management-api/v3/offers/$idToUse",
                HttpMethod.GET, HttpEntity(null, GenerateData.createHeader(newPublisher)), String::class.java)
        Assert.assertEquals(HttpStatus.OK, resp.statusCode)

        val publishingOffer = Helpers.createJsonDocument(resp.body)
        val publishingOfferIO = mapper.convertValue(publishingOffer, OfferIO::class.java)

        Assert.assertEquals(OfferStatus.PUBLISHED, publishingOfferIO.status)
        Assert.assertNotNull(publishingOfferIO.contentfulId)
        Assert.assertNotNull(publishingOfferIO.detailsId)

        GenerateData.mockPublishV2(idToUse, offerV2Service)

        val doingPublish = testRestTemplate.exchange("/offer-management-api/v3/offers/publish",
                HttpMethod.POST, HttpEntity(IdIO(idToUse), GenerateData.createHeader(newPublisher)), String::class.java)
        Assert.assertEquals(HttpStatus.NO_CONTENT, doingPublish.statusCode)

        val respAfter = testRestTemplate.exchange("/offer-management-api/v3/offers/$idToUse",
                HttpMethod.GET, HttpEntity(null, GenerateData.createHeader()), String::class.java)

        val publishedOffer = Helpers.createJsonDocument(respAfter.body)
        val publishedOfferIO = mapper.convertValue(publishedOffer, OfferIO::class.java)
        Assert.assertNotEquals(publishingOfferIO.publishedAt, publishedOfferIO.publishedAt)
        Assert.assertEquals(publishedOfferIO.publishedBy, newPublisher)
    }

    @Test
    @DisplayName("Re-Publish Edited Offer - Happy Path")
    fun publishOffer_happyPath_EditedOfferPublished() {
        val id = "3cccc4dd"
        val idToUse = "$id-a684-4afc-9864-b9ae771c70dd"
        val newPublisher = "<EMAIL>"

        val resp = testRestTemplate.exchange("/offer-management-api/v3/offers/$idToUse",
                HttpMethod.GET, HttpEntity(null,
                GenerateData.createHeader(newPublisher)),
                String::class.java)
        Assert.assertEquals(HttpStatus.OK, resp.statusCode)

        val publishingOffer = Helpers.createJsonDocument(resp.body)
        val publishingOfferIO = mapper.convertValue(publishingOffer, OfferIO::class.java)

        Assert.assertEquals(OfferStatus.UPDATED, publishingOfferIO.status)
        Assert.assertNotNull(publishingOfferIO.contentfulId)
        Assert.assertNotNull(publishingOfferIO.detailsId)
        Assert.assertNotNull(publishingOfferIO.updatedBy)

        GenerateData.mockPublishV2(idToUse, offerV2Service)

        val doingPublish = testRestTemplate.exchange("/offer-management-api/v3/offers/publish",
                HttpMethod.POST, HttpEntity(IdIO(idToUse), GenerateData.createHeader(newPublisher)), String::class.java)

        Assert.assertEquals(HttpStatus.NO_CONTENT, doingPublish.statusCode)

        val respAfter = testRestTemplate.exchange("/offer-management-api/v3/offers/$idToUse",
                HttpMethod.GET, HttpEntity(null, GenerateData.createHeader()), String::class.java)

        val publishedOffer = Helpers.createJsonDocument(respAfter.body)
        val publishedOfferIO = mapper.convertValue(publishedOffer, OfferIO::class.java)

        Assert.assertNotEquals(publishingOfferIO.publishedAt, publishedOfferIO.publishedAt)
        Assert.assertEquals(publishedOfferIO.publishedBy, newPublisher)
        Assert.assertEquals(OfferStatus.PUBLISHED, publishedOfferIO.status)
    }

//    @Test
//    @DisplayName("Publish Offer - Failures on create - Returns 400")
//    fun publishOffer_V2Failure_OnCreate() {
//        val id = "1aaaa2bb"
//        val idToUse = "$id-a684-4afc-9864-b9ae771c70dd"
//        val newPublisher = "<EMAIL>"
//
//        val resp = testRestTemplate.exchange("/offer-management-api/v3/offers/$idToUse",
//                HttpMethod.GET, HttpEntity(null, GenerateData.createHeader()), String::class.java)
//        Assert.assertEquals(HttpStatus.OK, resp.statusCode)
//
//        val publishingOffer = Helpers.createJsonDocument(resp.body)
//        val publishingOfferIO = mapper.convertValue(publishingOffer, OfferIO::class.java)
//
//        Assert.assertEquals(OfferStatus.DRAFT, publishingOfferIO.status)
//        Assert.assertNull(publishingOfferIO.contentfulId)
//        Assert.assertNull(publishingOfferIO.detailsId)
//
//        GenerateData.mockCreateV2Failure(offerV2Service)
//        GenerateData.mockPublishV2(idToUse, offerV2Service)
//
//        val doingPublish = testRestTemplate.exchange("/offer-management-api/v3/offers/publish",
//                HttpMethod.POST, HttpEntity(IdIO(idToUse), GenerateData.createHeader(newPublisher)), String::class.java)
//        Assert.assertEquals(HttpStatus.BAD_REQUEST, doingPublish.statusCode)
//
//        val respAfter = testRestTemplate.exchange("/offer-management-api/v3/offers/$idToUse",
//                HttpMethod.GET, HttpEntity(null, GenerateData.createHeader()), String::class.java)
//        val publishedOffer = Helpers.createJsonDocument(respAfter.body)
//        val publishedOfferIO = mapper.convertValue(publishedOffer, OfferIO::class.java)
//
//        Assert.assertNull(publishedOfferIO.contentfulId)
//        Assert.assertNull(publishedOfferIO.detailsId)
//
//        Assert.assertNull(publishedOfferIO.publishedAt)
//        Assert.assertNull(publishedOfferIO.publishedBy)
//        Assert.assertNull(publishedOfferIO.publishedBy)
//    }

//    @Test
//    @DisplayName("Publish Offer - Failures on republish - Returns 400")
//    fun publishOffer_V2Failure_OnRepublish() {
//        val id = "2bbbb3cc"
//        val idToUse = "$id-a684-4afc-9864-b9ae771c70dd"
//        val newPublisher = "<EMAIL>"
//
//        val resp = testRestTemplate.exchange("/offer-management-api/v3/offers/$idToUse",
//                HttpMethod.GET, HttpEntity(null, GenerateData.createHeader()), String::class.java)
//        Assert.assertEquals(HttpStatus.OK, resp.statusCode)
//
//        val publishingOffer = Helpers.createJsonDocument(resp.body)
//        val publishingOfferIO = mapper.convertValue(publishingOffer, OfferIO::class.java)
//        val publishingOfferV2 = offerTransformService.transformOfferV2(publishingOfferIO)
//
//        Assert.assertEquals(OfferStatus.PUBLISHED, publishingOfferIO.status)
//        Assert.assertNotNull(publishingOfferIO.contentfulId)
//        Assert.assertNotNull(publishingOfferIO.detailsId)
//
//        GenerateData.mockCreateV2(publishingOfferV2, offerV2Service)
//        GenerateData.mockPublishV2Failure(offerV2Service)
//
//        val doingPublish = testRestTemplate.exchange("/offer-management-api/v3/offers/publish",
//                HttpMethod.POST, HttpEntity(IdIO(idToUse), GenerateData.createHeader()), String::class.java)
//
//        Assert.assertEquals(HttpStatus.BAD_REQUEST, doingPublish.statusCode)
//
//        val respAfter = testRestTemplate.exchange("/offer-management-api/v3/offers/$idToUse",
//                HttpMethod.GET, HttpEntity(null, GenerateData.createHeader()), String::class.java)
//
//        val publishedOffer = Helpers.createJsonDocument(respAfter.body)
//        val publishedOfferIO = mapper.convertValue(publishedOffer, OfferIO::class.java)
//
//        Assert.assertNotNull(publishedOfferIO.contentfulId)
//        Assert.assertNotNull(publishedOfferIO.detailsId)
//
//        Assert.assertNotEquals(publishedOfferIO.publishedBy, newPublisher)
//        Assert.assertEquals(publishedOfferIO.publishedBy, publishingOfferIO.publishedBy)
//        Assert.assertEquals(publishedOfferIO.publishedAt, publishingOfferIO.publishedAt)
//        Assert.assertEquals(publishedOfferIO.contentfulId, publishingOfferIO.contentfulId)
//    }

//    @Test
//    @DisplayName("Publish Offer - Failures on updateV2 - Returns 400")
//    fun publishOffer_V2Failure_OnEdit() {
//        val id = "3cccc4dd"
//        val idToUse = "$id-a684-4afc-9864-b9ae771c70dd"
//        val newPublisher = "<EMAIL>"
//
//        val resp = testRestTemplate.exchange("/offer-management-api/v3/offers/$idToUse",
//                HttpMethod.GET, HttpEntity(null, GenerateData.createHeader()), String::class.java)
//        Assert.assertEquals(HttpStatus.OK, resp.statusCode)
//
//        val publishingOffer = Helpers.createJsonDocument(resp.body)
//        val publishingOfferIO = mapper.convertValue(publishingOffer, OfferIO::class.java)
//        val publishingOfferV2 = offerTransformService.transformOfferV2(publishingOfferIO)
//
//        Assert.assertEquals(OfferStatus.UPDATED, publishingOfferIO.status)
//        Assert.assertNotNull(publishingOfferIO.contentfulId)
//        Assert.assertNotNull(publishingOfferIO.detailsId)
//        Assert.assertNotNull(publishingOfferIO.updatedBy)
//        Assert.assertNotNull(publishingOfferIO.updatedAt)
//
//        GenerateData.mockUpdate(publishingOfferV2, offerV2Service)
//        GenerateData.mockPublishV2(idToUse, offerV2Service)
//
//        val doingPublish = testRestTemplate.exchange("/offer-management-api/v3/offers/publish",
//                HttpMethod.POST, HttpEntity(IdIO(idToUse), GenerateData.createHeader()), String::class.java)
//        Assert.assertEquals(HttpStatus.BAD_REQUEST, doingPublish.statusCode)
//
//        val respAfter = testRestTemplate.exchange("/offer-management-api/v3/offers/$idToUse",
//                HttpMethod.GET, HttpEntity(null, GenerateData.createHeader()), String::class.java)
//        val publishedOffer = Helpers.createJsonDocument(respAfter.body)
//        val publishedOfferIO = mapper.convertValue(publishedOffer, OfferIO::class.java)
//
//        Assert.assertNotEquals(publishedOfferIO.publishedBy, newPublisher)
//        Assert.assertEquals(publishedOfferIO.publishedAt, publishingOfferIO.publishedAt)
//        Assert.assertEquals(publishedOfferIO.updatedAt, publishingOfferIO.updatedAt)
//        Assert.assertEquals(publishedOfferIO.contentfulId, publishingOfferIO.contentfulId)
//    }
}
