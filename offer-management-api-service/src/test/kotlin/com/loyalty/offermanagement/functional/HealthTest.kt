package com.loyalty.offermanagement.functional

import com.jayway.jsonpath.JsonPath
import com.loyalty.offermanagement.functional.common.Helpers
import org.junit.Assert
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Test

class HealthTest : OfferManagementFunctionalTest() {
    @Test
    @DisplayName("Health call")
    fun health() {
        val resp = testRestTemplate.getForObject<String>("/offer-management-api/health", String::class.java)

        val document = Helpers.createJsonDocument(resp)

        val status = JsonPath.read<String>(document, "$.status")
        Assert.assertEquals("OK", status)
    }
}
