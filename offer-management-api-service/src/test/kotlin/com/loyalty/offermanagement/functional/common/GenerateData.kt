@file:Suppress(
        "TooManyFunctions"
)

package com.loyalty.offermanagement.functional.common

import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.loyalty.offermanagement.functional.v3.PublishTest
import com.loyalty.offermanagement.models.ContentGenerationResponse
import com.loyalty.offermanagement.models.LocalizedString
import com.loyalty.offermanagement.models.offerv2.OfferCreateResponseV2
import com.loyalty.offermanagement.models.offerv2.OfferV2
import com.loyalty.offermanagement.models.partner.PartnerResponse
import com.loyalty.offermanagement.models.v3.inout.BulkPutOfferIO
import com.loyalty.offermanagement.models.v3.inout.OfferIO
import com.loyalty.offermanagement.models.v3.inout.PostOfferIO
import com.loyalty.offermanagement.models.v3.inout.PutOfferIO
import com.loyalty.offermanagement.services.ContentGenerationService
import com.loyalty.offermanagement.services.facets.OfferCategoryService
import com.loyalty.offermanagement.services.facets.PromotionService
import com.loyalty.offermanagement.services.offerv2.OfferV2Service
import com.loyalty.offermanagement.services.partner.PartnerService
import org.mockito.Mockito
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.web.client.HttpClientErrorException
import java.io.IOException

class GenerateData private constructor() {
    companion object {
        private val locale = LocalizedString(
                enUS = "English",
                frCA = "French"
        )

        private val mapper = jacksonObjectMapper()
        private val createResV2 = mapper.readValue(
                PublishTest::class.java.getResourceAsStream("/test-data/offerV2_create_response.json"),
                OfferCreateResponseV2::class.java
        )

        private fun mockResponse(numLocale: Int, locale: LocalizedString = GenerateData.locale):
                ResponseEntity<ContentGenerationResponse> {
            val localList = MutableList(numLocale) { locale }

            val cgsResponse = ContentGenerationResponse(localList, localList, locale, locale, locale)
            return ResponseEntity(cgsResponse, HttpStatus.OK)
        }

        fun mockForCGS(cgs: ContentGenerationService, numLocale: Int,
                       locale: LocalizedString = GenerateData.locale) {
            Mockito.`when`(cgs.generate(Helpers.anyMatcher(OfferIO::class.java)))
                    .thenReturn(GenerateData.mockResponse(numLocale, locale))
        }

        fun mockForCGS(offer: OfferIO, cgs: ContentGenerationService,
                       locale: LocalizedString = GenerateData.locale) {
            mockForCGS(cgs, offer.tiers.size, locale)
        }

        fun mockForCGS(offer: PostOfferIO, cgs: ContentGenerationService,
                       locale: LocalizedString = GenerateData.locale) {
            mockForCGS(cgs, offer.tiers.size, locale)
        }

        fun mockForCGS(offer: PutOfferIO, cgs: ContentGenerationService,
                       locale: LocalizedString = GenerateData.locale) {
            mockForCGS(cgs, offer.tiers.size, locale)
        }

        fun mockForCGS(bulkJob: BulkPutOfferIO, cgs: ContentGenerationService,
                       locale: LocalizedString = GenerateData.locale) {
            for (offer in bulkJob.offers) {
                mockForCGS(cgs, offer.tiers.size, locale)
            }
        }

        fun createHeader(user: String = "<EMAIL>"): HttpHeaders = HttpHeaders().also { headers ->
            headers.add("x-user-email", user)
        }

        fun mockCreateV2(offerV2: OfferV2, offerV2Service: OfferV2Service) {
            Mockito.`when`(offerV2Service.create(offerV2))
                    .thenReturn(ResponseEntity(createResV2, HttpStatus.OK))
        }

        fun mockPublishV2(offerId: String, offerV2Service: OfferV2Service) {
            Mockito.`when`(offerV2Service.publish(offerId))
                    .thenReturn(ResponseEntity(Any(), HttpStatus.OK))
        }

        fun mockCategoryInfo(categoryId: String, offerCategoryServicevice: OfferCategoryService) {
            Mockito.`when`(offerCategoryServicevice.getCategoryInfo(categoryId))
                    .thenReturn(LocalizedString("Mocked Category Label in English", "Mocked Category Label in French"))
        }

        fun mockPromotionInfo(promotionId: String, promotionService: PromotionService) {
            Mockito.`when`(promotionService.getPromotionInfo(promotionId))
                    .thenReturn(LocalizedString("Mocked Promo Label in English", "Mocked Promo Label in French"))
        }

        fun mockUpdate(offerV2: OfferV2, offerV2Service: OfferV2Service) {
            Mockito.`when`(offerV2Service.update(offerV2))
                    .thenThrow(HttpClientErrorException(HttpStatus.SERVICE_UNAVAILABLE))
        }

        fun mockForPartnerService(ps: PartnerService) {
            Mockito.`when`(ps.getPartnerById(Helpers.anyMatcher(String::class.java)))
                .thenReturn(mockPartnerResponse())
        }

        private fun mockPartnerResponse(): PartnerResponse {
            val partner = mapper.readValue(
                GenerateData::class.java.getResourceAsStream(
                "/test-data/get_partner_data.json"), PartnerResponse::class.java)

            return partner
        }

        @Throws(IOException::class)
        fun mockCreateV2Failure(offerV2Service: OfferV2Service) {
            Mockito.`when`(offerV2Service.create(Helpers.anyMatcher(OfferV2::class.java)))
                    .thenThrow(HttpClientErrorException(HttpStatus.SERVICE_UNAVAILABLE))
        }

        fun mockPublishV2Failure(offerV2Service: OfferV2Service) {
            Mockito.`when`(offerV2Service.publish(Helpers.anyMatcher(String::class.java)))
                    .thenThrow(HttpClientErrorException(HttpStatus.SERVICE_UNAVAILABLE))
        }
    }
}
