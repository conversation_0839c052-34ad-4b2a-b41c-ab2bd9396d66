package com.loyalty.offermanagement.functional

import com.loyalty.offermanagement.services.ContentGenerationService
import com.loyalty.offermanagement.services.facets.OfferCategoryService
import com.loyalty.offermanagement.services.facets.PromotionService
import com.loyalty.offermanagement.services.offerv2.OfferV2Service
import com.loyalty.offermanagement.services.partner.PartnerService
import org.junit.jupiter.api.Tag
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.api.extension.ExtendWith
import org.junit.runner.RunWith
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.boot.test.context.SpringBootTest.WebEnvironment
import org.springframework.boot.test.mock.mockito.MockBean
import org.springframework.boot.test.web.client.TestRestTemplate
import org.springframework.context.annotation.ComponentScan
import org.springframework.test.context.ActiveProfiles
import org.springframework.test.context.junit.jupiter.SpringExtension
import org.springframework.test.context.junit4.SpringRunner

@RunWith(SpringRunner::class)
@ExtendWith(SpringExtension::class)
@Tag("unit")
@SpringBootTest(webEnvironment = WebEnvironment.RANDOM_PORT)
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
@ActiveProfiles("unit")
@ComponentScan(basePackages = ["com.loyalty.offermanagement.functional"])
abstract class OfferManagementFunctionalTest {
    @MockBean
    lateinit var cgs: ContentGenerationService

    @MockBean
    lateinit var offerV2Service: OfferV2Service

    @MockBean
    lateinit var offerCategoryServicevice: OfferCategoryService

    @MockBean
    lateinit var promotionService: PromotionService

    @Autowired
    protected lateinit var testRestTemplate: TestRestTemplate

    @MockBean
    lateinit var partnerService: PartnerService
}
