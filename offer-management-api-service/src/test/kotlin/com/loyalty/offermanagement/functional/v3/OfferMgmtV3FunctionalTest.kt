@file:Suppress(
        "<PERSON><PERSON><PERSON><PERSON>",
        "TooManyFunctions",
        "MagicN<PERSON><PERSON>",
        "LargeClass",
        "FunctionNaming",
        "UnsafeCast",
        "ComplexMethod",
        "UnsafeCast"
)

package com.loyalty.offermanagement.functional.v3

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import com.jayway.jsonpath.JsonPath
import com.loyalty.nova.common.util.plusDays
import com.loyalty.offermanagement.functional.OfferManagementFunctionalTest
import com.loyalty.offermanagement.functional.common.GenerateData
import com.loyalty.offermanagement.functional.common.Helpers
import com.loyalty.offermanagement.functional.common.RDSLocalFixture
import com.loyalty.offermanagement.models.LocalizedString
import com.loyalty.offermanagement.models.v3.ExternalSystem
import com.loyalty.offermanagement.models.v3.OfferStatus
import com.loyalty.offermanagement.models.v3.inout.BulkPostOfferIO
import com.loyalty.offermanagement.models.v3.inout.IntegrationIO
import com.loyalty.offermanagement.models.v3.inout.PostOfferIO
import com.loyalty.offermanagement.models.v3.inout.PutOfferIO
import com.loyalty.offermanagement.models.v3.inout.BulkPutOfferIO
import com.loyalty.offermanagement.utils.BULK_OFFER_IO_OFFERS_MAX_SIZE
import com.loyalty.offermanagement.utils.BULK_OFFER_IO_OFFERS_MIN_SIZE
import com.loyalty.offermanagement.utils.BULK_OFFER_IO_OFFERS_SIZE_ERROR_MESSAGE
import com.loyalty.offermanagement.utils.UNAUTHORIZED_ERROR_MESSAGE
import net.minidev.json.JSONArray
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Test
import org.junit.runner.RunWith
import org.mockito.junit.MockitoJUnitRunner
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.HttpEntity
import org.springframework.http.HttpMethod
import org.springframework.http.HttpStatus
import java.time.Instant
import java.time.temporal.ChronoUnit
import java.util.UUID

@RunWith(MockitoJUnitRunner::class)
class OfferMgmtV3FunctionalTest : OfferManagementFunctionalTest() {
    @Autowired
    private lateinit var rdsLocalFixture: RDSLocalFixture

    @Autowired
    private lateinit var mapper: ObjectMapper

    @BeforeEach
    fun beforeEach() {
        rdsLocalFixture.dropBatchJobs()
        rdsLocalFixture.dropOffers()
        rdsLocalFixture.storeOffers(
                "/test-data/offerRDS/spendAndGet.json",
                "/test-data/offerRDS/spendAndGetHighValues.json",
                "/test-data/offerRDS/spendAndGetNonTier.json"
        )
        rdsLocalFixture.storeBatchOffers(
                "/test-data/bulk/data/spendAndGet.json",
                "/test-data/bulk/data/spendAndGetHighValues.json",
                "/test-data/bulk/data/spendAndGetNonTier.json"
        )
    }

    @Test
    @DisplayName("Get Offer - Happy Path - Returns Offer")
    fun getOffer_happyPath_returnsOffer() {
        val idToUse = "45262c25-457e-46f9-9744-188fb6118ed0"
        val partnerIdToExpect = "3db0b3e0-4134-453e-8381-08e90f2b5141"

        val resp = testRestTemplate.exchange("/offer-management-api/v3/offers/$idToUse",
                HttpMethod.GET, HttpEntity(null, GenerateData.createHeader()), String::class.java)

        val document = Helpers.createJsonDocument(resp.body)

        Assertions.assertEquals(HttpStatus.OK, resp.statusCode)

        val id = JsonPath.read<String>(document, "$.id")
        Assertions.assertEquals(idToUse, id)

        val partnerId = JsonPath.read<String>(document, "$.partnerId")
        Assertions.assertEquals(partnerIdToExpect, partnerId)
    }

    @Test
    @DisplayName("Get Offer Counts - Happy Path - Returns Offer Counts")
    fun getOfferCounts_happyPath_returnsOfferCounts() {
        val resp = testRestTemplate.exchange("/offer-management-api/v3/offers/counts",
                HttpMethod.GET, HttpEntity(null, GenerateData.createHeader()), String::class.java)

        Assertions.assertEquals(HttpStatus.OK, resp.statusCode)
    }

    @Test
    @DisplayName("Get Offer - Non-existent Id - Returns HttpStatus.NOT_FOUND")
    fun getOffer_nonExistentId_404() {
        val idToUse = UUID.randomUUID()

        val resp = testRestTemplate.exchange("/offer-management-api/v3/offers/$idToUse",
                HttpMethod.GET, HttpEntity(null, GenerateData.createHeader()), String::class.java)

        val document = Helpers.createJsonDocument(resp.body)

        Assertions.assertEquals(HttpStatus.NOT_FOUND, resp.statusCode)

        val message = JsonPath.read<String>(document, "$.message")
        Assertions.assertEquals(idToUse.toString(), message)

        val status = JsonPath.read<Int>(document, "$.status")
        Assertions.assertEquals(404, status)
    }

    @Test
    @DisplayName("Post Offer - Happy Path - Offer created")
    fun postOffers_happyPath_offerCreated() {
        val postOffers = mapper.readValue(OfferMgmtV3FunctionalTest::class.java.getResourceAsStream(
                "/test-data/post_offer_data.json"), PostOfferIO::class.java)
        postOffers.shiftOfferDates()
        GenerateData.mockForCGS(postOffers, cgs)

        val resp = testRestTemplate.exchange("/offer-management-api/v3/offers",
                HttpMethod.POST, HttpEntity(postOffers, GenerateData.createHeader()), String::class.java)

        val document = Helpers.createJsonDocument(resp.body)

        Assertions.assertEquals(HttpStatus.OK, resp.statusCode)

        val id = JsonPath.read<String>(document, "$.id")
        Assertions.assertTrue(Helpers.isValidUUID(id))

        val integrations = JsonPath
                .parse(document)
                .let { documentContext -> documentContext.read("$.integrations") as JSONArray }
                .let { json -> this.mapper.readValue<List<IntegrationIO>>(json.toString()) }
        Assertions.assertTrue(integrations.any { x -> x.systemName == ExternalSystem.Agility })

        val status = JsonPath.read<String>(document, "$.status")
        Assertions.assertEquals(OfferStatus.DRAFT, OfferStatus.valueOf(status))
    }

    @Test
    @DisplayName("Post Offer - CampaignCode")
    fun postOffers_campaignCode() {
        val postOffers = mapper.readValue(OfferMgmtV3FunctionalTest::class.java.getResourceAsStream(
                "/test-data/post_offer_campaign_code.json"), PostOfferIO::class.java)
        postOffers.shiftOfferDates()
        GenerateData.mockForCGS(postOffers, cgs)

        val resp = testRestTemplate.exchange("/offer-management-api/v3/offers",
                HttpMethod.POST, HttpEntity(postOffers, GenerateData.createHeader()), String::class.java)

        val document = Helpers.createJsonDocument(resp.body)

        Assertions.assertEquals(HttpStatus.OK, resp.statusCode)
        val campaignCode = JsonPath.read<String>(document, "$.campaignCode")
        Assertions.assertEquals(campaignCode, "23451")
    }

    @Test
    @DisplayName("Post Offer - Happy Path - Offer created - Multi SKU")
    fun postOffers_happyPath_offerCreated_MultiSKU() {
        val postOffers = mapper.readValue(OfferMgmtV3FunctionalTest::class.java.getResourceAsStream(
            "/test-data/post_offer_data_multi_sku.json"), PostOfferIO::class.java)
        postOffers.shiftOfferDates()
        GenerateData.mockForCGS(postOffers, cgs)

        val resp = testRestTemplate.exchange("/offer-management-api/v3/offers",
            HttpMethod.POST, HttpEntity(postOffers, GenerateData.createHeader()), String::class.java)

        val document = Helpers.createJsonDocument(resp.body)

        Assertions.assertEquals(HttpStatus.OK, resp.statusCode)
        val sku0 = JsonPath.read<String>(document, "$.tiers[0].content[0].skus[0]")
        Assertions.assertEquals(sku0, "a1234")
    }

    @Test
    @DisplayName("Post Offer - Happy Path - Offer created with French Fallback")
    fun postOffer_happyPath_offerCreatedWithFrenchFallback() {
        val postOffers = mapper.readValue(OfferMgmtV3FunctionalTest::class.java.getResourceAsStream(
                "/test-data/post_offer_data.json"), PostOfferIO::class.java)
        postOffers.shiftOfferDates()
        GenerateData.mockForCGS(postOffers, cgs, LocalizedString(
                enUS = "English"
        ))

        val resp = testRestTemplate.exchange("/offer-management-api/v3/offers",
                HttpMethod.POST, HttpEntity(postOffers, GenerateData.createHeader()), String::class.java)

        val document = Helpers.createJsonDocument(resp.body)

        Assertions.assertEquals(HttpStatus.OK, resp.statusCode)

        val id = JsonPath.read<String>(document, "$.id")
        Assertions.assertTrue(Helpers.isValidUUID(id))

        val active = JsonPath.read<Boolean>(document, "$.active")
        Assertions.assertTrue(active)

        val imageEnUS = JsonPath.read<String>(document, "$.image.en-US.path")
        val imageFrCA = JsonPath.read<String>(document, "$.image.en-US.path")
        Assertions.assertEquals(imageEnUS, imageFrCA)

        val status = JsonPath.read<String>(document, "$.status")
        Assertions.assertEquals(OfferStatus.DRAFT, OfferStatus.valueOf(status))
    }

    @Test
    @DisplayName("Post Offer - Happy Path - CouponCode Offer created without mechanismValue FR")
    fun postOffer_happyPath_couponCodeOfferCreatedWithoutMechanismValueFR() {
        val postOffers = mapper.readValue(OfferMgmtV3FunctionalTest::class.java.getResourceAsStream(
            "/test-data/post_offer_data_couponCode.json"), PostOfferIO::class.java)
        postOffers.shiftOfferDates()
        GenerateData.mockForCGS(postOffers, cgs, LocalizedString(
            enUS = "English"
        ))

        val resp = testRestTemplate.exchange("/offer-management-api/v3/offers",
            HttpMethod.POST, HttpEntity(postOffers, GenerateData.createHeader()), String::class.java)

        val document = Helpers.createJsonDocument(resp.body)

        Assertions.assertEquals(HttpStatus.OK, resp.statusCode)

        val id = JsonPath.read<String>(document, "$.id")
        Assertions.assertTrue(Helpers.isValidUUID(id))

        val active = JsonPath.read<Boolean>(document, "$.active")
        Assertions.assertTrue(active)

        val mechanismValueEN = JsonPath.read<String>(document, "$.mechanisms[0].mechanismValue.en-US")
        val mechanismValueFR = JsonPath.read<String>(document, "$.mechanisms[0].mechanismValue.fr-CA")
        Assertions.assertEquals(mechanismValueEN, mechanismValueFR)
    }

    @Test
    @DisplayName("Post Offer - Bad Request uniqueness validation")
    fun postOffer_unhappyPath_offer_uniqueness_throwsBadRequest() {

        val postOffers = mapper.readValue(OfferMgmtV3FunctionalTest::class.java.getResourceAsStream(
            "/test-data/post_offer_data_uniqueness.json"), PostOfferIO::class.java)
        postOffers.shiftOfferDates()
        GenerateData.mockForCGS(postOffers, cgs)
        GenerateData.mockForPartnerService(partnerService)

        val resp = testRestTemplate.exchange("/offer-management-api/v3/offers",
            HttpMethod.POST, HttpEntity(postOffers, GenerateData.createHeader()), String::class.java)

        Assertions.assertEquals(HttpStatus.BAD_REQUEST, resp.statusCode)
    }

    @Test
    @DisplayName("Post Offer - Unhappy Path - Dates not valid")
    fun postOffer_unhappyPath_datesNotValid_throwsBadRequest() {
        val postOffers = mapper.readValue(OfferMgmtV3FunctionalTest::class.java.getResourceAsStream(
            "/test-data/post_offer_date_validation_failure.json"), PostOfferIO::class.java)
        GenerateData.mockForCGS(postOffers, cgs, LocalizedString(
            enUS = "English"
        ))

        val resp = testRestTemplate.exchange("/offer-management-api/v3/offers",
            HttpMethod.POST, HttpEntity(postOffers, GenerateData.createHeader()), String::class.java)

        Assertions.assertEquals(HttpStatus.BAD_REQUEST, resp.statusCode)
    }

    @Test
    @DisplayName("Post Offers - Missing Headers - Bad Request")
    fun postOffers_unauthorized_request() {
        val postOffers = mapper.readValue(OfferMgmtV3FunctionalTest::class.java.getResourceAsStream(
                "/test-data/post_offer_data.json"), PostOfferIO::class.java)
        postOffers.shiftOfferDates()
        val resp = testRestTemplate.exchange("/offer-management-api/v3/offers",
                HttpMethod.POST, HttpEntity(postOffers), String::class.java)

        Assertions.assertEquals(HttpStatus.BAD_REQUEST, resp.statusCode)
    }

    @Test
    @DisplayName("Post Offer - Happy Path - Offer created with Default Image")
    fun postOffer_happyPath_offerCreatedWithDefaultImage() {
        val postOffers = mapper.readValue(OfferMgmtV3FunctionalTest::class.java.getResourceAsStream(
                "/test-data/post_offer_default_image.json"), PostOfferIO::class.java)
        postOffers.shiftOfferDates()
        GenerateData.mockForCGS(postOffers, cgs, LocalizedString(
                enUS = "English"
        ))

        val resp = testRestTemplate.exchange("/offer-management-api/v3/offers",
                HttpMethod.POST, HttpEntity(postOffers, GenerateData.createHeader()), String::class.java)

        val document = Helpers.createJsonDocument(resp.body)

        Assertions.assertEquals(HttpStatus.OK, resp.statusCode)

        val id = JsonPath.read<String>(document, "$.id")
        Assertions.assertTrue(Helpers.isValidUUID(id))

        // get offer that was just posted to verify the offer has an image
        val getResp = testRestTemplate.exchange("/offer-management-api/v3/offers/$id",
                HttpMethod.GET, HttpEntity(null, GenerateData.createHeader()), String::class.java)
        val getDocument = Helpers.createJsonDocument(getResp.body)

        Assertions.assertEquals(HttpStatus.OK, getResp.statusCode)
        // If the offer is created there will be an image and image path that is not null
        val imageEN = JsonPath.read<String>(getDocument, "$.image.en-US.path")

        Assertions.assertNotNull(imageEN)
    }

    @Test
    @DisplayName("Post Offers - Schema Validation Failure - Bad Request No Default Image for Offer")
    fun postOffers_schemaValidationFailure_badRequestNoDefaultImage() {
        val postOffers = mapper.readValue(OfferMgmtV3FunctionalTest::class.java.getResourceAsStream(
                "/test-data/post_offer_default_image_invalid.json"), PostOfferIO::class.java)
        postOffers.shiftOfferDates()
        val resp = testRestTemplate.exchange("/offer-management-api/v3/offers",
                HttpMethod.POST, HttpEntity(postOffers, GenerateData.createHeader()), String::class.java)

        Assertions.assertEquals(HttpStatus.BAD_REQUEST, resp.statusCode)
    }

    @Test
    @DisplayName("Post Offers - Schema Validation Failure - Bad Request")
    fun postOffers_schemaValidationFailure_badRequest() {
        val postOffers = mapper.readValue(OfferMgmtV3FunctionalTest::class.java.getResourceAsStream(
                "/test-data/post_offer_data_invalid.json"), PostOfferIO::class.java)
        postOffers.shiftOfferDates()
        val resp = testRestTemplate.exchange("/offer-management-api/v3/offers",
                HttpMethod.POST, HttpEntity(postOffers, GenerateData.createHeader()), String::class.java)

        Assertions.assertEquals(HttpStatus.BAD_REQUEST, resp.statusCode)
    }

    @Test
    @DisplayName("Bulk Post Offers - Happy Path - 3 Offers created")
    fun bulkPostOffers_happyPath_offersCreated() {
        val postOffer = mapper.readValue(OfferMgmtV3FunctionalTest::class.java.getResourceAsStream(
                "/test-data/post_offer_data.json"), PostOfferIO::class.java)
        postOffer.shiftOfferDates()
        // generate mock
        GenerateData.mockForCGS(postOffer, cgs)

        val bulkRequest = BulkPostOfferIO("TestBatch", listOf(postOffer, postOffer, postOffer))
        val resp = testRestTemplate.exchange("/offer-management-api/v3/offers/bulk",
                HttpMethod.POST, HttpEntity(bulkRequest, GenerateData.createHeader()), String::class.java)

        val document = Helpers.createJsonDocument(resp.body)

        Assertions.assertEquals(HttpStatus.OK, resp.statusCode)

        val id = JsonPath.read<String>(document, "$.id")
        Assertions.assertTrue(Helpers.isValidUUID(id))
    }

    @Test
    @DisplayName("Bulk Post Offers - Happy Path - Default Images")
    fun bulkPostOffers_happyPath_defaultImages() {
        val postOffer = mapper.readValue(OfferMgmtV3FunctionalTest::class.java.getResourceAsStream(
                "/test-data/post_offer_default_image.json"), PostOfferIO::class.java)
        postOffer.shiftOfferDates()
        // generate mock
        GenerateData.mockForCGS(postOffer, cgs)

        val bulkRequest = BulkPostOfferIO("DefaultImageBulkJob", listOf(postOffer, postOffer, postOffer))
        val resp = testRestTemplate.exchange("/offer-management-api/v3/offers/bulk",
                HttpMethod.POST, HttpEntity(bulkRequest, GenerateData.createHeader()), String::class.java)

        val document = Helpers.createJsonDocument(resp.body)

        Assertions.assertEquals(HttpStatus.OK, resp.statusCode)

        val id = JsonPath.read<String>(document, "$.id")
        Assertions.assertTrue(Helpers.isValidUUID(id))

        // get offers that were just stored in the repo
        val getResp = testRestTemplate.exchange("/offer-management-api/v3/offers?bulkId=${id}",
                HttpMethod.GET, HttpEntity(null, GenerateData.createHeader()), String::class.java)
        Assertions.assertEquals(HttpStatus.OK, getResp.statusCode)

        val getDocument = Helpers.createJsonDocument(getResp.body)

        val content = JsonPath.read<JSONArray>(getDocument, "$.content")
        // Iterate over the JSONArray to ensure each image from the created batchJob contains an image
        for (i in 0 until content.size) {
            val offer = content[i]
            val image = JsonPath.read<LinkedHashMap<String, String>>(offer, "$.image")
            Assertions.assertNotNull(image)
        }
    }

    @Test
    @DisplayName("Bulk Post Offers - Schema Validation Failure - Bad Request No Default Image for Offer")
    fun bulkPostOffers_validationFailure_defaultImages() {
        val postOffer = mapper.readValue(OfferMgmtV3FunctionalTest::class.java.getResourceAsStream(
                "/test-data/post_offer_default_image_invalid.json"), PostOfferIO::class.java)
        postOffer.shiftOfferDates()
        // generate mock
        GenerateData.mockForCGS(postOffer, cgs)

        val bulkRequest = BulkPostOfferIO("TestBatch", listOf(postOffer, postOffer, postOffer))
        val resp = testRestTemplate.exchange("/offer-management-api/v3/offers/bulk",
                HttpMethod.POST, HttpEntity(bulkRequest, GenerateData.createHeader()), String::class.java)

        Assertions.assertEquals(HttpStatus.BAD_REQUEST, resp.statusCode)
    }

    @Test
    @DisplayName("Bulk Post Offers - Schema Validation in Offer - Bad Request")
    fun bulkPostOffers_schemaValidation_failure() {
        val postOffer = mapper.readValue(OfferMgmtV3FunctionalTest::class.java.getResourceAsStream(
                "/test-data/post_offer_data_invalid.json"), PostOfferIO::class.java)
        postOffer.shiftOfferDates()
        val bulkRequest = BulkPostOfferIO("TestBatch", offers = listOf(postOffer, postOffer, postOffer))
        val resp = testRestTemplate.exchange("/offer-management-api/v3/offers/bulk",
                HttpMethod.POST, HttpEntity(bulkRequest, GenerateData.createHeader()), String::class.java)

        Assertions.assertEquals(HttpStatus.BAD_REQUEST, resp.statusCode)
    }

    @Test
    @DisplayName("Bulk Post Offers - Bad Request uniqueness validation")
    fun bulkPostOffers_unhappyPath_offer_uniqueness_throwsBadRequest() {
        val postOffer = mapper.readValue(OfferMgmtV3FunctionalTest::class.java.getResourceAsStream(
            "/test-data/post_offer_data_uniqueness.json"), PostOfferIO::class.java)
        postOffer.shiftOfferDates()
        GenerateData.mockForPartnerService(partnerService)
        val bulkRequest = BulkPostOfferIO("TestBatch", offers = listOf(postOffer, postOffer, postOffer))
        val resp = testRestTemplate.exchange("/offer-management-api/v3/offers/bulk",
            HttpMethod.POST, HttpEntity(bulkRequest, GenerateData.createHeader()), String::class.java)

        Assertions.assertEquals(HttpStatus.BAD_REQUEST, resp.statusCode)
    }

    @Test
    @DisplayName("Put Offers - Change Published Partner - Bad Request")
    fun putOffers_changePublishedPartner_badRequest() {
        val idToUpdate = "45262c25-457e-46f9-9744-188fb6118ed0"

        val putOffer = mapper.readValue(OfferMgmtV3FunctionalTest::class.java.getResourceAsStream(
                "/test-data/put_offer_ace.json"), PutOfferIO::class.java)
        putOffer.shiftOfferDates()
        GenerateData.mockForCGS(putOffer, cgs)

        val resp = testRestTemplate.exchange("/offer-management-api/v3/offers/$idToUpdate",
                HttpMethod.PUT, HttpEntity(putOffer, GenerateData.createHeader()), String::class.java)

        Assertions.assertEquals(HttpStatus.BAD_REQUEST, resp.statusCode)
    }

    @Test
    @DisplayName("Put Offers - Change Published Mass - Bad Request")
    fun putOffers_changePublishedMass_badRequest() {
        val idToUpdate = "45262c25-457e-46f9-9744-188fb6118ed0"

        val putOffer = mapper.readValue(OfferMgmtV3FunctionalTest::class.java.getResourceAsStream(
                "/test-data/put_offer_mass.json"), PutOfferIO::class.java)
        putOffer.shiftOfferDates()
        GenerateData.mockForCGS(putOffer, cgs)

        val resp = testRestTemplate.exchange("/offer-management-api/v3/offers/$idToUpdate",
                HttpMethod.PUT, HttpEntity(putOffer, GenerateData.createHeader()), String::class.java)

        Assertions.assertEquals(HttpStatus.BAD_REQUEST, resp.statusCode)
    }

    @Test
    @DisplayName("Put Offers - Happy Path - Offer updated")
    fun putOffers_happyPath_offerUpdated() {
        val idToUpdate = "45262c25-457e-46f9-9744-188fb6118ed0"

        val putOffer = mapper.readValue(OfferMgmtV3FunctionalTest::class.java.getResourceAsStream(
                "/test-data/put_offer_08b02831-f49a-44f9-b3c3-b985270ed90f.json"), PutOfferIO::class.java)
        putOffer.shiftOfferDates()
        GenerateData.mockForCGS(putOffer, cgs)

        val resp = testRestTemplate.exchange("/offer-management-api/v3/offers/$idToUpdate",
                HttpMethod.PUT, HttpEntity(putOffer, GenerateData.createHeader()), String::class.java)

        val document = Helpers.createJsonDocument(resp.body)

        Assertions.assertEquals(HttpStatus.OK, resp.statusCode)

        val id = JsonPath.read<String>(document, "$.id")
        Assertions.assertEquals(idToUpdate, id)

        val updatedBy = JsonPath.read<String>(document, "$.updatedBy")
        Assertions.assertEquals("<EMAIL>", updatedBy)

        val active = JsonPath.read<Boolean>(document, "$.active")
        Assertions.assertTrue(active)
    }

    @Test
    @DisplayName("Put Offers - Integrations Test")
    fun putOffers_happyPath_integrationsTest() {
        val idToUse = "0595dde5-e204-439a-8a05-c6d4ee087ba5"

        val respGet = testRestTemplate.exchange("/offer-management-api/v3/offers/$idToUse",
                HttpMethod.GET, HttpEntity(null, GenerateData.createHeader()), String::class.java)

        val documentGet = Helpers.createJsonDocument(respGet.body)

        val integrationsInitially = JsonPath
                .parse(documentGet)
                .let { documentContext -> documentContext.read("$.integrations") as JSONArray }
                .let { json -> this.mapper.readValue<List<IntegrationIO>>(json.toString()) }

        // Asserting this offer initially has no integrations
        Assertions.assertTrue(integrationsInitially.isEmpty())

        val putOffer = mapper.readValue(OfferMgmtV3FunctionalTest::class.java.getResourceAsStream(
                "/test-data/put_offer_08b02831-f49a-44f9-b3c3-b985270ed90f.json"), PutOfferIO::class.java)
        putOffer.shiftOfferDates()
        GenerateData.mockForCGS(putOffer, cgs)

        val respFirstPut = testRestTemplate.exchange("/offer-management-api/v3/offers/$idToUse",
                HttpMethod.PUT, HttpEntity(putOffer, GenerateData.createHeader()), String::class.java)

        val documentFirstPut = Helpers.createJsonDocument(respFirstPut.body)
        val integrationsFirstPut = JsonPath
                .parse(documentFirstPut)
                .let { documentContext -> documentContext.read("$.integrations") as JSONArray }
                .let { json -> this.mapper.readValue<List<IntegrationIO>>(json.toString()) }
        // Asserting an integration with agility is automatically created on PUT
        Assertions.assertTrue(integrationsFirstPut.any { x -> x.systemName == ExternalSystem.Agility })

        val agilityIdFirstPut = integrationsFirstPut.find { x -> x.systemName == ExternalSystem.Agility }?.systemCode

        val respSecondPut = testRestTemplate.exchange("/offer-management-api/v3/offers/$idToUse",
                HttpMethod.PUT, HttpEntity(putOffer, GenerateData.createHeader()), String::class.java)

        val documentSecondPut = Helpers.createJsonDocument(respSecondPut.body)

        val integrationsAfterSecondPut = JsonPath
                .parse(documentSecondPut)
                .let { documentContext -> documentContext.read("$.integrations") as JSONArray }
                .let { json -> this.mapper.readValue<List<IntegrationIO>>(json.toString()) }

        // Asserting same integration with agility remains in second PUT
        Assertions.assertTrue(integrationsAfterSecondPut.any { x -> x.systemName == ExternalSystem.Agility })

        val agilityIdSecondPut = integrationsAfterSecondPut.find { x -> x.systemName == ExternalSystem.Agility }?.systemCode

        // Asserting agility ID is same after two PUTs
        Assertions.assertEquals(agilityIdFirstPut, agilityIdSecondPut)
    }

    @Test
    @DisplayName("Put Offers - Happy Path - Offer updated french fallback")
    fun putOffers_happyPath_offerUpdatedFrenchFallback() {
        val idToUpdate = "45262c25-457e-46f9-9744-188fb6118ed0"

        val putOffer = mapper.readValue(OfferMgmtV3FunctionalTest::class.java.getResourceAsStream(
                "/test-data/put_offer_08b02831-f49a-44f9-b3c3-b985270ed90f.json"), PutOfferIO::class.java)
        putOffer.shiftOfferDates()
        GenerateData.mockForCGS(putOffer, cgs)

        val resp = testRestTemplate.exchange("/offer-management-api/v3/offers/$idToUpdate",
                HttpMethod.PUT, HttpEntity(putOffer, GenerateData.createHeader()), String::class.java)

        val document = Helpers.createJsonDocument(resp.body)

        Assertions.assertEquals(HttpStatus.OK, resp.statusCode)

        val id = JsonPath.read<String>(document, "$.id")
        Assertions.assertEquals(idToUpdate, id)

        val imageEnUS = JsonPath.read<String>(document, "$.image.en-US.path")
        val imageFrCA = JsonPath.read<String>(document, "$.image.en-US.path")
        Assertions.assertEquals(imageEnUS, imageFrCA)

        val updatedBy = JsonPath.read<String>(document, "$.updatedBy")
        Assertions.assertEquals("<EMAIL>", updatedBy)
    }

    @Test
    @DisplayName("Put Offers - Updating Offer mechanismType from CouponCode to barcode and verifying the mechanismValues")
    fun putOffers_happyPath_couponCodeOfferUpdateWithDifferentMechanismValue() {
        val idToUpdate = "45262c25-457e-46f9-9744-188fb6118ed0"

        val putOffer = mapper.readValue(OfferMgmtV3FunctionalTest::class.java.getResourceAsStream(
                "/test-data/put_offer_464d5ccc-46a8-4cae-a30e-f27afa127e97.json"), PutOfferIO::class.java)
        putOffer.shiftOfferDates()
        GenerateData.mockForCGS(putOffer, cgs)

        val resp = testRestTemplate.exchange("/offer-management-api/v3/offers/$idToUpdate",
                HttpMethod.PUT, HttpEntity(putOffer, GenerateData.createHeader()), String::class.java)

        val document = Helpers.createJsonDocument(resp.body)

        Assertions.assertEquals(HttpStatus.OK, resp.statusCode)

        val id = JsonPath.read<String>(document, "$.id")
        Assertions.assertEquals(idToUpdate, id)

        val mechanismValueEN = JsonPath.read<String>(document, "$.mechanisms[0].mechanismValue.en-US")
        val mechanismValueFR = JsonPath.read<String>(document, "$.mechanisms[0].mechanismValue.fr-CA")
        Assertions.assertEquals(mechanismValueEN, mechanismValueFR)
    }

    @Test
    @DisplayName("Put Offers - Schema Validation Failure - Bad Request")
    fun putOffers_schemaValidationFailure_badRequest() {
        val putOffer = mapper.readValue(OfferMgmtV3FunctionalTest::class.java.getResourceAsStream(
                "/test-data/put_offer_data_invalid.json"), PutOfferIO::class.java)
        putOffer.shiftOfferDates()
        val resp = testRestTemplate.exchange("/offer-management-api/v3/offers/a0d00a60-2436-4d39-9a57-62c1d0524d99",
                HttpMethod.PUT, HttpEntity(putOffer, GenerateData.createHeader()), String::class.java)

        Assertions.assertEquals(HttpStatus.BAD_REQUEST, resp.statusCode)
    }

    @Test
    @DisplayName("Put Offers - Bad Request uniqueness validation")
    fun putOffers_unhappyPath_offer_uniqueness_throwsBadRequest() {
        val putOffer = mapper.readValue(OfferMgmtV3FunctionalTest::class.java.getResourceAsStream(
            "/test-data/put_offer_data_uniqueness.json"), PutOfferIO::class.java)
        putOffer.shiftOfferDates()
        GenerateData.mockForPartnerService(partnerService)
        val resp = testRestTemplate.exchange("/offer-management-api/v3/offers/a0d00a60-2436-4d39-9a57-62c1d0524d99",
            HttpMethod.PUT, HttpEntity(putOffer, GenerateData.createHeader()), String::class.java)

        Assertions.assertEquals(HttpStatus.BAD_REQUEST, resp.statusCode)
    }

    @Test
    @DisplayName("Put Offers - Missing Headers - Bad Request")
    fun puttOffers_unauthorized_request() {
        val putOffer = mapper.readValue(OfferMgmtV3FunctionalTest::class.java.getResourceAsStream(
                "/test-data/offer_put_a0d00a60-2436-4d39-9a57-62c1d0524d99.json"), PutOfferIO::class.java)
        putOffer.shiftOfferDates()
        val resp = testRestTemplate.exchange("/offer-management-api/v3/offers/a0d00a60-2436-4d39-9a57-62c1d0524d99",
                HttpMethod.PUT, HttpEntity(putOffer), String::class.java)

        Assertions.assertEquals(HttpStatus.BAD_REQUEST, resp.statusCode)
    }

    @Test
    @DisplayName("Bulk Update Offers - Happy Path - Bulk Job Offer updated")
    fun patchBulkJob_happyPath_bulkJobUpdated() {
        val idToUpdate = "47224121-a83c-4435-9cdc-19906810e1af"
        val offerIdToUpdate = "12342c25-457e-46f9-9744-188fb6118ed0"

        val patchOffersForBulk = mapper.readValue(OfferMgmtV3FunctionalTest::class.java.getResourceAsStream(
                "/test-data/bulk/bulk_put_offer.json"), BulkPutOfferIO::class.java)
        patchOffersForBulk.offers[0].shiftOfferDates()
        GenerateData.mockForCGS(patchOffersForBulk, cgs)

        var resp = testRestTemplate.exchange("/offer-management-api/v3/offers/bulk/$idToUpdate",
                HttpMethod.PATCH, HttpEntity(patchOffersForBulk, GenerateData.createHeader()), String::class.java)

        Assertions.assertEquals(HttpStatus.NO_CONTENT, resp.statusCode)

        val specificBatch = rdsLocalFixture.getBatchJobById(idToUpdate)

        Assertions.assertEquals("<EMAIL>", specificBatch.get().updatedBy)

        // Check if offer is updated correctly
        resp = testRestTemplate.exchange("/offer-management-api/v3/offers/$offerIdToUpdate",
                HttpMethod.GET, HttpEntity(null, GenerateData.createHeader()), String::class.java)

        val document = Helpers.createJsonDocument(resp.body)
        val updatedBy = JsonPath.read<String>(document, "$.updatedBy")
        Assertions.assertEquals("<EMAIL>", updatedBy)
    }

    @Test
    @DisplayName("Bulk Update Offers - Happy Path - Bulk Job Offer updated without updated the active field")
    fun patchBulkJob_happyPath_bulkJobUpdatedDontUpdateActive() {
        val bulkOfferIdToUpdate = "47224121-a83c-4435-9cdc-19906810e1af"

        val patchOffersForBulk = mapper.readValue(OfferMgmtV3FunctionalTest::class.java.getResourceAsStream(
                "/test-data/bulk/bulk_put_offer.json"), BulkPutOfferIO::class.java)
        patchOffersForBulk.offers[0].shiftOfferDates()
        GenerateData.mockForCGS(patchOffersForBulk, cgs)

        // Change the active value in the first offer
        patchOffersForBulk.offers[0].active = false
        val offerIdToUpdate = patchOffersForBulk.offers[0].id

        var resp = testRestTemplate.exchange("/offer-management-api/v3/offers/bulk/$bulkOfferIdToUpdate",
                HttpMethod.PATCH, HttpEntity(patchOffersForBulk, GenerateData.createHeader()), String::class.java)

        Assertions.assertEquals(HttpStatus.NO_CONTENT, resp.statusCode)

        // Check if offer is updated correctly
        resp = testRestTemplate.exchange("/offer-management-api/v3/offers/$offerIdToUpdate",
                HttpMethod.GET, HttpEntity(null, GenerateData.createHeader()), String::class.java)

        val document = Helpers.createJsonDocument(resp.body)
        val active = JsonPath.read<Boolean>(document, "$.active")

        Assertions.assertTrue(active)
    }

    @Test
    @DisplayName("Bulk Update Offers - One Invalid Offer Prevents Any Updates - Bad Request")
    fun patchBulkJob_transactionalUpdate_failure() {
        // For more than one offer in the bulk job, when the first offer is successful but the second offer is invalid we should fail the entire bulk job update

        val idToUpdate = "47224121-a83c-4435-9cdc-19906810e1af"
        val offerIdToUpdate = "5678dde5-e204-439a-8a05-c6d4ee087ba5"
        val originalBatchName = rdsLocalFixture.getBatchJobById(idToUpdate).get().batchName

        val patchOffersForBulk = mapper.readValue(OfferMgmtV3FunctionalTest::class.java.getResourceAsStream(
                "/test-data/bulk/bulk_put_offer_invalid_offer.json"), BulkPutOfferIO::class.java)
        patchOffersForBulk.offers[0].shiftOfferDates()
        val originalResponseBody = testRestTemplate.exchange("/offer-management-api/v3/offers/$offerIdToUpdate",
                HttpMethod.GET, HttpEntity(null, GenerateData.createHeader()), String::class.java).body

        GenerateData.mockForCGS(patchOffersForBulk, cgs)

        val resp = testRestTemplate.exchange("/offer-management-api/v3/offers/bulk/$idToUpdate",
                HttpMethod.PATCH, HttpEntity(patchOffersForBulk, GenerateData.createHeader()), String::class.java)

        Assertions.assertEquals(HttpStatus.BAD_REQUEST, resp.statusCode)

        // To confirm nothing was updated we will get the offer again
        val specificBatch = rdsLocalFixture.getBatchJobById(idToUpdate)

        // Ensure we did not update the bulk job by checking the name
        Assertions.assertEquals(originalBatchName, specificBatch.get().batchName)

        // Ensure we did not update any valid fields in the offer
        val updatedResponseBody = testRestTemplate.exchange("/offer-management-api/v3/offers/$offerIdToUpdate",
                HttpMethod.GET, HttpEntity(null, GenerateData.createHeader()), String::class.java).body

        Assertions.assertEquals(originalResponseBody, updatedResponseBody)
    }

    @Test
    @DisplayName("Bulk Update Offers - Bulk Job Not Found - Bad Request")
    fun patchBulkJob_bulkJobNotFound_failure() {
        val idToUpdate = "17224121-a83c-4435-9cdc-19906810e1af"

        val patchOffersForBulk = mapper.readValue(OfferMgmtV3FunctionalTest::class.java.getResourceAsStream(
                "/test-data/bulk/bulk_put_offer.json"), BulkPutOfferIO::class.java)
        patchOffersForBulk.offers[0].shiftOfferDates()
        val resp = testRestTemplate.exchange("/offer-management-api/v3/offers/bulk/$idToUpdate",
                HttpMethod.PATCH, HttpEntity(patchOffersForBulk, GenerateData.createHeader()), String::class.java)

        Assertions.assertEquals(HttpStatus.NOT_FOUND, resp.statusCode)
    }

    @Test
    @DisplayName("Bulk Update Offers - Bad Request uniqueness validation")
    fun patchBulkJob_unhappyPath_offer_uniqueness_throwsBadRequest() {
        val idToUpdate = "17224121-a83c-4435-9cdc-19906810e1af"

        val patchOffersForBulk = mapper.readValue(OfferMgmtV3FunctionalTest::class.java.getResourceAsStream(
            "/test-data/bulk/bulk_put_offer_data_uniqueness.json"), BulkPutOfferIO::class.java)
        patchOffersForBulk.offers[0].shiftOfferDates()
        GenerateData.mockForPartnerService(partnerService)

        val resp = testRestTemplate.exchange("/offer-management-api/v3/offers/bulk/$idToUpdate",
            HttpMethod.PATCH, HttpEntity(patchOffersForBulk, GenerateData.createHeader()), String::class.java)

        Assertions.assertEquals(HttpStatus.BAD_REQUEST, resp.statusCode)
    }

    @Test
    @DisplayName("Bulk Update Offers - Offer Not Found in Bulk Job - Bad Request")
    fun patchBulkJob_offerNotFoundInBulk_failure() {
        val idToUpdate = "47224121-a83c-4435-9cdc-19906810e1af"

        val patchOffersForBulk = mapper.readValue(OfferMgmtV3FunctionalTest::class.java.getResourceAsStream(
                "/test-data/bulk/bulk_put_offer_invalid_offer.json"), BulkPutOfferIO::class.java)
        patchOffersForBulk.offers[0].shiftOfferDates()
        val resp = testRestTemplate.exchange("/offer-management-api/v3/offers/bulk/$idToUpdate",
                HttpMethod.PATCH, HttpEntity(patchOffersForBulk, GenerateData.createHeader()), String::class.java)

        Assertions.assertEquals(HttpStatus.BAD_REQUEST, resp.statusCode)
    }

    @Test
    @DisplayName("Bulk Update Offers - Schema Validation should fail in Offer to Be updated - Bad Request")
    fun patchBulkJob_schemaValidation_failure() {
        val idToUpdate = "47224121-a83c-4435-9cdc-19906810e1af"

        val patchOffersForBulk = mapper.readValue(OfferMgmtV3FunctionalTest::class.java.getResourceAsStream(
                "/test-data/bulk/bulk_put_offer_invalid_offer.json"), BulkPutOfferIO::class.java)
        patchOffersForBulk.offers[0].shiftOfferDates()
        val resp = testRestTemplate.exchange("/offer-management-api/v3/offers/bulk/$idToUpdate",
                HttpMethod.PATCH, HttpEntity(patchOffersForBulk, GenerateData.createHeader()), String::class.java)

        Assertions.assertEquals(HttpStatus.BAD_REQUEST, resp.statusCode)
    }

    @Test
    @DisplayName("Bulk Update Offers - Missing Headers - Bad Request")
    fun patchBulkJob_unauthorized_request() {
        val idToUpdate = "47224121-a83c-4435-9cdc-19906810e1af"

        val patchOffersForBulk = mapper.readValue(OfferMgmtV3FunctionalTest::class.java.getResourceAsStream(
                "/test-data/bulk/bulk_put_offer.json"), BulkPutOfferIO::class.java)
        patchOffersForBulk.offers[0].shiftOfferDates()
        val resp = testRestTemplate.exchange("/offer-management-api/v3/offers/bulk/$idToUpdate",
                HttpMethod.PATCH, HttpEntity(patchOffersForBulk), String::class.java)

        Assertions.assertEquals(HttpStatus.BAD_REQUEST, resp.statusCode)
    }

//    @Test -- Test commented because of an inconsistency in javax.validation.constraints.@Size annotation with this spring version,
//   would be fixed in OFD-2350
    @DisplayName("Bulk Create Offers - Exception - Number of offers must not be less than $BULK_OFFER_IO_OFFERS_MIN_SIZE")
    fun bulkCreateOffers_exception_numberOfOffersMustNotBeLessThanMin() {
        val bulkRequest = BulkPostOfferIO("TestBatch", listOf())
        val resp = testRestTemplate.exchange("/offer-management-api/v3/offers/bulk",
            HttpMethod.POST, HttpEntity(bulkRequest, GenerateData.createHeader()), String::class.java)

        Helpers.createJsonDocument(resp.body)

        Assertions.assertEquals(HttpStatus.BAD_REQUEST, resp.statusCode)
        Assertions.assertTrue(resp.body?.contains(BULK_OFFER_IO_OFFERS_SIZE_ERROR_MESSAGE) ?: false)
    }

//    @Test -- Test commented because of an inconsistency in javax.validation.constraints.@Size annotation with this spring version,
//   would be fixed in OFD-2350
    @DisplayName("Bulk Create Offers - Exception - Number of offers must not exceed $BULK_OFFER_IO_OFFERS_MAX_SIZE")
    fun bulkCreateOffers_exception_numberOfOffersMustNotExceedMax() {
        val postOffer = mapper.readValue(OfferMgmtV3FunctionalTest::class.java.getResourceAsStream(
            "/test-data/post_offer_data.json"), PostOfferIO::class.java)
        postOffer.shiftOfferDates()
        // generate mock
        GenerateData.mockForCGS(postOffer, cgs)

        val offers = ArrayList<PostOfferIO>()
        for (i in 1..BULK_OFFER_IO_OFFERS_MAX_SIZE + 1) {
            offers.add(postOffer)
        }

        val bulkRequest = BulkPostOfferIO("TestBatch", offers)
        val resp = testRestTemplate.exchange("/offer-management-api/v3/offers/bulk",
            HttpMethod.POST, HttpEntity(bulkRequest, GenerateData.createHeader()), String::class.java)

        Helpers.createJsonDocument(resp.body)

        Assertions.assertEquals(HttpStatus.BAD_REQUEST, resp.statusCode)
        Assertions.assertTrue(resp.body?.contains(BULK_OFFER_IO_OFFERS_SIZE_ERROR_MESSAGE) ?: false)
    }

//    @Test -- Test commented because of an inconsistency in javax.validation.constraints.@Size annotation with this spring version,
//   would be fixed in OFD-2350
    @DisplayName("Bulk Update Offers - Exception - Number of offers must not be less than $BULK_OFFER_IO_OFFERS_MIN_SIZE")
    fun bulkUpdateOffers_exception_numberOfOffersMustNotBeLessThanMin() {
        val idToUpdate = "47224121-a83c-4435-9cdc-19906810e1af"

        val bulkRequest = BulkPutOfferIO("ValidUpdatedBatch", listOf())

        val resp = testRestTemplate.exchange("/offer-management-api/v3/offers/bulk/$idToUpdate",
            HttpMethod.PATCH, HttpEntity(bulkRequest, GenerateData.createHeader()), String::class.java)
        Assertions.assertEquals(HttpStatus.BAD_REQUEST, resp.statusCode)
        Assertions.assertTrue(resp.body?.contains(BULK_OFFER_IO_OFFERS_SIZE_ERROR_MESSAGE) ?: false)
    }

//    @Test -- Test commented because of an inconsistency in javax.validation.constraints.@Size annotation with this spring version,
//   would be fixed in OFD-2350
    @DisplayName("Bulk Update Offers - Exception - Number of offers must not exceed $BULK_OFFER_IO_OFFERS_MAX_SIZE")
    fun bulkUpdateOffers_exception_numberOfOffersMustNotExceedMax() {
        val idToUpdate = "47224121-a83c-4435-9cdc-19906810e1af"

        val putOffer = mapper.readValue(OfferMgmtV3FunctionalTest::class.java.getResourceAsStream(
            "/test-data/put_offer_data.json"), PutOfferIO::class.java)
        putOffer.shiftOfferDates()
        GenerateData.mockForCGS(putOffer, cgs)

        val offers = ArrayList<PutOfferIO>()
        for (i in 1..BULK_OFFER_IO_OFFERS_MAX_SIZE + 1) {
            offers.add(putOffer)
        }

        val bulkRequest = BulkPutOfferIO("ValidUpdatedBatch", offers)

        val resp = testRestTemplate.exchange("/offer-management-api/v3/offers/bulk/$idToUpdate",
            HttpMethod.PATCH, HttpEntity(bulkRequest, GenerateData.createHeader()), String::class.java)

        Assertions.assertEquals(HttpStatus.BAD_REQUEST, resp.statusCode)
        Assertions.assertTrue(resp.body?.contains(BULK_OFFER_IO_OFFERS_SIZE_ERROR_MESSAGE) ?: false)
    }

    @Test
    @DisplayName("Get Offer - not x-user-email header")
    fun getOffer_notUserEmail_throwsBadRequest() {
        val idToUse = "45262c25-457e-46f9-9744-188fb6118ed0"
        val partnerIdToExpect = "3db0b3e0-4134-453e-8381-08e90f2b5141"

        val resp = testRestTemplate.exchange("/offer-management-api/v3/offers/$idToUse",
            HttpMethod.GET, HttpEntity(null, null), String::class.java)

        Assertions.assertEquals(HttpStatus.BAD_REQUEST, resp.statusCode)
        Assertions.assertTrue(resp.body?.contains(UNAUTHORIZED_ERROR_MESSAGE) ?: false)
    }

    fun PostOfferIO.shiftOfferDates() {
        this.displayDate = Instant.now().plusDays(1).truncatedTo(ChronoUnit.HOURS).toString()
        this.startDate = Instant.now().plusDays(2).truncatedTo(ChronoUnit.HOURS).toString()
        this.endDate = Instant.now().plusDays(3).truncatedTo(ChronoUnit.HOURS).toString()
    }

    fun PutOfferIO.shiftOfferDates() {
        this.displayDate = Instant.now().plusDays(1).truncatedTo(ChronoUnit.HOURS).toString()
        this.startDate = Instant.now().plusDays(2).truncatedTo(ChronoUnit.HOURS).toString()
        this.endDate = Instant.now().plusDays(3).truncatedTo(ChronoUnit.HOURS).toString()
    }

}
