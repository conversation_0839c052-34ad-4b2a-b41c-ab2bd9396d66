package com.loyalty.offermanagement.functional.common

import java.time.Instant
import java.time.LocalDateTime
import java.time.ZoneOffset
import java.time.temporal.ChronoUnit

class TimeUtils {
    private constructor()

    companion object {
        fun getDateAtMidnight(): Instant {
            val midnight = LocalDateTime.now().toLocalDate().atStartOfDay()
            return midnight.toInstant(ZoneOffset.UTC)
        }

        fun getPastDate(numDays: Long = 1): Instant = getDateAtMidnight().minus(numDays, ChronoUnit.DAYS)

        fun getFutureDate(numDays: Long = 1): Instant = getDateAtMidnight().plus(numDays, ChronoUnit.DAYS)
    }
}
