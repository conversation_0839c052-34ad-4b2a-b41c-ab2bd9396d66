package com.loyalty.offermanagement.unit


import com.fasterxml.jackson.databind.ObjectMapper
import com.loyalty.nova.common.util.plusDays
import com.loyalty.offermanagement.errors.BadRequestException
import com.loyalty.offermanagement.fixtures.OfferFixtures
import com.loyalty.offermanagement.functional.common.GenerateData
import com.loyalty.offermanagement.functional.common.Helpers
import com.loyalty.offermanagement.models.Direction
import com.loyalty.offermanagement.models.OfferCriteria
import com.loyalty.offermanagement.models.PageCriteria
import com.loyalty.offermanagement.models.SortBy
import com.loyalty.offermanagement.models.v3.inout.PostOfferIO
import com.loyalty.offermanagement.repository.OfferRepository
import com.loyalty.offermanagement.services.OfferTransformService
import com.loyalty.offermanagement.services.partner.PartnerService
import com.loyalty.offermanagement.services.v3.OfferService

import org.everit.json.schema.Schema
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Test
import org.junit.runner.RunWith
import org.mockito.ArgumentMatchers
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.Mockito
import org.mockito.MockitoAnnotations
import org.mockito.junit.MockitoJUnitRunner
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.boot.test.context.SpringBootTest.WebEnvironment
import org.springframework.data.domain.PageImpl
import java.time.Instant
import java.time.LocalDateTime
import java.time.temporal.ChronoUnit
import java.util.UUID
import kotlin.test.assertFailsWith

@RunWith(MockitoJUnitRunner::class)
@SpringBootTest(webEnvironment = WebEnvironment.RANDOM_PORT)
class OfferServiceTest() {


    @InjectMocks
    lateinit var offerService: OfferService

    @Mock
    lateinit var offerRepository: OfferRepository

    @Mock
    lateinit var partnerService: PartnerService

    @Autowired
    private lateinit var testMapper: ObjectMapper

    @Mock
    private lateinit var postOfferSchema: Schema

    @Mock
    private lateinit var mapper: ObjectMapper

    @Mock
    lateinit var offerTransformService: OfferTransformService


    @BeforeEach
    fun init() {
        MockitoAnnotations.openMocks(this)
    }


    @Test
    @DisplayName("Post Offer - BadRequestException uniqueness validation")
    fun postOffer_unhappyPath_offer_uniqueness_throwsBadRequestException() {

        assertFailsWith<BadRequestException> {

            val postOffers = testMapper.readValue(
                OfferServiceTest::class.java.getResourceAsStream(
                    "/test-data/post_offer_data_uniqueness.json"
                ), PostOfferIO::class.java
            )
            postOffers.shiftOfferDates()
            val map: Map<String, String> = mapOf("x-user-email" to "<EMAIL>")
            GenerateData.mockForPartnerService(partnerService)

            Mockito.`when`(mapper.writeValueAsString(any())).thenReturn("{}")
            Mockito.doNothing().`when`(postOfferSchema).validate(any())

            Mockito.`when`(
                offerRepository.countBysponsorCodeAndIssuanceCodebetweenDates(
                    Helpers.anyMatcher(LocalDateTime::class.java), Helpers.anyMatcher(LocalDateTime::class.java),
                    Helpers.anyMatcher(String::class.java), Helpers.anyMatcher(String::class.java),
                    Helpers.anyMatcher(UUID::class.java)
                )
            ).thenReturn(1L)

            offerService.postOffer(postOffers, map)
        }
    }

    fun PostOfferIO.shiftOfferDates() {
        this.displayDate = Instant.now().plusDays(1).truncatedTo(ChronoUnit.HOURS).toString()
        this.startDate = Instant.now().plusDays(2).truncatedTo(ChronoUnit.HOURS).toString()
        this.endDate = Instant.now().plusDays(3).truncatedTo(ChronoUnit.HOURS).toString()
    }


    @Test
    @DisplayName("Get Offers - sorted by createdAt ASC")
    fun getOffers_sortedByCreatedAtAsc() {

        val now = Instant.now()
        val offers = OfferFixtures.threeOffersSortedByCreatedAtAsc(now)
        val expectedIds = offers.map { it.id.toString() }

        Mockito.`when`(
            offerRepository.doSearch(
                startFrom = any(), startTill = any(),
                endFrom = any(), endTill = any(),
                ids = any(), users = any(), partners = any(),
                bulkIds = any(), tag1 = any(), tag2 = any(), tag3 = any(),
                offerTypes = any(), awardTypes = any(), qualifiers = any(),
                mechanismType = any(),
                region1 = any(), region2 = any(), region3 = any(), region4 = any(),
                region5 = any(), region6 = any(), region7 = any(), region8 = any(),
                region9 = any(), region10 = any(), region11 = any(), region12 = any(),
                region13 = any(), region14 = any(), region15 = any(),
                statuses = any(), massOffer = any(), active = any(),
                offerCategory1 = any(), offerCategory2 = any(), offerCategory3 = any(),
                onlyEmptyCategory = any(), hasIssuanceCode = any(),
                campaignCode = any(), programTypes = any(),
                eventBasedOffer = any(),
                eligibilityDuration = any(), eligibilityDurationUnits = any(),
                pageReq = any()
            )
        ).thenReturn(PageImpl(offers))

        offers.forEach {
            val mockIO = OfferFixtures.mockOfferIO(it.id)
            Mockito.`when`(offerTransformService.transform(it)).thenReturn(mockIO)
        }


        val result = offerService.getOffers(
            OfferCriteria(),
            PageCriteria(sort = SortBy.createdAt, direction = Direction.asc)
        )

        val actualIds = result.content?.map { it.id }
        assert(actualIds == expectedIds) {
            "Expected: $expectedIds\nActual: $actualIds"
        }
    }


    @Test
    @DisplayName("Get Offers - sorted by publishedAt ASC")
    fun getOffers_sortedByPublishedAtAsc() {
        val now = Instant.now()
        val offers = OfferFixtures.threeOffersSortedByPublishedAtAsc(now)

        Mockito.`when`(
            offerRepository.doSearch(
                startFrom = any(), startTill = any(),
                endFrom = any(), endTill = any(),
                ids = any(), users = any(), partners = any(),
                bulkIds = any(), tag1 = any(), tag2 = any(), tag3 = any(),
                offerTypes = any(), awardTypes = any(), qualifiers = any(),
                mechanismType = any(),
                region1 = any(), region2 = any(), region3 = any(), region4 = any(),
                region5 = any(), region6 = any(), region7 = any(), region8 = any(),
                region9 = any(), region10 = any(), region11 = any(), region12 = any(),
                region13 = any(), region14 = any(), region15 = any(),
                statuses = any(), massOffer = any(), active = any(),
                offerCategory1 = any(), offerCategory2 = any(), offerCategory3 = any(),
                onlyEmptyCategory = any(), hasIssuanceCode = any(),
                campaignCode = any(), programTypes = any(),
                eventBasedOffer = any(),
                eligibilityDuration = any(), eligibilityDurationUnits = any(),
                pageReq = any()
            )
        ).thenReturn(PageImpl(offers))

        offers.forEach { offer ->
            val offerIO = OfferFixtures.mockOfferIO(offer.id)
            Mockito.`when`(offerTransformService.transform(offer)).thenReturn(offerIO)
        }

        val results = offerService.getOffers(
            OfferCriteria(),
            PageCriteria(sort = SortBy.publishedAt, direction = Direction.asc)
        )

        val expectedIds = offers.sortedBy { it.publishedAt }.map { it.id.toString() }
        val actualIds = results.content?.map { it.id }

        assert(actualIds == expectedIds) {
            "Expected: $expectedIds\nActual: $actualIds"
        }
    }

    @Test
    @DisplayName("Get Offers - sorted by updatedAt ASC")
    fun getOffers_sortedByUpdatedAtAsc() {
        val now = Instant.now()
        val offers = OfferFixtures.threeOffersSortedByUpdatedAtAsc(now)

        Mockito.`when`(
            offerRepository.doSearch(
                startFrom = any(), startTill = any(),
                endFrom = any(), endTill = any(),
                ids = any(), users = any(), partners = any(),
                bulkIds = any(), tag1 = any(), tag2 = any(), tag3 = any(),
                offerTypes = any(), awardTypes = any(), qualifiers = any(),
                mechanismType = any(),
                region1 = any(), region2 = any(), region3 = any(), region4 = any(),
                region5 = any(), region6 = any(), region7 = any(), region8 = any(),
                region9 = any(), region10 = any(), region11 = any(), region12 = any(),
                region13 = any(), region14 = any(), region15 = any(),
                statuses = any(), massOffer = any(), active = any(),
                offerCategory1 = any(), offerCategory2 = any(), offerCategory3 = any(),
                onlyEmptyCategory = any(), hasIssuanceCode = any(),
                campaignCode = any(), programTypes = any(),
                eventBasedOffer = any(),
                eligibilityDuration = any(), eligibilityDurationUnits = any(),
                pageReq = any()
            )
        ).thenReturn(PageImpl(offers))

        offers.forEach { offer ->
            val offerIO = OfferFixtures.mockOfferIO(offer.id)
            Mockito.`when`(offerTransformService.transform(offer)).thenReturn(offerIO)
        }

        val results = offerService.getOffers(
            OfferCriteria(),
            PageCriteria(sort = SortBy.updatedAt, direction = Direction.asc)
        )

        val expectedIds = offers.sortedBy { it.updatedAt }.map { it.id.toString() }
        val actualIds = results.content?.map { it.id }

        assert(actualIds == expectedIds) {
            "Expected: $expectedIds\nActual: $actualIds"
        }
    }

}

@Suppress("UNCHECKED_CAST")
fun <T> any(): T = ArgumentMatchers.any<T>() as T
