package com.loyalty.offermanagement.models.v3.inout

import com.loyalty.offermanagement.functional.OfferManagementFunctionalTest
import com.loyalty.offermanagement.models.LocalizedString
import org.junit.jupiter.api.Test
import kotlin.test.assertTrue

class MechanismIOTest : OfferManagementFunctionalTest() {

    @Test
    fun fallback_withMechanismTypeWithEnglishOnly_shouldUpdateMechanismValueFrenchAsEnglishValue() {
        MechanismIO.mechanismTypeWithEnglishOnly.forEach { mechanism ->
            val mechanismIO = MechanismIO(mechanismType = mechanism, mechanismValue = LocalizedString("EnglishValue", "FrenchValue"))
            mechanismIO.fallback()

            assertTrue(mechanismIO.mechanismValue?.enUS == "EnglishValue")
            assertTrue(mechanismIO.mechanismValue?.frCA == "EnglishValue")
        }
    }

    @Test
    fun fallback_withMechanismTypeWithEnglishAndFrench_shouldNotUpdateMechanismValues() {
        MechanismIO.mechanismTypeWithEnglishAndFrench.forEach { mechanism ->
            val mechanismIO = MechanismIO(
                    mechanismType = mechanism,
                    mechanismValue = LocalizedString("EnglishValue", "FrenchValue"))
            mechanismIO.fallback()

            assertTrue(mechanismIO.mechanismValue?.enUS == "EnglishValue")
            assertTrue(mechanismIO.mechanismValue?.frCA == "FrenchValue")
        }
    }

    @Test
    fun fallback_withMechanismTypeWithEnglishAndFrenchEmpty_shouldUpdateMechanismValueFrenchAsEnglishValue() {
        MechanismIO.mechanismTypeWithEnglishAndFrench.forEach { mechanism ->
            val mechanismIO = MechanismIO(
                    mechanismType = mechanism,
                    mechanismValue = LocalizedString("EnglishValue", ""))
            mechanismIO.fallback()

            assertTrue(mechanismIO.mechanismValue?.enUS == "EnglishValue")
            assertTrue(mechanismIO.mechanismValue?.frCA == "EnglishValue")
        }
    }

    @Test
    fun fallback_withMechanismTypeWithNoMechanismValue_shouldUpdateMechanismValueToNull() {
        MechanismIO.mechanismTypeWithNoMechanismValue.forEach { mechanism ->
            val mechanismIO = MechanismIO(
                    mechanismType = mechanism,
                    mechanismValue = LocalizedString("EnglishValue", "FrenchValue"))
            mechanismIO.fallback()

            assertTrue(mechanismIO.mechanismValue == null)
        }
    }
}
