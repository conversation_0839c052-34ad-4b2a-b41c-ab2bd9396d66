@file:Suppress(
        "Complex<PERSON>ethod",
        "ComplexMethod",
        "LargeClass",
        "LongMethod",
        "MagicNumber",
        "MaximumLineLength",
        "Max<PERSON>ineLength",
        "ReturnCount",
        "ReturnCount",
        "ThrowsCount",
        "TooGenericExceptionCaught",
        "TooManyFunctions",
        "UnusedPrivateMember",
        "ReturnCount"
)

package com.loyalty.offermanagement.extractors

import com.fasterxml.jackson.databind.JsonNode
import com.loyalty.offermanagement.models.LocalizedString
import com.loyalty.offermanagement.models.isNullOrEmpty
import com.loyalty.offermanagement.models.v3.Availability
import com.loyalty.offermanagement.models.v3.AwardType
import com.loyalty.offermanagement.models.v3.ImageProps
import com.loyalty.offermanagement.models.v3.LocalizedImage
import com.loyalty.offermanagement.models.v3.MechanismType
import com.loyalty.offermanagement.models.v3.OfferLimitation
import com.loyalty.offermanagement.models.v3.OfferStatus
import com.loyalty.offermanagement.models.v3.OfferType
import com.loyalty.offermanagement.models.v3.ProgramType
import com.loyalty.offermanagement.models.v3.Qualifier
import com.loyalty.offermanagement.models.v3.Region
import com.loyalty.offermanagement.models.v3.inout.ContentDetailsIO
import com.loyalty.offermanagement.models.v3.inout.MechanismIO
import com.loyalty.offermanagement.models.v3.inout.OfferIO
import com.loyalty.offermanagement.models.v3.inout.TierIO
import org.slf4j.LoggerFactory
import java.time.Instant

@Deprecated(message = "Offer V1 is not being used anymore. All related classes will be removed in a future ticket.")
class DynamoOfferV1ToIOJsonNodeExtractor : JsonNodeExtractorBase<OfferIO>() {
    private val logger by lazy { LoggerFactory.getLogger(this::class.java) }

    @Throws(JsonNodeExtractorException::class)
    override fun extractItem(jsonNode: JsonNode): OfferIO {
        try {
            val offerType = jsonNode.pathsAsString("offerType")
                    .let { offerTypeValue ->
                        return@let OfferType.values()
                                .firstOrNull { offerType -> offerType.value == offerTypeValue }
                                ?: throw JsonNodeExtractorException("""No valid offerType, found "$offerTypeValue" instead""")
                    }

            val awardType = jsonNode.pathsAsString("awardType")
                    .let { awardTypeValue ->
                        return@let AwardType.values()
                                .firstOrNull { awardType -> awardType.value == awardTypeValue }
                                ?: throw JsonNodeExtractorException("""No valid awardType, found "$awardTypeValue" instead""")
                    }

            val qualifier = jsonNode.pathsAsString("qualifier")
                    .let { qualifierValue ->
                        return@let Qualifier.values()
                                .firstOrNull { qualifier -> qualifier.value == qualifierValue }
                                ?: throw JsonNodeExtractorException("""No valid qualifier, found "$qualifierValue" instead""")
                    }
            val programType = jsonNode.pathsAsString("programType")
                    .let { programTypeValue ->
                        return@let ProgramType.values()
                                .firstOrNull { programType -> programType.value == programTypeValue }
                                ?: throw JsonNodeExtractorException("""No valid programType, found "$programTypeValue" instead""")
                    }

            val offerIO = OfferIO(
                    id = jsonNode.pathsAsString("id"),
                    duplicatedFrom = jsonNode.pathsAsString("duplicatedFrom").takeUnless(String::isBlank),
                    contentfulId = jsonNode.pathsAsString("contentfulId"),
                    detailsId = jsonNode.pathsAsString("detailsId"),
                    partnerId = jsonNode.pathsAsString("partnerId"),
                    partnerName = jsonNode.pathsAsString("partnerName"),
                    offerType = offerType,
                    qualifier = qualifier,
                    awardType = awardType,
                    programType = programType,
                    displayDate = jsonNode.pathsAsString("displayDate").let(Instant::parse),
                    startDate = jsonNode.pathsAsString("startDate").let(Instant::parse),
                    endDate = jsonNode.pathsAsString("endDate").let(Instant::parse),
                    issuanceCode = jsonNode.pathsAsString("issuanceCode").takeUnless(String::isBlank),
                    displayPriority = jsonNode.pathsAsInt("displayPriority"),
                    tags = listOf(replaceTagWithID(
                            jsonNode.pathsAsString("promotionalTag"),
                            jsonNode.pathsAsString("id"))
                    ).filterNot { tag -> tag.isBlank() },
                    regions = jsonNode.pathsAsJsonNodeMap("regions")
                            .filter { (_, jsonNode) -> jsonNode.pathsAsBoolean() }
                            .map { (regionKey, _) -> Region.valueOf(regionKey) },
                    awardShort = jsonNode.pathsAsLocalizedString("awardShort"),
                    qualifierShort = jsonNode.pathsAsLocalizedString("qualifierShort"),
                    partnerBaseEarnRate = jsonNode.pathsAsInt("partnerBaseEarnRate"),
                    canBeCombined = jsonNode.pathsAsBoolean("canBeCombined"), // TODO("default = true")
                    combinationsText = jsonNode.pathsAsLocalizedString("combinations"),
                    exclusions = LocalizedString(
                            enUS = jsonNode.pathsAsJsonNodeList("exclusions")
                                    .mapNotNull { jsonNode -> jsonNode.pathsAsLocalizedString() }
                                    .mapNotNull(LocalizedString::enUS)
                                    .connect("en-US"),
                            frCA = jsonNode.pathsAsJsonNodeList("exclusions")
                                    .mapNotNull { jsonNode -> jsonNode.pathsAsLocalizedString() }
                                    .mapNotNull(LocalizedString::frCA)
                                    .connect("fr-CA")
                    ),
                    availability = jsonNode.pathsAsJsonNodeMap("availability")
                            .filter { (_, jsonNode) -> jsonNode.pathsAsBoolean() }
                            .mapNotNull { (availabilityKey, _) -> Availability.values().firstOrNull { availability -> availability.value == availabilityKey } },
                    partnerUrl = jsonNode.pathsAsLocalizedString("partnerUrl"),
                    offerLimitation = jsonNode.pathsAsString("offerLimitation")
                            .let { offerLimitationValue ->
                                if (offerLimitationValue === "perCollectorPerCustom") {
                                    return@let OfferLimitation.Custom
                                }
                                return@let OfferLimitation.values()
                                        .firstOrNull { offerLimitation -> offerLimitation.value == offerLimitationValue }
                                        ?: OfferLimitation.NoLimit
                            },
                    offerLimitationText = jsonNode.pathsAsLocalizedString("offerLimitationText"),
                    daysToApply = jsonNode.pathsAsInt("daysToApply").takeUnless { i -> i == 0 },
                    trademarkInfo = jsonNode.pathsAsLocalizedString("trademarkInfo"),
                    partnerLegalName = jsonNode.pathsAsLocalizedString("partnerLegalName"),
                    legalText = jsonNode.pathsAsLocalizedString("legalText").let { localizedString ->
                        return@let LocalizedString(
                                enUS = localizedString?.enUS?.collapseWhitespace() ?: String(),
                                frCA = localizedString?.frCA?.collapseWhitespace() ?: String()
                        )
                    },
                    createdAt = jsonNode.pathsAsString("createdAt").let(Instant::parse),
                    createdBy = jsonNode.pathsAsString("createdBy"),
                    updatedAt = jsonNode.pathsAsString("updatedAt").takeUnless(String::isBlank)?.let(Instant::parse),
                    updatedBy = jsonNode.pathsAsString("updatedBy").takeUnless(String::isBlank),
                    publishedAt = jsonNode.pathsAsString("publishedAt").takeUnless(String::isBlank)?.let(Instant::parse),
                    publishedBy = jsonNode.pathsAsString("publishedBy").takeUnless(String::isBlank),
                    status = jsonNode.pathsAsString("status")
                            .let(String::toUpperCase)
                            .let(OfferStatus::valueOf),
                    description = jsonNode.pathsAsLocalizedString("description"),
                    hasCustomLegal = jsonNode.pathsAsBoolean("hasCustomLegal"),
                    includedLocations = jsonNode.pathsAsJsonNodeList("includedLocations")
                            .mapNotNull { jsonNode -> jsonNode.pathsAsLocalizedString() },
                    excludedLocations = jsonNode.pathsAsJsonNodeList("excludedLocations")
                            .mapNotNull { jsonNode -> jsonNode.pathsAsLocalizedString() },
                    includedBanners = jsonNode.pathsAsJsonNodeList("includedBanners")
                            .mapNotNull { jsonNode -> jsonNode.pathsAsLocalizedString() },
                    excludedBanners = jsonNode.pathsAsJsonNodeList("excludedBanners")
                            .mapNotNull { jsonNode -> jsonNode.pathsAsLocalizedString() },
                    mechanisms = jsonNode.pathsAsString("mechanismType")
                            .let { mechanismTypeValue ->
                                val barcodeTypeValue = jsonNode.pathsAsString("mechanism", "M", "barcodeType")
                                val cashierCodeTypeValue = jsonNode.pathsAsString("mechanism", "M", "cashierCodeType")
                                val mechanismType = this.parseMechanismType(mechanismTypeValue, barcodeTypeValue, cashierCodeTypeValue)

                                val mechanismIO = MechanismIO(
                                        mechanismType = mechanismType,
                                        mechanismValue = when (mechanismType) {
                                            MechanismType.BarcodeUPC,
                                            MechanismType.BarcodeEAN,
                                            MechanismType.BarcodeCODE39,
                                            MechanismType.BarcodeCODE128 -> LocalizedString(
                                                    enUS = jsonNode.pathsAsString("mechanism", "M", "barcodeValue")
                                            )
                                            MechanismType.PLU -> LocalizedString(
                                                    enUS = jsonNode.pathsAsString("mechanism", "M", "pluCode")
                                            )
                                            MechanismType.CouponCode -> jsonNode.pathsAsLocalizedString("mechanism", "M", "couponCode")
                                            MechanismType.Button -> jsonNode.pathsAsLocalizedString("mechanism", "M", "buttonUrl")
                                            else -> null
                                        },
                                        mechanismLabel = when (mechanismType) {
                                            MechanismType.Button -> jsonNode.pathsAsLocalizedString("mechanism", "M", "buttonText")
                                            else -> null
                                        },
                                        mechanismText = null
                                )
                                return@let listOf(mechanismIO)
                            },
                    image = LocalizedImage(
                            enUS = ImageProps(
                                    path = jsonNode.paths("image", "M", "en-US", "M").pathsAsString("link")
                            ),
                            frCA = ImageProps(
                                    path = jsonNode.paths("image", "M", "fr-CA", "M").pathsAsString("link")
                            )
                    ),
                    bulkId = null,
                    bulkName = null,
                    tiers = jsonNode.pathsAsJsonNodeList("tiers").map { jsonNode ->
                        return@map TierIO(
                                awardValue = when (awardType) {
                                    AwardType.FlatMiles -> jsonNode.pathsAsJsonNodeMap()["miles"]?.pathsAsFloat()
                                    AwardType.MultiplierMiles -> jsonNode.pathsAsJsonNodeMap()["multiplier"]?.pathsAsFloat()
                                    AwardType.FlatDiscount -> jsonNode.pathsAsJsonNodeMap()["dollars"]?.pathsAsFloat()
                                    AwardType.PercentDiscount -> jsonNode.pathsAsJsonNodeMap()["percent"]?.pathsAsFloat()
                                    AwardType.CashDiscount -> jsonNode.pathsAsJsonNodeMap()["miles"]?.pathsAsFloat()
                                    else -> null
                                },
                                qualifierValue = when (qualifier) {
                                    Qualifier.StoreWide,
                                    Qualifier.Category -> jsonNode.pathsAsJsonNodeMap()["spend"]?.pathsAsFloat()
                                    Qualifier.Product -> jsonNode.pathsAsJsonNodeMap()["quantity"]?.pathsAsFloat()
                                    Qualifier.CashDiscount -> 95f - (jsonNode.pathsAsJsonNodeMap()["miles"]?.pathsAsFloat()
                                            ?: 0f)
                                    Qualifier.CashRedemption -> 95f
                                    else -> null
                                },
                                awardLong = jsonNode.pathsAsJsonNodeMap()["offerAwardText"]?.pathsAsLocalizedString(),
                                qualifierLong = jsonNode.pathsAsJsonNodeMap()["offerQualifierText"]?.pathsAsLocalizedString(),
                                content = when (qualifier) {
                                    Qualifier.Product -> jsonNode.pathsAsJsonNodeMap()["product"]
                                            ?.pathsAsJsonNodeList()
                                            ?.mapNotNull { jsonNode -> jsonNode.pathAsContentDetails() }
                                            .orEmpty()
                                    Qualifier.Category -> jsonNode.pathsAsJsonNodeMap()["category"]
                                            ?.pathsAsJsonNodeList()
                                            ?.mapNotNull { jsonNode -> jsonNode.pathAsContentDetails() }
                                            .orEmpty()
                                    else -> emptyList()
                                }
                        )
                    },
                    baseCashRedemption = 95,
                    cashierInstruction = null
            )

            return offerIO
                    .copy(
                            exclusions = if (offerIO.exclusions.isNullOrEmpty) null else offerIO.exclusions
                    )
                    .also(OfferIO::fallback)
        } catch (throwable: Throwable) {
            this.logger.error("Something went wrong", throwable)
            throw throwable
        }
    }

    internal fun parseMechanismType(
            mechanismTypeValue: String,
            barcodeTypeValue: String,
            cashierCodeTypeValue: String
    ): MechanismType {
        return when (mechanismTypeValue) {
            "noAction" -> MechanismType.NoAction
            "button" -> MechanismType.Button
            "cashierCode" -> when (cashierCodeTypeValue) {
                "plu" -> MechanismType.PLU
                "couponCode" -> MechanismType.CouponCode
                else -> throw JsonNodeExtractorException("""No valid cashierCodeType, found "$cashierCodeTypeValue" instead""")
            }
            "barcode" -> when (barcodeTypeValue) {
                "upc" -> MechanismType.BarcodeUPC
                "ean" -> MechanismType.BarcodeEAN
                "code39" -> MechanismType.BarcodeCODE39
                "code128" -> MechanismType.BarcodeCODE128
                else -> throw JsonNodeExtractorException("""No valid barcodeType, found "$barcodeTypeValue" instead""")
            }
            "optIn" -> throw Exception("$mechanismTypeValue not available for OfferV2")
            else -> throw JsonNodeExtractorException("""No valid mechanismType, found "$mechanismTypeValue" instead""")
        }
    }

    private fun JsonNode.pathsAsJsonNodeList(vararg segments: Any): List<JsonNode> {
        return this.paths(*segments).paths("L").elements().asSequence().toList()
    }

    private fun JsonNode.pathsAsJsonNodeMap(vararg segments: Any): Map<String, JsonNode> {
        return this.paths(*segments).paths("M").fields().asSequence().associate { entry -> entry.key to entry.value }
    }

    private fun JsonNode.pathsAsLocalizedString(vararg segments: Any): LocalizedString? {
        return this.paths(*segments).pathsAsJsonNodeMap().let { map ->
            if (map.isNotEmpty()) {
                return@let LocalizedString(
                        enUS = map["en-US"]?.pathsAsString() ?: String(),
                        frCA = map["fr-CA"]?.pathsAsString() ?: String()
                )
            }
            return@let null
        }
    }

    private fun JsonNode.pathAsContentDetails(vararg segments: Any): ContentDetailsIO? {
        return this.paths(*segments).pathsAsJsonNodeMap().let { map ->
            ContentDetailsIO(
                    enUS = map["en-US"]?.pathsAsString() ?: String(),
                    frCA = map["fr-CA"]?.pathsAsString() ?: String(),
                    masterProduct = false
            )
        }
    }

    private fun replaceTagWithID(tagItem: String, id: String): String {
        return when (tagItem) {
            "shopTheBlock" -> "924ad367-f930-439c-93eb-823952d4228d"
            "megaMiles" -> "4db99143-1768-4f85-a44a-d6fa16011f7b"
            "bonusBoom" -> "0f91f291-dc7c-45d3-9a76-fbdadc6cd836"
            "roundUpContest" -> "f109518a-313c-40e3-b3f0-3023fa34a55c"
            "offerHub" -> "1f0d2275-b289-4f7f-b72d-da76650ba1cf"
            else -> tagItem.also { tagItem ->
                if (tagItem.isNotBlank()) {
                    this.logger.error("the tag of $tagItem was not replaced for offer $id")
                }
            }
        }
    }

    private fun JsonNode.pathsAsString(vararg segments: Any): String {
        return this.paths(*segments).paths("S").asText()
    }

    private fun JsonNode.pathsAsFloat(vararg segments: Any): Float {
        return this.paths(*segments).paths("N").asDouble().toFloat()
    }

    private fun JsonNode.pathsAsDouble(vararg segments: Any): Double {
        return this.paths(*segments).paths("N").asDouble()
    }

    private fun JsonNode.pathsAsInt(vararg segments: Any): Int {
        return this.paths(*segments).paths("N").asInt()
    }

    private fun JsonNode.pathsAsBoolean(vararg segments: Any): Boolean {
        return this.paths(*segments).paths("BOOL").asBoolean()
    }

    private fun JsonNode.paths(vararg segments: Any): JsonNode {
        return segments.fold(this) { jsonNode, segment ->
            return@fold when (segment) {
                is String -> jsonNode.path(segment)
                is Int -> jsonNode.path(segment)
                else -> segment.toString().let(jsonNode::path)
            }
        }
    }

    private fun List<String>.connect(locale: String): String {
        if (this.isEmpty()) {
            return String()
        }

        if (this.size == 1) {
            return this.first()
        }

        return when (locale) {
            "en-US" -> "${this.joinToString(separator = ", ", truncated = "and", limit = this.size - 1)} ${this.last()}"
            "fr-CA" -> "${this.joinToString(separator = ", ", truncated = "et", limit = this.size - 1)} ${this.last()}"
            else -> "Pardon?"
        }
    }
}
