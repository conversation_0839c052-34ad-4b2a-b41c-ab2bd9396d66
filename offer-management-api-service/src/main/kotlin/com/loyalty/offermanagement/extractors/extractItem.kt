package com.loyalty.offermanagement.extractors

import com.fasterxml.jackson.databind.ObjectMapper
import java.io.File
import java.io.InputStream

fun <R> JsonNodeExtractor<R>.extractItem(file: File, objectMapper: ObjectMapper): R {
    return objectMapper.readTree(file).let(this::extractItem)
}

fun <R> JsonNodeExtractor<R>.extractItem(inputStream: InputStream, objectMapper: ObjectMapper): R {
    return objectMapper.readTree(inputStream).let(this::extractItem)
}
