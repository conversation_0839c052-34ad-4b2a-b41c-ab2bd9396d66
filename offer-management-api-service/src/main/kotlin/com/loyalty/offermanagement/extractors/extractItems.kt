package com.loyalty.offermanagement.extractors

import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.ObjectMapper
import java.io.File
import java.io.InputStream

fun <R> JsonNodeExtractor<R>.extractItems(jsonNode: JsonNode?): List<Result<R>> {
    return jsonNode?.path("Items")?.elements()?.asSequence().orEmpty().toList().map { jsonNode ->
        return@map runCatching { this.extractItem(jsonNode) }
    }
}

fun <R> JsonNodeExtractor<R>.extractItems(file: File, objectMapper: ObjectMapper): List<Result<R>> {
    return objectMapper.readTree(file).let(this::extractItems)
}

fun <R> JsonNodeExtractor<R>.extractItems(inputStream: InputStream, objectMapper: ObjectMapper): List<Result<R>> {
    return objectMapper.readTree(inputStream).let(this::extractItems)
}
