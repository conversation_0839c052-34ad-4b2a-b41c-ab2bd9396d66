@file:Suppress(
        "<PERSON><PERSON><PERSON><PERSON>",
        "MagicN<PERSON>ber"
)

package com.loyalty.offermanagement

import com.amazonaws.client.builder.AwsClientBuilder
import com.amazonaws.services.dynamodbv2.AmazonDynamoDB
import com.amazonaws.services.dynamodbv2.AmazonDynamoDBClientBuilder
import com.fasterxml.jackson.databind.DeserializationFeature
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.databind.SerializationFeature
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.loyalty.offermanagement.extractors.DynamoOfferV1ToIOJsonNodeExtractor
import com.loyalty.offermanagement.extractors.JsonNodeExtractorBase
import com.loyalty.offermanagement.models.v3.inout.OfferIO
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Value
import org.springframework.beans.factory.config.ConfigurableBeanFactory
import org.springframework.boot.context.event.ApplicationReadyEvent
import org.springframework.boot.context.properties.ConfigurationProperties
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.context.annotation.Scope
import org.springframework.context.event.EventListener
import org.springframework.core.env.AbstractEnvironment
import org.springframework.core.env.EnumerablePropertySource
import org.springframework.core.env.Environment
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory
import org.springframework.web.client.RestTemplate
import java.util.Arrays
import java.util.stream.StreamSupport

@Configuration
@ConfigurationProperties
class SpringConfig {
    val logger by lazy { LoggerFactory.getLogger(this::class.java) }

    @Value("\${aws.region}")
    lateinit var awsRegion: String

    @Value("\${spring.datasource.masteruser.secret}")
    lateinit var dbMasteruserSecret: String

    @Value("\${spring.datasource.adminuser.secret}")
    lateinit var dbAdminuserSecret: String

    @Value("\${spring.datasource.appuser.secret}")
    lateinit var dbAppuserSecret: String

    @Value("\${spring.datasource.reportinguser.secret}")
    lateinit var dbReportinguserSecret: String

    @Value("\${database.name}")
    lateinit var databaseName: String

    @Value("\${database.writer.endpoint}")
    lateinit var databaseWriterEndpoint: String

    @Value("\${url.prefix}")
    lateinit var urlPrefix: String

    @Value("\${cgs.endpoint}")
    lateinit var cgsEndpoint: String

    @Value("\${offer.api.v2.endpoint}")
    lateinit var offerV2Endpoint: String

    @Value("\${nova.bus.region}")
    lateinit var novaBusRegion: String

    @Value("\${nova.bus.endpoint}")
    lateinit var novaBusEndpoint: String

    @Value("\${nova.bus.table.name}")
    lateinit var novaBusTableName: String

    @Value("\${offer.facets.endpoint}")
    lateinit var offerFacetsEndpoint: String

    @Value("\${offer.promotions.endpoint}")
    lateinit var offerPromotionsEndpoint: String

    @Value("\${environment}")
    lateinit var environment: String

    @Value("\${offer.default.image.base}")
    lateinit var offerDefaultImageBasePath: String

    @Value("\${partners.endpoint}")
    lateinit var partnersEndpoint: String

    @Bean
    @Scope(value = ConfigurableBeanFactory.SCOPE_SINGLETON)
    fun getMapper(): ObjectMapper = jacksonObjectMapper()
            .registerModule(JavaTimeModule())
            .disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS)
            .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)

    // just chose HttpComponentsClientHttpRequestFactory over SimpleClientHttpRequestFactory
    // because of connection pooling functionality which maybe needed in future
    @Bean

    @Scope(value = ConfigurableBeanFactory.SCOPE_SINGLETON)
    internal fun clientHttpRequestFactory(): HttpComponentsClientHttpRequestFactory {
        val clientHttpRequestFactory = HttpComponentsClientHttpRequestFactory()
        clientHttpRequestFactory.setConnectTimeout(10000)
        clientHttpRequestFactory.setConnectionRequestTimeout(10000)
        clientHttpRequestFactory.setReadTimeout(10000)
        return clientHttpRequestFactory
    }

    @Bean
    internal fun restTemplate(): RestTemplate {
        return RestTemplate(clientHttpRequestFactory())
    }

    @Bean
    @Scope(value = ConfigurableBeanFactory.SCOPE_SINGLETON)
    fun getJsonNodeExtractorOfOfferIO(): JsonNodeExtractorBase<OfferIO> {
        return DynamoOfferV1ToIOJsonNodeExtractor()
    }

    @Bean
    @Scope(value = ConfigurableBeanFactory.SCOPE_SINGLETON)
    fun getDynamoDBClient(): AmazonDynamoDB = AmazonDynamoDBClientBuilder.standard().withEndpointConfiguration(
            AwsClientBuilder.EndpointConfiguration(novaBusEndpoint, novaBusRegion)
    ).build()

    @EventListener(ApplicationReadyEvent::class)
    fun printProperties(event: ApplicationReadyEvent) {
        val buffer: StringBuffer = StringBuffer("============ System Properties ============\n")

        val properties = System.getProperties()
        properties.forEach { k, v -> buffer.append("$k = $v\n") }

        buffer.append("============ env Properties ============\n")

        val env = System.getenv()
        env.forEach { k, v -> buffer.append("$k = $v\n") }

        buffer.append("============ configuration ============\n")
        val contextEnv: Environment = event.applicationContext.environment
        buffer.append("Active profiles: ", Arrays.toString(contextEnv.activeProfiles))
        buffer.append("\n")
        val sources = (contextEnv as AbstractEnvironment).propertySources
        StreamSupport.stream(sources.spliterator(), false)
                .filter { ps -> ps is EnumerablePropertySource<*> }
                .map { ps -> (ps as EnumerablePropertySource<*>).propertyNames }
                .flatMap(Arrays::stream)
                .distinct()
                .forEach { prop -> buffer.append("$prop: ${getPropertyValue(prop, contextEnv)}\n") }
        buffer.append("===========================================")

        if (environment != "prod") {
            logger.info("Environment :\n$buffer")
        }
    }

    private fun getPropertyValue(name: String, contextEnv: Environment): String? {
        return if (name.contains("password")) {
            "*****"
        } else {
            contextEnv.getProperty(name)
        }
    }
}

