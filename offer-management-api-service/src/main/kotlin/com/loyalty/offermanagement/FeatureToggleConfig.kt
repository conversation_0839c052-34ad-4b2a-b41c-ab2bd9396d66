package com.loyalty.offermanagement

import org.springframework.beans.factory.annotation.Value
import org.springframework.boot.context.properties.ConfigurationProperties
import org.springframework.context.annotation.Configuration

@Configuration
@ConfigurationProperties
class FeatureToggleConfig {
    @Value("\${feature.toggle.stream.event.v3}")
    lateinit var featureToggleStreamEventV3: String

    @Value("\${feature.toggle.stream.event.v2}")
    lateinit var featureToggleStreamEventV2: String

    @Value("\${feature.toggle.clo}")
    lateinit var featureToggleClo: String

    @Value("\${feature.toggle.am.receipts}")
    lateinit var featureToggleAmReceipts: String

}
