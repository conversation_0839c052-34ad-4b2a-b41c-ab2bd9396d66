package com.loyalty.offermanagement

import com.loyalty.offermanagement.utils.DataSourceFactory
import org.springframework.boot.context.properties.ConfigurationProperties
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.context.annotation.Primary
import javax.sql.DataSource

@Configuration
class DataSourceConfig(
        private val config: SpringConfig,
        private val flywayConfig: FlywayConfig,
        private val dataSourceFactory: DataSourceFactory
) {
    @ConfigurationProperties(prefix = "spring.datasource")
    @Bean(name = ["userDataSource"])
    @Primary
    fun userDataSource(): DataSource {
        flywayConfig.migrateFlyway()
        return dataSourceFactory.createDataSource(config.dbAppuserSecret)
    }
}