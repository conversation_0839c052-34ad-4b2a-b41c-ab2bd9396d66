package com.loyalty.offermanagement.entities

import com.fasterxml.jackson.annotation.JsonIgnore
import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonInclude
import com.loyalty.offermanagement.models.v3.*
import org.hibernate.annotations.OrderBy
import org.hibernate.annotations.LazyCollection
import org.hibernate.annotations.LazyCollectionOption
import org.springframework.format.annotation.DateTimeFormat
import java.time.Instant
import java.time.LocalDateTime
import java.util.UUID
import javax.persistence.CascadeType
import javax.persistence.CollectionTable
import javax.persistence.Column
import javax.persistence.ElementCollection
import javax.persistence.Entity
import javax.persistence.EnumType
import javax.persistence.Enumerated
import javax.persistence.FetchType
import javax.persistence.Id
import javax.persistence.Index
import javax.persistence.JoinColumn
import javax.persistence.JoinTable
import javax.persistence.ManyToOne
import javax.persistence.OneToMany
import javax.persistence.Table

@Entity
@Table(name = "offers", indexes = [
    Index(columnList = "display_date", name = "offers_display_date"),
    Index(columnList = "start_date", name = "offers_start_date"),
    Index(columnList = "end_date", name = "offers_end_date"),
    Index(columnList = "partner_id", name = "partner_id_idx"),
    Index(columnList = "partner_name", name = "partner_name_idx")
])
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
data class Offer(
        @Id
        @Column(columnDefinition = "uuid", updatable = false)
        var id: UUID = UUID.randomUUID(),

        @Enumerated(value = EnumType.STRING)
        var status: OfferStatus = OfferStatus.DRAFT,

        @Column(name = "created_at")
        @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
        var createdAt: Instant = Instant.now(),

        @Column(name = "created_by")
        var createdBy: String = "",

        @Column(name = "updated_at")
        @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
        var updatedAt: Instant? = null,

        @Column(name = "updated_by")
        var updatedBy: String? = null,

        @Column(name = "published_at")
        @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
        var publishedAt: Instant? = null,

        @Column(name = "published_by")
        var publishedBy: String? = null,

        @Column(name = "partner_id")
        var partnerId: UUID? = null,

        @Column(name = "partner_offer_id")
        var partnerOfferId: String? = null,

        @Column(name = "display_date")
        @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
        var displayDate: LocalDateTime? = null,

        @Column(name = "start_date")
        @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
        var startDate: LocalDateTime? = null,

        @Column(name = "end_date")
        @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
        var endDate: LocalDateTime? = null,

        @Enumerated(value = EnumType.STRING)
        @Column(name = "offer_type")
        var offerType: OfferType = OfferType.Custom,

        @Enumerated(value = EnumType.STRING)
        var qualifier: Qualifier = Qualifier.Custom,

        @Enumerated(value = EnumType.STRING)
        @Column(name = "award_type")
        var awardType: AwardType = AwardType.Custom,

        @Column(name = "display_priority")
        var displayPriority: Int = 0,

        @Column(name = "duplicated_from")
        var duplicatedFrom: UUID? = null,

        @Column(name = "partner_name")
        var partnerName: String = "",

        @Column(name = "issuance_code")
        var issuanceCode: String? = null,

        @Column(name = "offer_content", columnDefinition = "TEXT")
        var offerContent: String = "",

        @Column(name = "award_short_en")
        var awardShortEN: String = "",

        @Column(name = "award_short_fr")
        var awardShortFR: String = "",

        @Column(name = "qualifier_short_en")
        var qualifierShortEN: String = "",

        @Column(name = "qualifier_short_fr")
        var qualifierShortFR: String = "",

        @Column(name = "legal_text_en", columnDefinition = "TEXT")
        var legalTextEN: String = "",

        @Column(name = "legal_text_fr", columnDefinition = "TEXT")
        var legalTextFR: String = "",

        @Column(name = "image_en", columnDefinition = "TEXT")
        var imageEN: String = "",

        @Column(name = "image_fr", columnDefinition = "TEXT")
        var imageFR: String = "",

        @Column(name = "description_en")
        var descriptionEN: String? = null,

        @Column(name = "description_fr")
        var descriptionFR: String? = null,

        @Column(name = "cashier_instruction_en")
        var cashierInstructionEN: String? = null,

        @Column(name = "cashier_instruction_fr")
        var cashierInstructionFR: String? = null,

        @Column(name = "has_custom_legal")
        var hasCustomLegal: Boolean = false,

        @Column(name = "content_version")
        var contentVersion: Int = 1,

        @Column(name = "mass_offer", nullable = false, columnDefinition = "Boolean default True")
        var massOffer: Boolean = true,

        @Column(name = "active", nullable = false, columnDefinition = "Boolean default True")
        var active: Boolean = true,

        @Column(name = "offer_category_1", nullable = true)
        var offerCategory1: String? = null,

        @Column(name = "offer_category_2", nullable = true)
        var offerCategory2: String? = null,

        @Column(name = "offer_category_3", nullable = true)
        var offerCategory3: String? = null,

        @Column(name = "product_name", nullable = true)
        var productName: String? = null,

        @Column(name = "product_brand", nullable = true)
        var productBrand: String? = null,

        @Column(name = "campaign_code", nullable = true)
        var campaignCode: String? = null,

        @Enumerated(value = EnumType.STRING)
        @Column(name = "program_type")
        var programType: ProgramType = ProgramType.traditionalcore,

        @Column(name = "cta_url_en", nullable = true)
        var ctaUrlEN: String? = null,

        @Column(name = "cta_url_fr", nullable = true)
        var ctaUrlFR: String? = null,

        @Column(name = "cta_label_en", nullable = true)
        var ctaLabelEN: String? = null,

        @Column(name = "cta_label_fr", nullable = true)
        var ctaLabelFR: String? = null,

        @Column(name = "sponsor_code", nullable = true)
        var sponsorCode: String? = null,

        @Column(name = "regions")
        @ElementCollection(fetch = FetchType.EAGER)
        @CollectionTable(indexes = [Index(columnList = "offer_id", name = "offers_regions")])
        @Enumerated(value = EnumType.STRING)
        var regions: MutableSet<Region> = mutableSetOf(),

        @Column(name = "availability")
        @ElementCollection(fetch = FetchType.EAGER)
        @CollectionTable(indexes = [Index(columnList = "offer_id", name = "offers_availability")])
        @Enumerated(value = EnumType.STRING)
        var availability: MutableSet<Availability> = mutableSetOf(),

        @Column(name = "program_priority", nullable = false, columnDefinition = "Int default 0")
        var programPriority: Int = 0,

        @Column(name = "tags")
        @ElementCollection(fetch = FetchType.EAGER)
        @CollectionTable(indexes = [Index(columnList = "offer_id", name = "offers_tags")])
        var tags: Set<String> = HashSet(),

        @Column(name = "event_based_offer", nullable = false, columnDefinition = "Boolean default False")
        var eventBasedOffer: Boolean = false,

        @Column(name = "eligibility_duration",nullable=true)
        var eligibilityDuration: Int? = null,

        @Column(name = "first_qualification_date", nullable = true)
        @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
        var firstQualificationDate: LocalDateTime? = null,

        @Column(name = "last_qualification_date", nullable = true)
        @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
        var lastQualificationDate: LocalDateTime? = null,

        @Column(name = "eligibility_duration_unit", nullable = true)
        @Enumerated(value = EnumType.STRING)
        var eligibilityDurationUnit: EligibilityDurationUnit? = null,

        @Column(name = "card_type", nullable = true)
        private var _cardType: String? = null,

        @Column(name = "retailer_group_id", nullable = true)
        private var _retailerGroupId: String? = null,

        @Column(name = "usage_limit", nullable = true)
        var usageLimit: Int? = null
) {
    var retailerGroupId: List<UUID>?
        get() = _retailerGroupId?.takeIf { it.isNotBlank() }
                ?.split(",")?.mapNotNull { runCatching { UUID.fromString(it) }.getOrNull() }
        set(value) {
            _retailerGroupId = value?.joinToString(",") { it.toString() }
         }

    var cardType: List<CardType>
        get() = _cardType?.takeIf { it.isNotBlank() }?.split(",")?.map { CardType.valueOf(it) } ?: emptyList()
        set(value) {
            _cardType = value.joinToString(",") { it.value }
        }

    @OneToMany(cascade = [CascadeType.ALL], mappedBy = "offer", orphanRemoval = true)
    @LazyCollection(LazyCollectionOption.FALSE)
    var mechanisms: List<Mechanism> = mutableListOf()

    @OneToMany(cascade = [CascadeType.ALL], mappedBy = "offer", orphanRemoval = true)
    @LazyCollection(LazyCollectionOption.FALSE)
    var integrations: List<Integration> = mutableListOf()

    @OneToMany(cascade = [CascadeType.ALL], mappedBy = "offer", orphanRemoval = true)
    @OrderBy(clause = "ID ASC")
    @LazyCollection(LazyCollectionOption.FALSE)
    var tiers: List<Tier> = mutableListOf()

    @ManyToOne(cascade = [CascadeType.PERSIST], fetch = FetchType.LAZY)
    @JsonIgnore
    @JoinTable(
            name = "batchjob_offers",
            joinColumns = [JoinColumn(
                    name = "offer_id", referencedColumnName = "id",
                    insertable = false, updatable = false)],
            inverseJoinColumns = [JoinColumn(
                    name = "batch_id", referencedColumnName = "id",
                    insertable = false, updatable = false)])
    val batch: BatchJob? = null
}
