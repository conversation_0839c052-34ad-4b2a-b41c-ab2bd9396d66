@file:Suppress(
        "SerialVersionUIDInSerializableClass"
)

package com.loyalty.offermanagement.entities

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonInclude
import com.loyalty.offermanagement.models.LocalizedString
import com.loyalty.offermanagement.models.v3.OfferLimitation
import java.io.Serializable

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
data class OfferContent(
        var detailsId: String? = null,

        var contentfulId: String? = null,

        var partnerBaseEarnRate: Int? = null,

        var baseCashRedemption: Int = defaultBaseCashRedemption,

        var offerLimitation: OfferLimitation = OfferLimitation.NoLimit,

        var offerLimitationText: LocalizedString? = null,

        var includedLocations: List<LocalizedString> = emptyList(),

        var excludedLocations: List<LocalizedString> = emptyList(),

        var includedBanners: List<LocalizedString> = emptyList(),

        var excludedBanners: List<LocalizedString> = emptyList(),

        var canBeCombined: Boolean = true,

        var combinationsText: LocalizedString? = null,

        var exclusions: LocalizedString? = null,

        var partnerUrl: LocalizedString? = null,

        var daysToApply: Int? = null,

        var trademarkInfo: LocalizedString? = null,

        var partnerLegalName: LocalizedString? = null
) : Serializable {
    private companion object {
        private const val defaultBaseCashRedemption = 95
    }
}
