package com.loyalty.offermanagement.entities

import com.fasterxml.jackson.annotation.JsonIgnore
import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonInclude
import org.hibernate.annotations.LazyCollection
import org.hibernate.annotations.LazyCollectionOption
import org.hibernate.annotations.OnDelete
import org.hibernate.annotations.OnDeleteAction
import javax.persistence.CascadeType
import javax.persistence.Column
import javax.persistence.Entity
import javax.persistence.FetchType
import javax.persistence.GeneratedValue
import javax.persistence.GenerationType
import javax.persistence.Id
import javax.persistence.JoinColumn
import javax.persistence.ManyToOne
import javax.persistence.OneToMany
import javax.persistence.SequenceGenerator
import javax.persistence.Table

@Entity
@Table(name = "tiers")
@SequenceGenerator(name = "ID", sequenceName = "TIERS_SQ", allocationSize = 1)
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
data class Tier(
        @Column(name = "award_value")
        var awardValue: Float? = null,

        @Column(name = "qualifier_value")
        var qualifierValue: Float? = null,

        @Id
        @GeneratedValue(generator = "ID", strategy = GenerationType.AUTO)
        @Column(name = "ID", nullable = false)
        var id: Long = 0,

        @Column(name = "qualifier_long_en")
        var qualifierLongEN: String = "",

        @Column(name = "qualifier_long_fr")
        var qualifierLongFR: String = "",

        @Column(name = "award_long_en")
        var awardLongEN: String = "",

        @Column(name = "award_long_fr")
        var awardLongFR: String = "",

        @Column(name = "qualifier_frequency", nullable = false, columnDefinition = "Int default 1")
        var qualifierFrequency: Int = 1,

        @OneToMany(cascade = [CascadeType.ALL], mappedBy = "tier", orphanRemoval = true)
        @LazyCollection(LazyCollectionOption.FALSE)
        var content: List<Content> = mutableListOf(),

        @ManyToOne(fetch = FetchType.LAZY, optional = false)
        @OnDelete(action = OnDeleteAction.CASCADE)
        @JsonIgnore
        @JoinColumn(name = "offer_id")
        var offer: Offer? = null
)
