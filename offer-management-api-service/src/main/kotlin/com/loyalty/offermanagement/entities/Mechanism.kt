package com.loyalty.offermanagement.entities

import com.fasterxml.jackson.annotation.JsonIgnore
import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonInclude
import com.loyalty.offermanagement.models.v3.MechanismType
import org.hibernate.annotations.OnDelete
import org.hibernate.annotations.OnDeleteAction
import javax.persistence.Column
import javax.persistence.Entity
import javax.persistence.FetchType
import javax.persistence.GeneratedValue
import javax.persistence.GenerationType
import javax.persistence.Id
import javax.persistence.JoinColumn
import javax.persistence.ManyToOne
import javax.persistence.SequenceGenerator
import javax.persistence.Table
import javax.persistence.EnumType
import javax.persistence.Enumerated

@Entity
@Table(name = "mechanisms")
@SequenceGenerator(name = "ID", sequenceName = "MECHANISM_SQ", allocationSize = 1)
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
data class Mechanism(
        @Column(name = "mechanism_type")
        @Enumerated(value = EnumType.STRING)
        var mechanismType: MechanismType = MechanismType.NoAction,

        @Id
        @GeneratedValue(generator = "ID", strategy = GenerationType.AUTO)
        var id: Long = 0,

        @Column(name = "mechanism_label_en")
        var mechanismLabelEN: String? = null,

        @Column(name = "mechanism_label_fr")
        var mechanismLabelFR: String? = null,

        @Column(name = "mechanism_value_en", columnDefinition = "TEXT")
        var mechanismValueEN: String? = null,

        @Column(name = "mechanism_value_fr", columnDefinition = "TEXT")
        var mechanismValueFR: String? = null,

        @Column(name = "mechanism_title_en")
        var mechanismTitleEN: String? = null,

        @Column(name = "mechanism_title_fr")
        var mechanismTitleFR: String? = null,

        @Column(name = "mechanism_text_en")
        var mechanismTextEN: String? = null,

        @Column(name = "mechanism_text_fr")
        var mechanismTextFR: String? = null,

        @ManyToOne(fetch = FetchType.LAZY, optional = false)
        @OnDelete(action = OnDeleteAction.CASCADE)
        @JsonIgnore
        @JoinColumn(name = "offer_id")
        var offer: Offer? = null
)
