package com.loyalty.offermanagement.entities

import com.fasterxml.jackson.annotation.JsonIgnore
import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonInclude
import org.hibernate.annotations.OnDelete
import org.hibernate.annotations.OnDeleteAction
import javax.persistence.Column
import javax.persistence.CollectionTable
import javax.persistence.Entity
import javax.persistence.ElementCollection
import javax.persistence.FetchType
import javax.persistence.GeneratedValue
import javax.persistence.GenerationType
import javax.persistence.Id
import javax.persistence.Index
import javax.persistence.JoinColumn
import javax.persistence.ManyToOne
import javax.persistence.SequenceGenerator
import javax.persistence.Table

@Entity
@Table(name = "tier_contents")
@SequenceGenerator(name = "ID", sequenceName = "CONTENT_SQ", allocationSize = 1)
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
data class Content(
        @Id
        @GeneratedValue(strategy = GenerationType.AUTO)
        @Column(name = "ID", nullable = false)
        var id: Long = 0,

        // NOTE: to avoid data migration we are using the same old fields of content for labels
        @Column(name = "content_en")
        var labelEN: String = "",

        @Column(name = "content_fr")
        var labelFR: String = "",

        @Column(name = "product_sku")
        var productSKU: String? = null,

        @Column(name = "skus")
        @ElementCollection(fetch = FetchType.EAGER)
        @CollectionTable(indexes = [Index(columnList = "content_id", name = "contents_skus")])
        var skus: List<String> = mutableListOf(),

        @Column(name = "upc")
        var upc: String? = null,

        @Column(name = "master_product", nullable = false, columnDefinition = "Boolean default False")
        var masterProduct: Boolean = false
) {
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @OnDelete(action = OnDeleteAction.CASCADE)
    @JsonIgnore
    @JoinColumn(name = "tier_id")
    var tier: Tier? = null
}
