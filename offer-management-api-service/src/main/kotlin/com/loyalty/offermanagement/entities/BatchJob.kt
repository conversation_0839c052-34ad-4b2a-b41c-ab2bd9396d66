package com.loyalty.offermanagement.entities

import com.fasterxml.jackson.annotation.JsonIgnore
import com.fasterxml.jackson.annotation.JsonInclude
import org.springframework.format.annotation.DateTimeFormat
import java.time.Instant
import java.util.UUID
import javax.persistence.CascadeType
import javax.persistence.Column
import javax.persistence.Entity
import javax.persistence.FetchType
import javax.persistence.Id
import javax.persistence.JoinColumn
import javax.persistence.JoinTable
import javax.persistence.OneToMany
import javax.persistence.Table

@Entity
@Table(name = "batch_jobs")
@JsonInclude(JsonInclude.Include.NON_NULL)
data class BatchJob(
        @Id
        @Column(columnDefinition = "uuid", updatable = false)
        var id: UUID = UUID.randomUUID(),

        @Column(name = "batch_name")
        var batchName: String = "",

        @Column(name = "created_at")
        @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
        var createdAt: Instant = Instant.now(),

        @Column(name = "created_by")
        var createdBy: String = "",

        @Column(name = "updatedAt")
        @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
        var updatedAt: Instant? = null,

        @Column(name = "updated_by")
        var updatedBy: String? = null,

        @OneToMany(cascade = [CascadeType.ALL], fetch = FetchType.LAZY)
        @JsonIgnore // don't serialize in your JSON the children entities
        @JoinTable(
                name = "batchjob_offers",
                joinColumns = [JoinColumn(name = "batch_id", referencedColumnName = "id")],
                inverseJoinColumns = [JoinColumn(name = "offer_id", referencedColumnName = "id")]
        )
        var offers: List<Offer> = mutableListOf(),

        var partnerName: String = "",

        var partnerId: UUID? = null,

        var totalOffers: Int = 0
)
