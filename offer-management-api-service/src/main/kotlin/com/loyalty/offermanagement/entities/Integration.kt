package com.loyalty.offermanagement.entities

import com.fasterxml.jackson.annotation.JsonIgnore
import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonInclude
import com.loyalty.nova.common.integrations.agility.DefaultAgilityIDGenerator
import com.loyalty.nova.common.integrations.agility.generateAgilityOfferCode
import com.loyalty.offermanagement.models.v3.ExternalSystem
import org.hibernate.annotations.OnDelete
import org.hibernate.annotations.OnDeleteAction
import javax.persistence.Column
import javax.persistence.Entity
import javax.persistence.EnumType
import javax.persistence.Enumerated
import javax.persistence.FetchType
import javax.persistence.GeneratedValue
import javax.persistence.GenerationType
import javax.persistence.Id
import javax.persistence.JoinColumn
import javax.persistence.ManyToOne
import javax.persistence.SequenceGenerator
import javax.persistence.Table

@Entity
@Table(name = "integrations")
@SequenceGenerator(name = "ID", sequenceName = "INTEGRATION_SQ", allocationSize = 1)
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
data class Integration(
        @Enumerated(value = EnumType.STRING)
        @Column(name = "system_name")
        var systemName: ExternalSystem = ExternalSystem.Agility,

        @Column(name = "system_code")
        var systemCode: String = DefaultAgilityIDGenerator().generateAgilityOfferCode(),

        @Id
        @GeneratedValue(generator = "ID", strategy = GenerationType.AUTO)
        var id: Long = 0,

        @ManyToOne(fetch = FetchType.LAZY, optional = false)
        @OnDelete(action = OnDeleteAction.CASCADE)
        @JsonIgnore
        @JoinColumn(name = "offer_id")
        var offer: Offer? = null
)
