@file:Suppress(
        "LongMethod"
)

package com.loyalty.offermanagement.novabus

import com.amazonaws.AmazonClientException
import com.amazonaws.AmazonServiceException
import com.amazonaws.services.dynamodbv2.AmazonDynamoDB
import com.amazonaws.services.dynamodbv2.document.DynamoDB
import com.amazonaws.services.dynamodbv2.document.Item
import com.fasterxml.jackson.databind.ObjectMapper
import com.loyalty.nova.common.events.definitions.Event
import com.loyalty.nova.common.events.definitions.data.EventData
import com.loyalty.nova.common.events.definitions.meta.v1.EventMeta
import com.loyalty.nova.common.logging.jsonError
import com.loyalty.offermanagement.utils.LogEventEnum
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

@Service
class DynamoServiceImpl {
    @Autowired
    private lateinit var objectMapper: ObjectMapper

    @Autowired
    private lateinit var client: AmazonDynamoDB

    @Autowired
    private var logger: Logger = LoggerFactory.getLogger(this::class.java)

    fun save(obj: Event<out EventData, out EventMeta>, tableName: String) {
        val table = DynamoDB(client).getTable(tableName)
        try {
            this.objectMapper.writeValueAsString(obj)
                    .let(Item::fromJSON)
                    .let(table::putItem)
        } catch (ase: AmazonServiceException) {
            logger.jsonError(
                    desc = LogEventEnum.StreamFailure.value,
                    value = object {
                        val message = ase.toString()
                        val tableName = tableName
                    }
            )
        } catch (ase: AmazonClientException) {
            logger.jsonError(
                    desc = LogEventEnum.StreamFailure.value,
                    value = object {
                        val message = "Internal error occurred communicating with DynamoDB, ${ase.message}"
                    }
            )
        }
    }
}
