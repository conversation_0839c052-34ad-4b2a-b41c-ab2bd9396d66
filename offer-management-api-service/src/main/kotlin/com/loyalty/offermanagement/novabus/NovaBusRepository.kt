@file:Suppress(
        "UnusedPrivateMember",
        "TooGenericExceptionCaught"
)

package com.loyalty.offermanagement.novabus

import com.fasterxml.jackson.databind.ObjectMapper
import com.loyalty.nova.common.events.definitions.Event
import com.loyalty.nova.common.events.definitions.data.EventData
import com.loyalty.nova.common.events.definitions.data.v3.OfferPublishedEventData
import com.loyalty.nova.common.events.definitions.meta.v1.EventMeta
import com.loyalty.nova.common.logging.jsonInfo
import com.loyalty.offermanagement.FeatureToggleConfig
import com.loyalty.offermanagement.SpringConfig
import com.loyalty.offermanagement.models.v3.inout.OfferIO
import com.loyalty.offermanagement.services.OfferTransformService
import com.loyalty.offermanagement.utils.LogEventEnum
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component
import java.time.Instant
import java.util.UUID

@Component
class NovaBusRepository {
    @Autowired
    private lateinit var dynamoDBService: DynamoServiceImpl

    @Autowired
    private lateinit var springConfig: SpringConfig

    @Autowired
    private lateinit var featureToggleConfig: FeatureToggleConfig

    @Autowired
    private lateinit var offerTransformService: OfferTransformService

    @Autowired
    private var logger: Logger = LoggerFactory.getLogger(this::class.java)

    @Autowired
    private lateinit var mapper: ObjectMapper

    fun publishOffer(offer: OfferIO, correlationId: String) {
        if (featureToggleConfig.featureToggleStreamEventV3.toBoolean()) {
            val eventData = offerTransformService.transformOfferV3(offer)
            val event = buildEvent(eventData, correlationId)
            dynamoDBService.save(event, springConfig.novaBusTableName)
            logger.jsonInfo(
                    desc = LogEventEnum.Stream.value,
                    value = event
            )
        }
    }

    fun buildEvent(
            eventData: OfferPublishedEventData,
            correlationId: String
    ): Event<EventData, EventMeta> {
        return Event(
                id = UUID.randomUUID(),
                data = eventData,
                meta = EventMeta(
                        correlationId = correlationId,
                        partitionKey = eventData.id.toString(),
                        originClient = "OMNIA-OFFER-STREAM",
                        timestamp = Instant.now()
                )
        )
    }
}
