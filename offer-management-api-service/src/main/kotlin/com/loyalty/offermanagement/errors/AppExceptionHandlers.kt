package com.loyalty.offermanagement.errors

import org.slf4j.Logger
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.lang.Nullable
import org.springframework.web.bind.annotation.ControllerAdvice
import org.springframework.web.bind.annotation.ExceptionHandler
import org.springframework.web.context.request.WebRequest
import org.springframework.web.servlet.mvc.method.annotation.ResponseEntityExceptionHandler
import java.util.Date

@ControllerAdvice
class AppExceptionHandlers : ResponseEntityExceptionHandler() {
    @Autowired
    lateinit var log: Logger

    @ExceptionHandler(BadRequestException::class)
    fun handleUnAuthorisedException(ex: Exception): ResponseEntity<ErrorResponse> {
        val status = HttpStatus.BAD_REQUEST
        val errorResponse = ErrorResponse(Date(), status.value(),
                "Operation not possible",
                ex.message)
        log.error(ex.message, ex)
        return ResponseEntity(errorResponse, status)
    }

    @ExceptionHandler(BadQueryException::class)
    fun handleBadQueryException(ex: Exception): ResponseEntity<ErrorResponse> {
        val status = HttpStatus.INTERNAL_SERVER_ERROR
        val errorResponse = ErrorResponse(Date(), status.value(),
                "Internal Server Error",
                "Please contact administrator")
        log.error(ex.message, ex)
        return ResponseEntity(errorResponse, status)
    }

    @ExceptionHandler(IllegalArgumentException::class)
    fun handleBadRequestException(ex: Exception): ResponseEntity<ErrorResponse> {
        val status = HttpStatus.BAD_REQUEST
        val errorResponse = ErrorResponse(Date(), status.value(),
                "Bad Request",
                ex.message)
        log.error(ex.message, ex)
        return ResponseEntity(errorResponse, status)
    }

    @ExceptionHandler(GatewayTimeoutException::class)
    fun handleGatewayTimeoutException(ex: Exception): ResponseEntity<ErrorResponse> {
        val status = HttpStatus.GATEWAY_TIMEOUT
        val errorResponse = ErrorResponse(Date(), status.value(),
                "Gateway Timeout",
                ex.message)
        log.error(ex.message, ex)
        return ResponseEntity(errorResponse, status)
    }

    @ExceptionHandler(OfferNotFoundException::class)
    fun handleOfferNotFoundException(ex: Exception): ResponseEntity<ErrorResponse> {
        val status = HttpStatus.NOT_FOUND
        val errorResponse = ErrorResponse(Date(), status.value(),
                "Offer Not Found",
                ex.message)
        log.error(ex.message, ex)
        return ResponseEntity(errorResponse, status)
    }

    @ExceptionHandler(BulkJobNotFoundException::class)
    fun handleBulkJobNotFoundException(ex: Exception): ResponseEntity<ErrorResponse> {
        val status = HttpStatus.NOT_FOUND
        val errorResponse = ErrorResponse(Date(), status.value(),
                "Bulk Job Not Found",
                ex.message)
        log.error(ex.message, ex)
        return ResponseEntity(errorResponse, status)
    }

    @ExceptionHandler(OfferNotFoundInBulkJobException::class)
    fun handleOfferNotFoundInBulkJobException(ex: Exception): ResponseEntity<ErrorResponse> {
        val status = HttpStatus.NOT_FOUND
        val errorResponse = ErrorResponse(Date(), status.value(),
                "Offer Not Found in Bulk Job",
                ex.message)
        log.error(ex.message, ex)
        return ResponseEntity(errorResponse, status)
    }

    override fun handleExceptionInternal(
            ex: Exception, @Nullable body: Any?, headers: HttpHeaders, status: HttpStatus, request: WebRequest): ResponseEntity<Any> {
        val errorResponse = ErrorResponse(Date(), status.value(),
                "Operation Failed",
                ex.message)
        log.error(ex.message, ex)
        return super.handleExceptionInternal(ex, errorResponse, headers, status, request)
    }
}
