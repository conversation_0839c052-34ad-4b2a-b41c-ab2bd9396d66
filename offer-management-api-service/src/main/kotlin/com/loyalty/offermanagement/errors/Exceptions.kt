package com.loyalty.offermanagement.errors

class OfferNotFoundException(message: String) : RuntimeException(message)

class BulkJobNotFoundException(message: String) : RuntimeException(message)

class OfferNotFoundInBulkJobException(offerId: String) : RuntimeException("Offer With Id $offerId Not Found in Bulk Job. Please check all Offer Ids in your Bulk Job")

class OfferNotInDeletableStateException(offerId: String) : java.lang.RuntimeException("Offer with ID $offerId is not in the Draft state and cannot be deleted")

class OfferCannotBeDisabledException(offerId: String) : java.lang.RuntimeException("Offer with ID $offerId cannot be Disabled due to being in the wrong state or already inactive")

class OfferCannotBeEnabledException(offerId: String) : java.lang.RuntimeException("Offer with ID $offerId cannot be Enabled due to being in the wrong state or already active")

class BadRequestException : IllegalArgumentException {
    constructor(message: String, t: Throwable) : super(message, t)
    constructor(message: String) : super(message)
    constructor(t: Throwable) : super(t)
}

class GatewayTimeoutException(message: String) : java.lang.RuntimeException(message)

class BadQueryException(ex: Exception) : java.lang.RuntimeException(ex)

class ProducerJsonMappingException(message: String, t: Throwable) : ProducerException(message, t)

class SecretNotFoundException(message: String) : RuntimeException(message)