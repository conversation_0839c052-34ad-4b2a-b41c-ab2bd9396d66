@file:Suppress(
        "LongMethod"
)

package com.loyalty.offermanagement

import com.loyalty.offermanagement.errors.SecretNotFoundException
import com.loyalty.offermanagement.services.secret.SecretGrabber
import com.loyalty.offermanagement.utils.DataSourceFactory
import org.flywaydb.core.Flyway
import org.springframework.context.annotation.Configuration
import org.springframework.context.annotation.Profile
import java.util.Properties
import javax.sql.DataSource

@Configuration
@Profile(value = ["local", "sole", "dev", "uat", "int", "prod"])
class FlywayConfig(private val config: SpringConfig, private val secretGrabber: SecretGrabber, private val dataSourceFactory: DataSourceFactory) {
    fun migrateFlyway() {
        val properties = Properties()
        properties.setProperty("flyway.placeholders.env", config.environment)
        properties.setProperty("flyway.placeholders.offermanagementdb", config.databaseName)
        properties.setProperty("flyway.placeholders.offermanagementdbschema", "offermanagement")

        secretGrabber.getSecret(config.dbAdminuserSecret)?.also { secret ->
            properties.setProperty("flyway.placeholders.dbadminuser", secret["username"].toString())
            properties.setProperty("flyway.placeholders.dbadminuserpasswd", secret["password"].toString())
        } ?: throw SecretNotFoundException("Secret ${config.dbAdminuserSecret} is not found")

        secretGrabber.getSecret(config.dbAppuserSecret)?.also { secret ->
            properties.setProperty("flyway.placeholders.dbappuser", secret["username"].toString())
            properties.setProperty("flyway.placeholders.dbappuserpasswd", secret["password"].toString())
        } ?: throw SecretNotFoundException("Secret ${config.dbAppuserSecret} is not found")

        secretGrabber.getSecret(config.dbReportinguserSecret)?.also { secret ->
            properties.setProperty("flyway.placeholders.dbreportinguser", secret["username"].toString())
            properties.setProperty("flyway.placeholders.dbreportinguserpasswd", secret["password"].toString())
        } ?: throw SecretNotFoundException("Secret ${config.dbReportinguserSecret} is not found")

        val masterDataSource: DataSource = dataSourceFactory.createDataSource(config.dbMasteruserSecret)
        Flyway.configure().dataSource(masterDataSource)
                .locations("db/master-migration")
                .schemas("offermanagement")
                .table("flyway_master_schema_history")
                .configuration(properties).load().migrate()

        val adminDataSource: DataSource = dataSourceFactory.createDataSource(config.dbAdminuserSecret)
        Flyway.configure().dataSource(adminDataSource)
                .locations("db/admin-migration")
                .schemas("offermanagement")
                .table("flyway_admin_schema_history")
                .configuration(properties).baselineOnMigrate(true).load().migrate()
    }
}
