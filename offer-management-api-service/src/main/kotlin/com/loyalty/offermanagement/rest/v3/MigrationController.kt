@file:Suppress(
        "<PERSON><PERSON><PERSON><PERSON>",
        "Complex<PERSON><PERSON><PERSON>",
        "ReturnCount",
        "TooGenericExceptionCaught",
        "ComplexMethod",
        "ReturnCount",
        "TooGenericExceptionCaught"
)

package com.loyalty.offermanagement.rest.v3

import com.fasterxml.jackson.databind.ObjectMapper
import com.loyalty.nova.common.integrations.agility.DefaultAgilityIDGenerator
import com.loyalty.nova.common.integrations.agility.generateAgilityOfferCode
import com.loyalty.offermanagement.extractors.JsonNodeExtractorBase
import com.loyalty.offermanagement.extractors.extractItems
import com.loyalty.offermanagement.models.v3.ExternalSystem
import com.loyalty.offermanagement.models.v3.inout.IntegrationIO
import com.loyalty.offermanagement.models.v3.inout.OfferIO
import com.loyalty.offermanagement.repository.OfferRepository
import com.loyalty.offermanagement.services.OfferTransformService
import io.swagger.annotations.Api
import io.swagger.annotations.ApiOperation
import org.slf4j.Logger
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.util.DigestUtils
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import org.springframework.web.multipart.MultipartFile
import java.security.MessageDigest

@RestController("migrationController")
@Api(value = "migration", description = "Migration API actions")
class MigrationController(
        private val offerRepository: OfferRepository,
        private val jsonNodeExtractor: JsonNodeExtractorBase<OfferIO>,
        private val offerTransformService: OfferTransformService,
        private val objectMapper: ObjectMapper
) {
    @Autowired
    private lateinit var logger: Logger

    @ApiOperation(value = "Performs extraction and importation of a DynamoDB V1 into persistence", response = Any::class)
    @PostMapping("/v3/migration/migrate")
    fun extractAndMigrate(
            @RequestParam("file") file: MultipartFile
    ): ResponseEntity<Any> {
        this.logger.info("POST /v3/migration/migrate")

        try {
            val results = this.jsonNodeExtractor.extractItems(file.inputStream, this.objectMapper)
                    .mapIndexed { index, result -> index to result }
                    .associate { (index, result) ->
                        return@associate index to when {
                            result.isSuccess -> runCatching {
                                val offerIO = result.getOrNull() ?: TODO()

                                // add the integration fields
                                offerIO.integrations = listOf(IntegrationIO(
                                        systemName = ExternalSystem.Agility,
                                        systemCode = DefaultAgilityIDGenerator().generateAgilityOfferCode())
                                )

                                val offerRDS = this.offerTransformService.transform(offerIO)
                                return@runCatching this.offerRepository.save(offerRDS).id.also { id -> this.logger.info("offerRepository.save success, with id=$id") }
                            }
                            result.isFailure -> run {
                                val exception = result.exceptionOrNull()
                                this.logger.error("Something went wrong because: $exception", exception)
                                return@run result
                            }
                            else -> TODO()
                        }
                    }

            val messages = results.map { (index, result) ->
                when {
                    result.isFailure -> {
                        val exception = result.exceptionOrNull()
                        val message = "Migration where index=$index failed because: $exception"
                        this.logger.error(message, exception)
                        return@map message
                    }
                    result.isSuccess -> "Migration where index=$index succeeded with id=${result.getOrNull()}"
                    else -> "Migration where index=$index did something weird"
                }
            }

            return ResponseEntity.ok(object {
                val failureCount = results.values.count(Result<*>::isFailure)

                val successCount = results.values.count(Result<*>::isSuccess)

                val messages = messages
            })
        } catch (throwable: Throwable) {
            this.logger.error("Migration migrate process failed", throwable)

            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(object {
                        val exception = throwable
                    })
        }
    }


    @ApiOperation(value = "Performs extraction and dump of a DynamoDB V1 to OfferIO instances", response = Any::class)
    @PostMapping("/v3/migration/dump")
    fun extractAndDump(
            @RequestParam params: Map<String, String>,
            @RequestParam("file") file: MultipartFile
    ): ResponseEntity<Any> {
        this.logger.info("POST /v3/migration/dump")

        try {
            val offerIOs = this.jsonNodeExtractor.extractItems(file.inputStream, this.objectMapper)
                    .also { results ->
                        results.filter(Result<*>::isFailure)
                                .mapNotNull(Result<*>::exceptionOrNull)
                                .forEach { exception ->
                                    this.logger.error("Someting went wrong because: $exception", exception)
                                }
                    }
                    .mapNotNull { result -> result.getOrNull() }
                    .map { offerIO ->
                        return@map offerIO.copy(
                                createdBy = this.hashToEmail(offerIO.createdBy) ?: String(),
                                updatedBy = this.hashToEmail(offerIO.updatedBy),
                                publishedBy = this.hashToEmail(offerIO.publishedBy)
                        )
                    }

            val filename = params.get("filename") ?: this.objectMapper.writeValueAsString(offerIOs)
                    .toByteArray()
                    .let(DigestUtils::md5DigestAsHex)
                    .let { hash -> "$hash.json" }

            return ResponseEntity.status(HttpStatus.OK)
                    .header("Content-Disposition", """attachment; filename="$filename"""")
                    .body(offerIOs)
        } catch (throwable: Throwable) {
            this.logger.error("Migration dump process failed", throwable)

            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(object {
                        val exception = throwable
                    })
        }
    }

    private fun hashToEmail(input: String?, host: String = "example.com"): String? {
        if (input == null) {
            return null
        }

        val shaMessageDigest = MessageDigest.getInstance("SHA-512")
        val md5MessageDigest = MessageDigest.getInstance("MD5")

        return input.toByteArray()
                .let(shaMessageDigest::digest)
                .let(md5MessageDigest::digest)
                .joinToString(
                        separator = String(),
                        transform = { byte -> String.format("%02x", byte) }
                )
                .let { hash -> "$hash@$host" }
    }
}
