@file:Suppress(
        "TooManyFunctions",
        "WildcardImport",
        "MayBeConst"
)

package com.loyalty.offermanagement.rest.v3

import com.loyalty.nova.common.logging.jsonError
import com.loyalty.offermanagement.entities.Offer
import com.loyalty.offermanagement.errors.BadRequestException
import com.loyalty.offermanagement.models.OfferCriteria
import com.loyalty.offermanagement.models.PageCriteria
import com.loyalty.offermanagement.models.v3.inout.BatchJobIO
import com.loyalty.offermanagement.models.v3.inout.BulkPostOfferIO
import com.loyalty.offermanagement.models.v3.inout.BulkPutOfferIO
import com.loyalty.offermanagement.models.v3.inout.IdIO
import com.loyalty.offermanagement.models.v3.inout.OfferIO
import com.loyalty.offermanagement.models.v3.inout.PostOfferIO
import com.loyalty.offermanagement.models.v3.inout.PutOfferIO
import com.loyalty.offermanagement.models.v3.inout.RepublishResponseIO
import com.loyalty.offermanagement.services.v3.BatchService
import com.loyalty.offermanagement.services.v3.OfferService
import com.loyalty.offermanagement.utils.LogEventEnum
import com.loyalty.offermanagement.utils.OfferCounts
import com.loyalty.offermanagement.utils.SearchResults
import io.swagger.annotations.Api
import io.swagger.annotations.ApiOperation
import org.slf4j.Logger
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PatchMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.PutMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestHeader
import org.springframework.web.bind.annotation.ResponseStatus
import org.springframework.web.bind.annotation.RestController
import java.util.UUID
import javax.validation.Valid

@RestController("controller-v3")
@Api(value = "offers", description = "Offer API actions")
class OfferController(private val offerService: OfferService, private val batchService: BatchService) {
    @Autowired
    private lateinit var logger: Logger

    @ApiOperation(value = "Search a product with an ID", response = OfferIO::class)
    @GetMapping("/v3/offers/{id}")
    fun getOfferById(@PathVariable id: String,
                     @RequestHeader headers: Map<String, String>): ResponseEntity<OfferIO>? {
        logger.info("GET /v3/offers/$id")
        return offerService.getOffer(id, headers)
    }

    @GetMapping("/v3/offers")
    fun getOffers(@Valid offerCriteria: OfferCriteria, @Valid pageCriteria: PageCriteria,
                  @RequestHeader headers: Map<String, String>): SearchResults<OfferIO> {
        logger.info("GET /v3/offers")

        if (!headers.containsKey("x-user-email")) {
            logger.jsonError(
                    desc = LogEventEnum.Search.value,
                    value = object {
                        val message = "Ignored, header is missing"
                        val offerCriteria = offerCriteria
                    }
            )
            throw BadRequestException("Missing authentication")
        }
        return offerService.getOffers(offerCriteria, pageCriteria)
    }

    @GetMapping("/v3/offers/counts")
    fun getOfferCounts(@Valid offerCriteria: OfferCriteria,
                       @RequestHeader headers: Map<String, String>): OfferCounts {
        logger.info("GET /v3/offers/counts")

        if (!headers.containsKey("x-user-email")) {
            logger.jsonError(
                    desc = LogEventEnum.GetOffersCounts.value,
                    value = object {
                        val message = "Ignored, header is missing"
                        val offerCriteria = offerCriteria
                    }
            )
            throw BadRequestException("Missing authentication")
        }

        return offerService.getOfferCounts(offerCriteria)
    }

    @ApiOperation(value = "Creates an Offer and stores in DB", response = OfferIO::class)
    @PostMapping("/v3/offers")
    fun createOffer(@RequestBody postOffer: PostOfferIO,
                    @RequestHeader headers: Map<String, String>): ResponseEntity<OfferIO>? {
        logger.info("POST /v3/offers")

        return offerService.postOffer(postOffer, headers)
    }

    @ApiOperation(value = "Deletes a list of draft offers from the DB")
    @PostMapping("/v3/offers/delete-multiple")
    fun deleteOffers(@RequestBody offersToDelete: List<UUID>,
                     @RequestHeader headers: Map<String, String>): ResponseEntity<List<UUID>>? {
        logger.info("POST /v3/offers/delete-multiple")

        return offerService.deleteOffers(offersToDelete, headers)
    }

    @ApiOperation(value = "Disables a list of draft offers from the DB")
    @PostMapping("/v3/offers/disable")
    fun disableOffer(@RequestBody offerToDisable: IdIO,
                     @RequestHeader headers: Map<String, String>): ResponseEntity<OfferIO> {
        logger.info("POST /v3/offers/disable")
        return offerService.disableOffer(offerToDisable, headers)
    }

    @ApiOperation(value = "Enables a list of draft offers from the DB")
    @PostMapping("/v3/offers/enable")
    fun enableOffer(@RequestBody offerToEnable: IdIO,
                    @RequestHeader headers: Map<String, String>): ResponseEntity<OfferIO> {
        logger.info("POST /v3/offers/enable")
        return offerService.enableOffer(offerToEnable, headers)
    }

    @ApiOperation(value = "Creates Offers and stores in DB", response = Offer::class)
    @PostMapping("/v3/offers/bulk")
    fun createOffers(@Valid @RequestBody body: BulkPostOfferIO,
                     @RequestHeader headers: Map<String, String>): ResponseEntity<IdIO>? {
        logger.info("POST /v3/offers/bulk with offers size=${body.offers.size}")
        return offerService.postOffers(body, headers)
    }

    @ApiOperation(value = "Updates an Offer stored in DB", response = OfferIO::class)
    @PutMapping("/v3/offers/{id}")
    fun updateOffer(@PathVariable id: String, @RequestBody offer: PutOfferIO,
                    @RequestHeader headers: Map<String, String>): ResponseEntity<OfferIO>? {
        logger.info("PUT /v3/offers/$id")
        return offerService.putOffer(offer, id, headers)
    }

    @ApiOperation(value = "Updates a Bulk Job including bulk name and multiple offers stored in DB", response = Offer::class)
    @PatchMapping("/v3/offers/bulk/{bulkId}")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    fun updateBulkJob(@PathVariable bulkId: String, @Valid @RequestBody bulkJob: BulkPutOfferIO,
                      @RequestHeader headers: Map<String, String>) {
        logger.info("PATCH /v3/offers/bulk/$bulkId with offers size=${bulkJob.offers.size}")
        return offerService.patchOffersForBulk(bulkJob, bulkId, headers)
    }

    @ApiOperation(value = "Publish Offer", response = OfferIO::class)
    @PostMapping("/v3/offers/publish")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    fun publishOffer(@RequestBody body: IdIO,
                     @RequestHeader headers: Map<String, String>) {
        logger.info("POST /v3/offers/publish")
        return offerService.publishOffer(body, headers)
    }

    @ApiOperation(value = "Republish Offers", response = OfferIO::class)
    @PostMapping("/v3/offers/republish-offers")
    fun republishOffers(): RepublishResponseIO {
        logger.info("POST /v3/offers/republish-offers")
        return offerService.republishOffers()
    }

    @GetMapping("/v3/offers/jobs")
    fun getJobs(@RequestHeader headers: Map<String, String>): ResponseEntity<List<BatchJobIO>> {
        logger.info("GET /v3/offers/jobs")
        return batchService.getJobs(headers)
    }
}
