package com.loyalty.offermanagement.services.partner

import com.loyalty.offermanagement.SpringConfig
import com.loyalty.offermanagement.models.partner.PartnerListResponse
import com.loyalty.offermanagement.models.partner.PartnerResponse
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.stereotype.Service
import org.springframework.web.client.HttpClientErrorException
import org.springframework.web.client.RestTemplate
import org.springframework.web.client.getForEntity

@Service
class PartnerService(private val config: SpringConfig) {
    @Autowired
    private lateinit var restTemplate: RestTemplate

    @Throws(PartnerServiceException::class)
    fun getPartnerById(partnerId: String): PartnerResponse? {
        try {
            val responseEntity: ResponseEntity<PartnerListResponse> =
                restTemplate.getForEntity("${config.partnersEndpoint}/$PATH/$partnerId", PartnerListResponse::class)

            return when {
                responseEntity.statusCode == HttpStatus.OK -> responseEntity.body?.results?.firstOrNull()
                else -> throw PartnerServiceException("failure on finding partner $partnerId")
            }
        } catch (e: HttpClientErrorException) {
            throw PartnerServiceException(
                message = "PartnerService - Exception body: ${e.responseBodyAsString} and parameter: $partnerId",
                cause = e)
        }
    }

    companion object {
        private const val PATH = "v1/partners"
    }
}