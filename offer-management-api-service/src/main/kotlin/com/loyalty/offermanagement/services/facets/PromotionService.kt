package com.loyalty.offermanagement.services.facets

import com.loyalty.offermanagement.SpringConfig
import com.loyalty.offermanagement.models.LocalizedString
import com.loyalty.offermanagement.models.facets.OfferPromotion
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.stereotype.Service
import org.springframework.web.client.HttpClientErrorException
import org.springframework.web.client.RestTemplate
import org.springframework.web.client.getForEntity

@Service
class PromotionService(val config: SpringConfig) {
    @Autowired
    private lateinit var restTemplate: RestTemplate

    @Throws(OfferPromotionServiceException::class)
    fun getPromotionInfo(item: String): LocalizedString? {
        try {
            var responseEntity: ResponseEntity<OfferPromotion> =
                    restTemplate.getForEntity("${config.offerPromotionsEndpoint}/$path/$item", OfferPromotion::class)

            return when {
                responseEntity.statusCode == HttpStatus.OK -> responseEntity.body?.translations
                else -> throw OfferPromotionServiceException("failure on finding promotion $item")
            }
        } catch (e: HttpClientErrorException) {
            throw OfferPromotionServiceException(
                    message = "PromotionService - Exception body: ${e.responseBodyAsString} and parameter: $item",
                    cause = e)
        }
    }

    companion object {
        private const val path = "v1/promotions"
    }
}
