package com.loyalty.offermanagement.services.secret

import org.springframework.context.annotation.Profile
import org.springframework.stereotype.Service

@Service
@Profile(value = ["local"])
class SecretGrabberStub : SecretGrabber {
    override fun getSecret(secretName: String): Map<*, *>? {
        return when (secretName) {
            "local-offer-management-api-resources-master-user" ->
                mapOf("username" to "postgres", "password" to "docker")
            "local-offer-management-api-resources-admin-user" ->
                mapOf("username" to "dbadminuser", "password" to "passwd1234")
            "local-offer-management-api-resources-app-user" ->
                mapOf("username" to "dbappuser", "password" to "passwd1234")
            "local-offer-management-api-resources-reporting-user" ->
                mapOf("username" to "dbreportinguser", "password" to "passwd1234")
            else -> emptyMap<String, String>()
        }
    }
}
