package com.loyalty.offermanagement.services

import com.loyalty.offermanagement.SpringConfig
import com.loyalty.offermanagement.models.ContentGenerationResponse
import com.loyalty.offermanagement.models.v3.inout.OfferIO
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.ResponseEntity
import org.springframework.stereotype.Service
import org.springframework.web.client.RestTemplate

@Service
class ContentGenerationService(private val config: SpringConfig) {
    @Autowired
    private lateinit var restTemplate: RestTemplate

    fun generate(offer: OfferIO): ResponseEntity<ContentGenerationResponse> =
            restTemplate.postForEntity("${config.cgsEndpoint}/$path", offer, ContentGenerationResponse::class.java)

    companion object {
        private const val path = "v1/generate-content"
    }
}
