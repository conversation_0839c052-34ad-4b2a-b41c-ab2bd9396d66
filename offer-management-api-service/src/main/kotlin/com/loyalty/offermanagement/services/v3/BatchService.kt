@file:Suppress(
        "<PERSON><PERSON><PERSON><PERSON>",
        "MayBeConst",
        "TooGenericExceptionCaught"
)

package com.loyalty.offermanagement.services.v3

import com.loyalty.nova.common.logging.jsonError
import com.loyalty.offermanagement.errors.BadQueryException
import com.loyalty.offermanagement.errors.BadRequestException
import com.loyalty.offermanagement.models.v3.inout.BatchJobIO
import com.loyalty.offermanagement.repository.BatchJobRepository
import com.loyalty.offermanagement.utils.LogEventEnum
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.stereotype.Component

@Component("BatchService-v3")
class BatchService {
    @Autowired
    private lateinit var jobsRepo: BatchJobRepository

    @Autowired
    private var logger: Logger = LoggerFactory.getLogger(this::class.java)

    fun getJobs(headers: Map<String, String>): ResponseEntity<List<BatchJobIO>> {
        if (!headers.containsKey("x-user-email")) {
            logger.jsonError(
                    desc = LogEventEnum.GetBulk.value,
                    value = object {
                        val message = "Get Bulk Jobs failed; ignored, header is missing"
                    }
            )
            throw BadRequestException("Missing authentication")
        }
        try {
            val jobs = jobsRepo.getJobs().map {
                BatchJobIO(
                        id = it.id.toString(),
                        name = it.batchName,
                        partnerId = it.partnerId.toString(),
                        partnerName = it.partnerName,
                        totalOffers = it.totalOffers,
                        createdAt = it.createdAt,
                        createdBy = it.createdBy,
                        updatedAt = it.updatedAt,
                        updatedBy = it.updatedBy
                )
            }

            return ResponseEntity(jobs, HttpStatus.OK)
        } catch (e: RuntimeException) {

            logger.jsonError(
                    desc = LogEventEnum.GetBulk.value,
                    value = object {
                        val message = "Get Bulk Jobs failed; ${e.message}"
                    }
            )

            throw BadQueryException(e)
        }
    }
}
