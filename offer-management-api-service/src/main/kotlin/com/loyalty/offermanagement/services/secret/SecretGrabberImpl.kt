package com.loyalty.offermanagement.services.secret

import com.amazonaws.services.secretsmanager.AWSSecretsManagerClientBuilder
import com.amazonaws.services.secretsmanager.model.GetSecretValueRequest
import com.fasterxml.jackson.databind.ObjectMapper
import com.loyalty.offermanagement.SpringConfig
import org.springframework.context.annotation.Profile
import org.springframework.stereotype.Service

@Service
@Profile(value = ["sole", "dev", "int", "uat", "prod"])
class SecretGrabberImpl(private val config: SpringConfig) : SecretGrabber {
    override fun getSecret(secretName: String): HashMap<*, *>? {
        val region = config.awsRegion
        // Create a Secrets Manager client
        val client = AWSSecretsManagerClientBuilder.standard()
                .withRegion(region)
                .build()

        val getSecretValueRequest = GetSecretValueRequest().withSecretId(secretName)
        val getSecretValueResult = client.getSecretValue(getSecretValueRequest)

        val secretBinaryString = getSecretValueResult.secretString
        val objectMapper = ObjectMapper()

        return objectMapper.readValue(secretBinaryString, HashMap::class.java)
    }
}
