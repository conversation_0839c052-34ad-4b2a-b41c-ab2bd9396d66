@file:Suppress(
        "ComplexMethod",
        "ForbiddenComment",
        "LargeClass",
        "LongMethod",
        "MagicN<PERSON>ber",
        "MaximumLine<PERSON>ength",
        "<PERSON><PERSON>ineLength",
        "MayBeConst",
        "NestedBlockDepth",
        "ThrowsCount",
        "TooGenericExceptionCaught",
        "TooGenericExceptionThrown",
        "TooManyFunctions",
        "UnsafeCallOnNullableType"
)

package com.loyalty.offermanagement.services.v3

import com.amazonaws.services.secretsmanager.model.InternalServiceErrorException
import com.amazonaws.util.StringUtils
import com.fasterxml.jackson.databind.ObjectMapper
import com.loyalty.nova.common.integrations.agility.DefaultAgilityIDGenerator
import com.loyalty.nova.common.integrations.agility.generateAgilityOfferCode
import com.loyalty.nova.common.logging.jsonError
import com.loyalty.nova.common.logging.jsonInfo
import com.loyalty.nova.common.logging.logger
import com.loyalty.offermanagement.FeatureToggleConfig
import com.loyalty.offermanagement.entities.BatchJob
import com.loyalty.offermanagement.entities.Offer
import com.loyalty.offermanagement.entities.OfferContent
import com.loyalty.offermanagement.errors.*
import com.loyalty.offermanagement.models.*
import com.loyalty.offermanagement.models.partner.PartnerResponse
import com.loyalty.offermanagement.models.v3.*
import com.loyalty.offermanagement.models.v3.ExternalSystem.Agility
import com.loyalty.offermanagement.models.v3.inout.BulkPostOfferIO
import com.loyalty.offermanagement.models.v3.inout.BulkPutOfferIO
import com.loyalty.offermanagement.models.v3.inout.IdIO
import com.loyalty.offermanagement.models.v3.inout.IntegrationIO
import com.loyalty.offermanagement.models.v3.inout.OfferIO
import com.loyalty.offermanagement.models.v3.inout.PostOfferIO
import com.loyalty.offermanagement.models.v3.inout.PutOfferIO
import com.loyalty.offermanagement.models.v3.inout.RepublishResponseIO
import com.loyalty.offermanagement.models.v3.inout.MechanismIO
import com.loyalty.offermanagement.novabus.NovaBusRepository
import com.loyalty.offermanagement.repository.BatchJobRepository
import com.loyalty.offermanagement.repository.OfferRepository
import com.loyalty.offermanagement.services.ContentGenerationService
import com.loyalty.offermanagement.services.OfferTransformService
import com.loyalty.offermanagement.services.facets.OfferCategoryService
import com.loyalty.offermanagement.services.facets.PromotionService
import com.loyalty.offermanagement.services.offerv2.OfferV2Service
import com.loyalty.offermanagement.services.kinesis.KinesisEventWriter
import com.loyalty.offermanagement.services.partner.PartnerService
import com.loyalty.offermanagement.utils.*
import com.loyalty.offermanagement.utils.TimeUtils.Companion.getDiffDay
import org.everit.json.schema.ValidationException
import org.json.JSONObject
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Sort
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.stereotype.Component
import java.time.Instant
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.ZoneOffset
import java.time.format.DateTimeFormatter
import java.util.UUID
import java.util.concurrent.CompletableFuture
import javax.transaction.Transactional

private const val INTERNAL_AIRMILES_CALCULATED = "internal-airmiles-calculated"

private const val MAX_OFFER_LIMIT = 1

@Component("offerService-v3")
class OfferService {
    @Autowired
    private lateinit var mapper: ObjectMapper

    @Autowired
    private lateinit var offerRepo: OfferRepository

    @Autowired
    private lateinit var jobRepo: BatchJobRepository

    @Autowired
    private lateinit var novaBusRepository: NovaBusRepository

    @Autowired
    private lateinit var offerTransformService: OfferTransformService

    @Autowired
    private lateinit var contentGenerationService: ContentGenerationService

    @Autowired
    private lateinit var featureToggleConfig: FeatureToggleConfig

    @Autowired
    private lateinit var offerV2Service: OfferV2Service

    @Autowired
    private lateinit var kinesisEventWriter: KinesisEventWriter

    @Autowired
    private lateinit var offerPromotionService: PromotionService

    @Autowired
    private lateinit var offerCategoryServicevice: OfferCategoryService

    @Autowired
    private lateinit var partnerService: PartnerService

    private val postOfferSchema = OfferUtils.getSchema("/schemas/PostOfferFormObject.json")
    private val putOfferSchema = OfferUtils.getSchema("/schemas/PutOfferFormObject.json")
    private val postBulkOfferSchema = OfferUtils.getSchema("/schemas/PostBulkOfferFormObject.json")
    private val putBulkOfferSchema = OfferUtils.getSchema("/schemas/PutBulkOfferFormObject.json")
    private val publishOfferSchema = OfferUtils.getSchema("/schemas/PublishOfferObject.json")

    @Value("\${partner.id.bank.montreal}")
    lateinit var partnetIdBankMontreal: String

    private class DateObjectType {
        var startFrom: LocalDateTime = TimeUtils.getBeginningOfTime()
        var startTill: LocalDateTime = TimeUtils.getEndOfTime()
        var endFrom: LocalDateTime = TimeUtils.getBeginningOfTime()
        var endTill: LocalDateTime = TimeUtils.getEndOfTime()
        var statuses: Set<OfferStatus>? = null
    }

    fun getOffer(id: String, headers: Map<String, String>): ResponseEntity<OfferIO> {
        if (!headers.containsKey("x-user-email")) {
            logger.jsonError(
                    desc = LogEventEnum.GetOffer.value,
                    value = object {
                        val message = "Ignored, header is missing"
                        val id = id
                    }
            )
            throw BadRequestException("Missing authentication")
        }

        val offer: Offer = offerRepo.findById(UUID.fromString(id)).orElseThrow {
            logger.jsonError(
                    desc = LogEventEnum.GetOffer.value,
                    value = object {
                        val message = "Offer not found"
                        val id = id
                    }
            )
            throw OfferNotFoundException(id)
        }

        return ResponseEntity(offerTransformService.transform(offer), HttpStatus.OK)
    }

    fun postOffer(postOffer: PostOfferIO, headers: Map<String, String>): ResponseEntity<OfferIO> {

        if (!headers.containsKey("x-user-email")) {
            logger.jsonError(
                    desc = LogEventEnum.Create.value,
                    value = object {
                        val message = "Ignored, header is missing"
                        val offer = postOffer
                    }
            )
            throw BadRequestException("Missing authentication")
        }

        postOffer.fallback()

        try {
            this.postOfferSchema.validate(JSONObject(mapper.writeValueAsString(postOffer)))
            validateOfferDates(postOffer)
            updateDates(postOffer)
            validateOfferUniqueness(postOffer)
            validateCardType(postOffer)
            validateBMOPreApproval(postOffer)
            validateCtaUrlCtaLabel(postOffer)
            validateAMReceipt(postOffer)
        } catch (exception: ValidationException) {
            logger.jsonError(
                    desc = LogEventEnum.Create.value,
                    value = object {
                        val message = exception.allMessages.toString()
                        val offer = postOffer
                    },
                    throwable = exception
            )
            throw BadRequestException(exception.allMessages.toString())
        }

        val offerIO = mapper.convertValue(postOffer, OfferIO::class.java)
        offerIO.createdBy = headers["x-user-email"].toString()
        offerIO.integrations = listOf(IntegrationIO(
                systemName = Agility,
                systemCode = DefaultAgilityIDGenerator().generateAgilityOfferCode())
        )

        try {
            val cgsResponse = contentGenerationService.generate(offerIO)
            val offerWithCopyStrings = getOfferWithCopyStrings(offerIO, cgsResponse.body)
            offerRepo.save(offerTransformService.transform(offerWithCopyStrings))
            logger.jsonInfo(
                    desc = LogEventEnum.Create.value,
                    value = offerIO
            )
            return ResponseEntity(offerWithCopyStrings, HttpStatus.OK)
        } catch (exception: RuntimeException) {
            logger.jsonError(
                    desc = LogEventEnum.Create.value,
                    value = object {
                        val message = exception.message
                        val offerIO = offerIO
                    },
                    throwable = exception
            )
            throw BadQueryException(exception)
        }
    }

    private fun updateDates(postOffer: PostOfferIO) {
        if(postOffer.eventBasedOffer){
            postOffer.startDate = postOffer.firstQualificationDate
            postOffer.displayDate = postOffer.firstQualificationDate
            val duration = postOffer.eligibilityDuration!!.toLong();
            val formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss'Z'")
            when(postOffer.eligibilityDurationUnit){
                EligibilityDurationUnit.DAYS -> {
                    val lqd = TimeUtils.parseStringToLocalDateTime(postOffer.lastQualificationDate!!).plusDays(duration).minusDays(1).toLocalDate()
                    postOffer.endDate = TimeUtils.getDateEnd(lqd,0).format(formatter)
                }
                EligibilityDurationUnit.HOURS -> postOffer.endDate = TimeUtils.parseStringToLocalDateTime(postOffer.lastQualificationDate!!).plusHours(duration).format(formatter)
                EligibilityDurationUnit.MINUTES-> postOffer.endDate = TimeUtils.parseStringToLocalDateTime(postOffer.lastQualificationDate!!).plusMinutes(duration).format(formatter)
            }
        }
    }

    private fun updateDates(putOffer: PutOfferIO) {
        if(putOffer.eventBasedOffer){
            putOffer.startDate = putOffer.firstQualificationDate
            putOffer.displayDate = putOffer.firstQualificationDate
            val duration = putOffer.eligibilityDuration!!.toLong();
            val formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss'Z'")
            when(putOffer.eligibilityDurationUnit){
                EligibilityDurationUnit.DAYS -> {
                    val lqd = TimeUtils.parseStringToLocalDateTime(putOffer.lastQualificationDate!!).plusDays(duration).minusDays(1).toLocalDate()
                    putOffer.endDate = TimeUtils.getDateEnd(lqd,0).format(formatter)
                }
                EligibilityDurationUnit.HOURS -> putOffer.endDate = TimeUtils.parseStringToLocalDateTime(putOffer.lastQualificationDate!!).plusHours(duration).format(formatter)
                EligibilityDurationUnit.MINUTES-> putOffer.endDate = TimeUtils.parseStringToLocalDateTime(putOffer.lastQualificationDate!!).plusMinutes(duration).format(formatter)
            }
        }
    }

    @Transactional
    fun deleteOffers(offersToDelete: List<UUID>, headers: Map<String, String>): ResponseEntity<List<UUID>> {
        // validate user
        if (!headers.containsKey("x-user-email")) {
            logger.jsonError(
                    desc = LogEventEnum.Delete.value,
                    value = object {
                        val message = "Ignored, header is missing"
                        val offersToDelete = offersToDelete
                    }
            )
            throw BadRequestException("Missing authentication")
        }
        // validate each offer is in draft state
        val storedOffers = offerRepo.findByIdIn(offersToDelete)

        try {
            storedOffers.forEach { if (it.status !== OfferStatus.DRAFT) throw com.loyalty.offermanagement.errors.OfferNotInDeletableStateException(it.id.toString()) else logger.info(mapper.writeValueAsString(it)) }
        } catch (exception: com.loyalty.offermanagement.errors.OfferNotInDeletableStateException) {
            logger.jsonError(
                    desc = LogEventEnum.Delete.value,
                    value = object {
                        val message = "Unable to delete offers due to bad offer state"
                        val storedOffers = storedOffers
                    }
            )
            throw BadRequestException(exception.message.toString(), exception)
        }

        // do delete
        val results = offerRepo.deleteByIdIn(offersToDelete)

        // if the offers that have just been deleted have batch ids, store them
        val batchIds = mutableSetOf<UUID>()
        storedOffers.forEach {
            if (it.batch != null) {
                batchIds.add(it.batch.id)
            }
        }

        if (batchIds.isNotEmpty()) {
            // Find the batch job by id
            val storedBatchJobs = jobRepo.findAllByIdFetchEagerly(batchIds)

            storedBatchJobs.forEach {
                if (it.offers.isEmpty()) {
                    jobRepo.delete(it)
                }
            }
        }
        // return deleted offer IDs
        return ResponseEntity(results.map { it.id }, HttpStatus.OK)
    }

    @Throws(GatewayTimeoutException::class, InternalServiceErrorException::class)
    @Transactional
    fun disableOffer(offerToDisable: IdIO, headers: Map<String, String>): ResponseEntity<OfferIO> {
        // validate user
        if (!headers.containsKey("x-user-email")) {
            logger.jsonError(
                    desc = LogEventEnum.Disable.value,
                    value = object {
                        val message = "Ignored, header is missing"
                        val id = offerToDisable.id
                    }
            )
            throw BadRequestException("Missing authentication")
        }

        // validate each offer is in draft state
        val storedOffer: Offer = offerRepo.findById(UUID.fromString(offerToDisable.id)).orElseThrow {
            logger.jsonError(
                    desc = LogEventEnum.Disable.value,
                    value = object {
                        val message = "Offer to Disable Not Found"
                        val id = offerToDisable.id
                    }
            )
            OfferNotFoundException(offerToDisable.toString())
        }

        if (storedOffer.status === OfferStatus.DRAFT) {
            throw OfferCannotBeDisabledException(storedOffer.id.toString())
        }

        if (storedOffer.active == false) {
            throw BadRequestException("Offer with ID ${storedOffer.id} is already in the inactive state")
        }

        try {
            storedOffer.active = false
            offerRepo.save(storedOffer)
            // Publish offer will fetch the id that was saved to the DB and publish it so that it can be updated downstream
            publishOffer(IdIO(storedOffer.id.toString()), headers)
        } catch (e: Throwable) {
            logger.jsonError(
                    desc = LogEventEnum.Disable.value,
                    value = object {
                        val message = "The offer was not successfully disabled"
                        val storedOffer = storedOffer
                    }
            )
            if (e is GatewayTimeoutException) {
                throw GatewayTimeoutException(e.toString())
            } else {
                throw InternalServiceErrorException(e.message)
            }
        }

        logger.info(mapper.writeValueAsString(storedOffer))
        return ResponseEntity(offerTransformService.transform(storedOffer), HttpStatus.OK)
    }

    @Transactional

    fun enableOffer(offerToEnable: IdIO, headers: Map<String, String>): ResponseEntity<OfferIO> {
        // validate user
        if (!headers.containsKey("x-user-email")) {
            logger.jsonError(
                    desc = LogEventEnum.Enable.value,
                    value = object {
                        val message = "Ignored, header is missing"
                        val id = offerToEnable.id
                    }
            )
            throw BadRequestException("Missing authentication")
        }

        // validate each offer is in draft state
        val storedOffer: Offer = offerRepo.findById(UUID.fromString(offerToEnable.id)).orElseThrow {
            logger.jsonError(
                    desc = LogEventEnum.Enable.value,
                    value = object {
                        val message = "Offer to Enable Not Found"
                        val id = offerToEnable.id
                    }
            )
            OfferNotFoundException(offerToEnable.toString())
        }

        if (storedOffer.status === OfferStatus.DRAFT) {
            throw com.loyalty.offermanagement.errors.OfferCannotBeEnabledException(storedOffer.id.toString())
        }

        if (storedOffer.active) {
            throw BadRequestException("Offer with ID ${storedOffer.id} is already in the active state")
        }

        try {
            storedOffer.active = true
            offerRepo.save(storedOffer)
            // Publish offer will fetch the id that was saved to the DB and publish it so that it can be updated downstream
            publishOffer(IdIO(storedOffer.id.toString()), headers)
        } catch (e: Throwable) {
            logger.jsonError(
                    desc = LogEventEnum.Enable.value,
                    value = object {
                        val message = "The offer was not successfully enabled"
                        val storedOffer = storedOffer
                    },
                    throwable = e
            )
            if (e is GatewayTimeoutException) {
                throw GatewayTimeoutException(e.toString())
            } else {
                throw Throwable(e.message)
            }
        }

        logger.info(mapper.writeValueAsString(storedOffer))
        return ResponseEntity(offerTransformService.transform(storedOffer), HttpStatus.OK)
    }

    fun postOffers(body: BulkPostOfferIO, headers: Map<String, String>): ResponseEntity<IdIO> {
        val partnerId = body.offers.first().partnerId
        val partnerName = body.offers.first().partnerName
        val totalCount = body.offers.size
        if (!headers.containsKey("x-user-email")) {
            logger.jsonError(
                    desc = LogEventEnum.CreateBulk.value,
                    value = object {
                        val message = "Ignored, header is missing"
                        val bulkName = body.bulkName
                        val partnerId = partnerId
                        val partnerName = partnerName
                        val totalCount = totalCount
                    }
            )
            throw BadRequestException("Missing authentication")
        }

        body.fallback()

        try {
            this.postBulkOfferSchema.validate(JSONObject(mapper.writeValueAsString(body)))
            validateOfferDates(body)
            validateOfferUniqueness(body)
        } catch (exception: ValidationException) {
            logger.jsonError(
                    desc = LogEventEnum.CreateBulk.value,
                    value = object {
                        val message = exception.allMessages.toString()
                        val bulkName = body.bulkName
                        val partnerId = partnerId
                        val partnerName = partnerName
                        val totalCount = totalCount
                    },
                    throwable = exception
            )
            throw BadRequestException(exception.allMessages.toString(), exception)
        }

        val bulkId = UUID.randomUUID()
        val batchJob = BatchJob(id = bulkId, batchName = body.bulkName, createdBy = headers["x-user-email"].toString())

        try {
            batchJob.offers = body.offers.map {
                val offerIO = mapper.convertValue(it, OfferIO::class.java)
                offerIO.createdBy = headers["x-user-email"].toString()
                offerIO.integrations = listOf(IntegrationIO(
                        systemName = ExternalSystem.Agility,
                        systemCode = DefaultAgilityIDGenerator().generateAgilityOfferCode())
                )

                val cgsResponse = contentGenerationService.generate(offerIO)
                val offerWithCopyStrings = getOfferWithCopyStrings(offerIO, cgsResponse.body)

                val offerRDS = offerTransformService.transform(offerWithCopyStrings)
                offerRDS
            }

            jobRepo.save(batchJob)
            logger.jsonInfo(
                    desc = LogEventEnum.CreateBulk.value,
                    value = object {
                        val message = ""
                        val bulkId = bulkId
                        val bulkName = body.bulkName
                        val partnerId = partnerId
                        val partnerName = partnerName
                        val totalCount = totalCount
                    }
            )

            return ResponseEntity(IdIO(bulkId.toString()), HttpStatus.OK)
        } catch (exception: RuntimeException) {
            logger.jsonError(
                    desc = LogEventEnum.CreateBulk.value,
                    value = object {
                        val message = exception.message
                        val bulkName = body.bulkName
                        val partnerId = partnerId
                        val partnerName = partnerName
                        val totalCount = totalCount
                    },
                    throwable = exception
            )

            throw BadQueryException(exception)
        }
    }

    fun putOffer(putOffer: PutOfferIO, id: String, headers: Map<String, String>): ResponseEntity<OfferIO> {
        if (!headers.containsKey("x-user-email")) {
            logger.jsonError(
                    desc = LogEventEnum.Update.value,
                    value = object {
                        val message = "Ignored, header is missing"
                        val putOffer = putOffer
                    }
            )

            throw BadRequestException("Missing authentication")
        }
        putOffer.fallback()

        try {

            this.putOfferSchema.validate(JSONObject(mapper.writeValueAsString(putOffer)))
            validateOfferDates(putOffer)
            updateDates(putOffer)
            validateOfferUniqueness(putOffer)
            validateCardType(putOffer)
            validateBMOPreApproval(putOffer)
            validateCtaUrlCtaLabel(putOffer)
            validateAMReceipt(putOffer)
        } catch (exception: ValidationException) {
            logger.jsonError(
                    desc = LogEventEnum.Update.value,
                    value = object {
                        val message = exception.allMessages.toString()
                        val putOffer = putOffer
                    },
                    throwable = exception
            )

            throw BadRequestException(exception.allMessages.toString(), exception)
        }

        val storedOffer: Offer = offerRepo.findById(UUID.fromString(id)).orElseThrow {
            logger.jsonError(
                    desc = LogEventEnum.Update.value,
                    value = object {
                        val message = "Offer to Update Not Found"
                        val id = id
                    }
            )

            OfferNotFoundException(id)
        }
        storedOffer validateUpdateTo putOffer

        val offerContent = mapper.readValue(storedOffer.offerContent, OfferContent::class.java)
        val offerIO = mapper.convertValue(putOffer, OfferIO::class.java)

        offerIO.id = storedOffer.id.toString()
        offerIO.createdAt = storedOffer.createdAt
        offerIO.updatedAt = Instant.now()
        offerIO.createdBy = storedOffer.createdBy
        offerIO.updatedBy = headers["x-user-email"]
        offerIO.publishedAt = storedOffer.publishedAt
        offerIO.publishedBy = storedOffer.publishedBy
        offerIO.contentfulId = offerContent.contentfulId
        offerIO.detailsId = offerContent.detailsId
        offerIO.status = if (storedOffer.status != OfferStatus.DRAFT) OfferStatus.UPDATED else OfferStatus.DRAFT
        offerIO.integrations = if (storedOffer.integrations.isNotEmpty()) storedOffer.integrations.map {
            IntegrationIO(
                    systemName = it.systemName,
                    systemCode = it.systemCode
            )
        } else listOf(IntegrationIO(
                systemName = ExternalSystem.Agility,
                systemCode = DefaultAgilityIDGenerator().generateAgilityOfferCode())
        )

        try {
            val cgsResponse = contentGenerationService.generate(offerIO)
            val offerWithCopyStrings = getOfferWithCopyStrings(offerIO, cgsResponse.body)

            offerRepo.save(offerTransformService.transform(offerWithCopyStrings))

            logger.jsonInfo(
                    desc = LogEventEnum.Update.value,
                    value = offerIO
            )

            return ResponseEntity(offerWithCopyStrings, HttpStatus.OK)
        } catch (exception: RuntimeException) {
            logger.jsonError(
                    desc = LogEventEnum.Update.value,
                    value = object {
                        val message = exception.message
                        val offerIO = offerIO
                    },
                    throwable = exception
            )

            throw BadQueryException(exception)
        }
    }

    @Transactional

    fun patchOffersForBulk(body: BulkPutOfferIO, bulkId: String, headers: Map<String, String>) {
        val partnerId = body.offers.first().partnerId
        val partnerName = body.offers.first().partnerName
        val totalCount = body.offers.size
        if (!headers.containsKey("x-user-email")) {
            logger.jsonError(
                    desc = LogEventEnum.UpdateBulk.value,
                    value = object {
                        val message = "Ignored, header is missing"
                        val bulkId = bulkId
                        val bulkName = body.bulkName
                        val partnerId = partnerId
                        val partnerName = partnerName
                        val totalCount = totalCount
                    }
            )

            throw BadRequestException("Missing authentication")
        }

        body.fallback()

        try {
            this.putBulkOfferSchema.validate(JSONObject(mapper.writeValueAsString(body)))
            validateOfferDates(body)
            validateOfferUniqueness(body)
        } catch (exception: ValidationException) {
            logger.jsonError(
                    desc = LogEventEnum.UpdateBulk.value,
                    value = object {
                        val message = exception.allMessages.toString()
                        val bulkId = bulkId
                        val bulkName = body.bulkName
                        val partnerId = partnerId
                        val partnerName = partnerName
                        val totalCount = totalCount
                    },
                    throwable = exception
            )

            throw BadRequestException(exception.allMessages.toString(), exception)
        }

        // Find the bulk job by id
        val storedBatchJob = jobRepo.findById(UUID.fromString(bulkId)).orElseThrow {
            logger.jsonError(
                    desc = LogEventEnum.UpdateBulk.value,
                    value = object {
                        val message = "Bulk Job to Update Not Found"
                        val bulkId = bulkId
                        val bulkName = body.bulkName
                        val partnerId = partnerId
                        val partnerName = partnerName
                        val totalCount = totalCount
                    }
            )

            BulkJobNotFoundException(bulkId)
        }

        val updatedOffers: MutableList<Offer> = mutableListOf()

        // For each request body offers, check if they exist in the batch job by id to identify what offer to update
        for (offer in body.offers) {
            // Get the offer to update from the stored batch job
            val storedOffer: Offer?
            try {
                storedOffer = storedBatchJob.offers.find { it.id.toString() == offer.id }!!
            } catch (exception: Throwable) {
                logger.jsonError(
                        desc = LogEventEnum.UpdateBulk.value,
                        value = object {
                            val message = "Offer to Update Not Found In Bulk Job"
                            val bulkId = bulkId
                            val bulkName = body.bulkName
                            val partnerId = partnerId
                            val partnerName = partnerName
                            val totalCount = totalCount
                        },
                        throwable = exception
                )

                throw OfferNotFoundInBulkJobException(offer.id)
            }
            if (storedOffer != null) {
                storedOffer validateUpdateTo offer

                val offerContent = mapper.readValue(storedOffer.offerContent, OfferContent::class.java)
                val offerIO = mapper.convertValue(offer, OfferIO::class.java)

                offerIO.id = storedOffer.id.toString()
                offerIO.createdAt = storedOffer.createdAt
                offerIO.updatedAt = Instant.now()
                offerIO.createdBy = storedOffer.createdBy
                offerIO.active = storedOffer.active
                offerIO.updatedBy = headers["x-user-email"]
                offerIO.publishedAt = storedOffer.publishedAt
                offerIO.publishedBy = storedOffer.publishedBy
                offerIO.contentfulId = offerContent.contentfulId
                offerIO.detailsId = offerContent.detailsId
                offerIO.status = if (storedOffer.status != OfferStatus.DRAFT) OfferStatus.UPDATED else OfferStatus.DRAFT
                offerIO.bulkName = body.bulkName
                offerIO.bulkId = bulkId
                offerIO.integrations = if (storedOffer.integrations.isNotEmpty()) storedOffer.integrations.map {
                    IntegrationIO(
                            systemName = it.systemName,
                            systemCode = it.systemCode
                    )
                } else listOf(IntegrationIO(
                        systemName = ExternalSystem.Agility,
                        systemCode = DefaultAgilityIDGenerator().generateAgilityOfferCode())
                )

                try {
                    val cgsResponse = contentGenerationService.generate(offerIO)
                    val offerWithCopyStrings = getOfferWithCopyStrings(offerIO, cgsResponse.body)

                    // Store offer in a list of all updated offers
                    updatedOffers.add(offerTransformService.transform(offerWithCopyStrings))
                } catch (exception: Throwable) {
                    logger.jsonError(
                            desc = LogEventEnum.UpdateBulk.value,
                            value = object {
                                val message = exception.toString()
                                val bulkId = bulkId
                                val bulkName = body.bulkName
                                val partnerId = partnerId
                                val partnerName = partnerName
                                val totalCount = totalCount
                                val offerId = offer.id
                                val offer = offerIO
                            },
                            throwable = exception
                    )

                    throw BadRequestException(exception)
                }
            }
        }

        try {
            if (updatedOffers.isNotEmpty()) {
                offerRepo.saveAll(
                        updatedOffers.distinctBy { it.id })
            }

            // Save the batch job now that the offer fields have been updated
            storedBatchJob.updatedAt = Instant.now()
            storedBatchJob.updatedBy = headers["x-user-email"]
            storedBatchJob.batchName = body.bulkName
            jobRepo.save(storedBatchJob)
            logger.jsonInfo(
                    desc = LogEventEnum.UpdateBulk.value,
                    value = object {
                        val bulkId = bulkId
                        val bulkName = body.bulkName
                        val partnerId = partnerId
                        val partnerName = partnerName
                        val totalCount = totalCount
                    }
            )
        } catch (exception: RuntimeException) {
            logger.jsonError(
                    desc = LogEventEnum.UpdateBulk.value,
                    value = object {
                        val message = exception.message
                        val bulkId = bulkId
                        val bulkName = body.bulkName
                        val partnerId = partnerId
                        val partnerName = partnerName
                        val totalCount = totalCount
                    },
                    throwable = exception
            )

            throw BadQueryException(exception)
        }
    }

    fun publishOffer(publishOffer: IdIO, headers: Map<String, String>) {
        val correlationId = UUID.randomUUID().toString()
        if (!headers.containsKey("x-user-email")) {
            logger.jsonError(
                    desc = LogEventEnum.Publish.value,
                    value = object {
                        val message = "Ignored, header is missing"
                        val offer = publishOffer
                    }
            )

            throw BadRequestException("Missing authentication")
        }

        try {
            this.publishOfferSchema.validate(JSONObject(mapper.writeValueAsString(publishOffer)))
        } catch (exception: ValidationException) {
            logger.jsonError(
                    desc = LogEventEnum.Publish.value,
                    value = object {
                        val message = exception.allMessages.toString()
                        val offer = publishOffer
                    },
                    throwable = exception
            )

            throw BadRequestException(exception.allMessages.toString(), exception)
        }

        try {
            val offer = this.getOffer(publishOffer.id, headers).body

            if (offer?.offerCategory1 != null) {
                offer.offerCategory1Label = offerCategoryServicevice.getCategoryInfo(offer.offerCategory1)
            }
            if (offer?.offerCategory2 != null) {
                offer.offerCategory2Label = offerCategoryServicevice.getCategoryInfo(offer.offerCategory2)
            }
            if (offer?.offerCategory3 != null) {
                offer.offerCategory3Label = offerCategoryServicevice.getCategoryInfo(offer.offerCategory3)
            }
            val tag = offer?.tags?.firstOrNull()
            if (tag != null) {
                offer.offerPromotionLabel = offerPromotionService.getPromotionInfo(tag)
            }

            // targeted offer won't be sent to v2
            if (offer?.massOffer == true && featureToggleConfig.featureToggleStreamEventV2.toBoolean()) {
                val offerV2 = offer.let { offerTransformService.transformOfferV2(it) }
                if (offer.contentfulId != null && offer.detailsId != null) {
                    try {
                        offerV2.let { offerV2Service.update(it) }
                        logger.jsonInfo(
                                desc = LogEventEnum.Redeploy.value,
                                value = object {
                                    val id = publishOffer.id
                                    val offer = offerV2
                                }
                        )
                    } catch (exception: Throwable) {
                        logger.jsonError(
                                desc = LogEventEnum.Redeploy.value,
                                value = object {
                                    val message = exception.toString()
                                    val id = publishOffer.id
                                    val offer = offerV2
                                },
                                throwable = exception
                        )

                        if (exception is org.springframework.web.client.ResourceAccessException) {
                            throw GatewayTimeoutException(exception.toString())
                        } else {
                            throw BadRequestException(exception)
                        }
                    }
                } else {
                    try {
                        val createResponse = offerV2.let { offerV2Service.create(it) }
                        offer.contentfulId = createResponse.body?.results?.id
                        offer.detailsId = createResponse.body?.results?.enUS?.sys?.id
                        logger.jsonInfo(
                                desc = LogEventEnum.Deploy.value,
                                value = object {
                                    val id = publishOffer.id
                                    val offer = offerV2
                                }
                        )
                    } catch (exception: Throwable) {
                        logger.jsonError(
                                desc = LogEventEnum.Deploy.value,
                                value = object {
                                    val message = exception.message
                                    val id = publishOffer.id
                                    val offer = offerV2
                                },
                                throwable = exception
                        )

                        throw BadRequestException(exception)
                    }
                }
                Thread.sleep(2000) // TODO: Further investigate this
                offer.contentfulId?.let { offerV2Service.publish(it) }
            }
            offer?.status = OfferStatus.valueOf("PUBLISHED")
            offer?.publishedAt = Instant.now()
            offer?.publishedBy = headers["x-user-email"]

            val offerRDS = offer?.let { offerTransformService.transform(it) }
            try {
                offerRDS?.let { offerRepo.save(it) }
                try {
                    //start adding code here

                    val region="us-east-1"

                    offer?.let { kinesisEventWriter.sendOfferToKinesis(offer,correlationId,region) }

                    //  offer?.let { novaBusRepository.publishOffer(offer, correlationId) }
                        } catch (exception: Throwable) {
                    logger.jsonError(
                            desc = LogEventEnum.Stream.value,
                            value = object {
                                val message = exception.message
                                val correlationId = correlationId
                                val offer = offer
                            },
                            throwable = exception
                    )
                }
                //call the kinesis function to send offers to ca-central-1
                try {
                    val region="ca-central-1"

                    offer?.let { kinesisEventWriter.sendOfferToKinesis(offer,correlationId,region) }
                    logger.jsonInfo(
                            desc = LogEventEnum.KinesisStream.value,
                            value = object {
                                val offer = offer
                                val correlationId = correlationId
                            }
                    )
                } catch (exception: Throwable) {
                    logger.jsonError(
                            desc = LogEventEnum.KinesisStream.value,
                            value = object {
                                val message = exception.message
                                val correlationId = correlationId
                                val offer = offer
                            },
                            throwable = exception
                    )
                }
            } catch (exception: Throwable) {
                logger.jsonError(
                        desc = LogEventEnum.UpdateDB.value,
                        value = object {
                            val message = exception.message
                            val offer = offer
                        },
                        throwable = exception
                )

                throw BadRequestException(exception.toString())
            }
            logger.jsonInfo(
                    desc = LogEventEnum.Publish.value,
                    value = offer ?: object {}
            )
        } catch (exception: Throwable) {
            logger.jsonError(
                    desc = LogEventEnum.Publish.value,
                    value = object {
                        val message = exception.message
                        val offer = publishOffer
                    },
                    throwable = exception
            )

            if (exception is GatewayTimeoutException) {
                throw GatewayTimeoutException(exception.toString())
            } else {
                throw BadRequestException(exception)
            }
        }
    }

    fun republishOffers(): RepublishResponseIO {
        val allOffers = mutableListOf<OfferIO>()
        var page = 0
        val size = 100
        // sets the dateContext property to the first DateContextType value that is not equal to DateContextType.expired
        val offerCriteria = OfferCriteria(status = setOf(OfferStatusCriteria.published, OfferStatusCriteria.updated),dateContext = DateContextType.notExpired)
        val totalCount = getOffers(offerCriteria, PageCriteria()).totalCount
        var successCount = 0L
        var failureCount = 0L

        logger.info("republish-offers - Found $totalCount offers in the database")

        while (totalCount > allOffers.size) {
            val returnedOffers = getOffers(offerCriteria, PageCriteria(page = page, size = size)).content
            returnedOffers?.map { x ->
                allOffers.add(x)
            }
            page++
        }

        logger.info("republish-offers - Entered ${allOffers.size} offers out of $totalCount into the offers array")

        logger.info("republish-offers - Starting to Republish $totalCount offers")

        allOffers.mapIndexed { index, offer ->

            try {
                if (offer.offerCategory1 != null) {
                    offer.offerCategory1Label = offerCategoryServicevice.getCategoryInfo(offer.offerCategory1)
                }
                if (offer.offerCategory2 != null) {
                    offer.offerCategory2Label = offerCategoryServicevice.getCategoryInfo(offer.offerCategory2)
                }
                if (offer.offerCategory3 != null) {
                    offer.offerCategory3Label = offerCategoryServicevice.getCategoryInfo(offer.offerCategory3)
                }
                val tag = offer.tags.firstOrNull()
                if (tag != null) {
                    offer.offerPromotionLabel = offerPromotionService.getPromotionInfo(tag)
                }
                if (offer.integrations.isEmpty()) {
                    offer.integrations = listOf(IntegrationIO(
                            systemName = ExternalSystem.Agility,
                            systemCode = DefaultAgilityIDGenerator().generateAgilityOfferCode())
                    )
                }
                // for sending data to dynamoDB use novaBusRepository publishOffer with offer, UUID.randomUUID().toString()
                kinesisEventWriter.sendOfferToKinesis(offer,UUID.randomUUID().toString(),"ca-central-1")
                successCount++
            } catch (exception: Throwable) {
                failureCount++
                logger.jsonError(
                        desc = LogEventEnum.RePublish.value,
                        value = object {
                            val message = exception.message
                            val offer = offer
                        },
                        throwable = exception
                )
            } finally {
                logger.jsonInfo(
                        desc = LogEventEnum.RePublish.value,
                        value = object {
                            val totalCount = totalCount
                            val successCount = successCount
                            val failureCount = failureCount
                        }
                )
            }
        }

        return RepublishResponseIO(totalCount = totalCount, successCount = successCount, failureCount = failureCount)
    }

    private fun getOfferWithCopyStrings(offer: OfferIO, data: ContentGenerationResponse?): OfferIO {
        val clonedOffer = offer.copy()
        clonedOffer.awardShort = data?.awardShort
        clonedOffer.qualifierShort = data?.qualifierShort
        clonedOffer.legalText = data?.legal
        clonedOffer.tiers = offer.tiers.mapIndexed { index, it ->
            it.qualifierLong = data?.qualifierLong?.elementAt(index)
            it.awardLong = data?.awardLong?.elementAt(index)
            it
        }
        return clonedOffer
    }

    fun getOffers(offerCriteria: OfferCriteria, pageCriteria: PageCriteria):
            SearchResults<OfferIO> {

        val dateObject = getOffersHelper(offerCriteria)
        try {
            val sortObj = Sort.by(pageCriteria.direction!!.sortDir, pageCriteria.sort.name, "id")
            val pageReq = PageRequest.of(pageCriteria.page, pageCriteria.size, sortObj)

            val results = offerRepo.doSearch(
                    startFrom = dateObject.startFrom,
                    startTill = dateObject.startTill,
                    endFrom = dateObject.endFrom,
                    endTill = dateObject.endTill,
                    ids = offerCriteria.id?.takeIf(Set<*>::isNotEmpty),
                    users = offerCriteria.user,
                    partners = offerCriteria.partnerId?.takeIf(Set<*>::isNotEmpty),
                    bulkIds = offerCriteria.bulkId?.takeIf(Set<*>::isNotEmpty),
                    tag1 = offerCriteria.tags?.toList()?.getOrNull(0),
                    tag2 = offerCriteria.tags?.toList()?.getOrNull(1),
                    tag3 = offerCriteria.tags?.toList()?.getOrNull(2),
                    offerTypes = offerCriteria.offerType?.map { it.type }?.toSet(),
                    awardTypes = offerCriteria.awardType?.map { it.type }?.toSet(),
                    qualifiers = offerCriteria.qualifier?.map { it.qualifier }?.toSet(),
                    mechanismType = offerCriteria.mechanismType?.map { it.mechanismType }?.toSet(),
                    region1 = offerCriteria.regions?.toList()?.getOrNull(0),
                    region2 = offerCriteria.regions?.toList()?.getOrNull(1),
                    region3 = offerCriteria.regions?.toList()?.getOrNull(2),
                    region4 = offerCriteria.regions?.toList()?.getOrNull(3),
                    region5 = offerCriteria.regions?.toList()?.getOrNull(4),
                    region6 = offerCriteria.regions?.toList()?.getOrNull(5),
                    region7 = offerCriteria.regions?.toList()?.getOrNull(6),
                    region8 = offerCriteria.regions?.toList()?.getOrNull(7),
                    region9 = offerCriteria.regions?.toList()?.getOrNull(8),
                    region10 = offerCriteria.regions?.toList()?.getOrNull(9),
                    region11 = offerCriteria.regions?.toList()?.getOrNull(10),
                    region12 = offerCriteria.regions?.toList()?.getOrNull(11),
                    region13 = offerCriteria.regions?.toList()?.getOrNull(12),
                    region14 = offerCriteria.regions?.toList()?.getOrNull(13),
                    region15 = offerCriteria.regions?.toList()?.getOrNull(14),
                    statuses = dateObject.statuses,
                    massOffer = offerCriteria.massOffer,
                    active = offerCriteria.active,
                    offerCategory1 = offerCriteria.offerCategory1,
                    offerCategory2 = offerCriteria.offerCategory2,
                    offerCategory3 = offerCriteria.offerCategory3,
                    onlyEmptyCategory = offerCriteria.onlyEmptyCategory,
                    hasIssuanceCode = offerCriteria.hasIssuanceCode,
                    campaignCode = offerCriteria.campaignCode?.map { it.toUpperCase() }?.takeIf(List<*>::isNotEmpty)?.toSet(),
                    programTypes = offerCriteria.programType?.map { it.type }?.toSet(),
                    eventBasedOffer = offerCriteria.eventbasedOffer,
                    eligibilityDuration = offerCriteria.eligibilityDuration,
                    eligibilityDurationUnits = offerCriteria.eligibilityDurationUnit?.map { it.unit }?.toSet(),
                    pageReq = pageReq
            )
            val searchResults = SearchResults<OfferIO>()
            searchResults.totalCount = results.totalElements
            searchResults.pageNumber = results.number
            searchResults.pageSize = pageCriteria.size
            searchResults.content = results.content.map {
                offerTransformService.transform(it)
            }.toList()
            searchResults.returnedCount = searchResults.content?.size ?: 0
            logger.jsonInfo(
                    desc = LogEventEnum.Search.value,
                    value = offerCriteria
            )

            return searchResults
        } catch (exception: RuntimeException) {
            logger.jsonError(
                    desc = LogEventEnum.Search.value,
                    value = object {
                        val message = exception.message
                        val offerCriteria = offerCriteria
                    },
                    throwable = exception
            )

            throw BadQueryException(exception)
        }
    }

    fun getOffersCountsBySearch(offerCriteria: OfferCriteria): Long {
        val dateObject = getOffersHelper(offerCriteria)
        try {
            val results = offerRepo.countBySearch(
                    startFrom = dateObject.startFrom,
                    startTill = dateObject.startTill,
                    endFrom = dateObject.endFrom,
                    endTill = dateObject.endTill,
                    ids = offerCriteria.id?.takeIf(Set<*>::isNotEmpty),
                    users = offerCriteria.user,
                    partners = offerCriteria.partnerId?.takeIf(Set<*>::isNotEmpty),
                    bulkIds = offerCriteria.bulkId?.takeIf(Set<*>::isNotEmpty),
                    tag1 = offerCriteria.tags?.toList()?.getOrNull(0),
                    tag2 = offerCriteria.tags?.toList()?.getOrNull(1),
                    tag3 = offerCriteria.tags?.toList()?.getOrNull(2),
                    offerTypes = offerCriteria.offerType?.map { it.type }?.toSet(),
                    awardTypes = offerCriteria.awardType?.map { it.type }?.toSet(),
                    qualifiers = offerCriteria.qualifier?.map { it.qualifier }?.toSet(),
                    mechanismType = offerCriteria.mechanismType?.map { it.mechanismType }?.toSet(),
                    region1 = offerCriteria.regions?.toList()?.getOrNull(0),
                    region2 = offerCriteria.regions?.toList()?.getOrNull(1),
                    region3 = offerCriteria.regions?.toList()?.getOrNull(2),
                    region4 = offerCriteria.regions?.toList()?.getOrNull(3),
                    region5 = offerCriteria.regions?.toList()?.getOrNull(4),
                    region6 = offerCriteria.regions?.toList()?.getOrNull(5),
                    region7 = offerCriteria.regions?.toList()?.getOrNull(6),
                    region8 = offerCriteria.regions?.toList()?.getOrNull(7),
                    region9 = offerCriteria.regions?.toList()?.getOrNull(8),
                    region10 = offerCriteria.regions?.toList()?.getOrNull(9),
                    region11 = offerCriteria.regions?.toList()?.getOrNull(10),
                    region12 = offerCriteria.regions?.toList()?.getOrNull(11),
                    region13 = offerCriteria.regions?.toList()?.getOrNull(12),
                    region14 = offerCriteria.regions?.toList()?.getOrNull(13),
                    region15 = offerCriteria.regions?.toList()?.getOrNull(14),
                    statuses = dateObject.statuses,
                    massOffer = offerCriteria.massOffer,
                    active = offerCriteria.active,
                    offerCategory1 = offerCriteria.offerCategory1,
                    offerCategory2 = offerCriteria.offerCategory2,
                    offerCategory3 = offerCriteria.offerCategory3,
                    onlyEmptyCategory = offerCriteria.onlyEmptyCategory,
                    hasIssuanceCode = offerCriteria.hasIssuanceCode,
                    campaignCode = offerCriteria.campaignCode?.map { it.toUpperCase() }?.takeIf(List<*>::isNotEmpty)?.toSet(),
                    programTypes = offerCriteria.programType?.map { it.type }?.toSet(),
                    eventBasedOffer = offerCriteria.eventbasedOffer,
                    eligibilityDuration = offerCriteria.eligibilityDuration,
                    eligibilityDurationUnits = offerCriteria.eligibilityDurationUnit?.map { it.unit }?.toSet()
            )

            return results
        } catch (exception: RuntimeException) {
            logger.jsonError(
                    desc = LogEventEnum.GetOffersCounts.value,
                    value = object {
                        val message = exception.message
                        val offerCriteria = offerCriteria
                    },
                    throwable = exception
            )

            throw BadQueryException(exception)
        }
    }

    fun getOfferCounts(offerCriteria: OfferCriteria): OfferCounts {
        // Get Offers from each offer filter status by changing the offerCriteria
        val draftCount = CompletableFuture.supplyAsync {
            val draftCriteria = offerCriteria.copy()
            draftCriteria.status = setOf(OfferStatusCriteria.draft)
            draftCriteria.dateContext = null
            draftCriteria.active = true
            getOffersCountsBySearch(draftCriteria)
        }
        val changesPending = CompletableFuture.supplyAsync {
            val changesCriteria = offerCriteria.copy()
            changesCriteria.status = setOf(OfferStatusCriteria.updated)
            changesCriteria.dateContext = DateContextType.notExpired
            changesCriteria.active = true
            getOffersCountsBySearch(changesCriteria)
        }
        val stagedCount = CompletableFuture.supplyAsync {
            val stagedCriteria = offerCriteria.copy()
            stagedCriteria.status = setOf(OfferStatusCriteria.published)
            stagedCriteria.dateContext = DateContextType.staged
            stagedCriteria.active = true
            getOffersCountsBySearch(stagedCriteria)
        }
        val publishedCount = CompletableFuture.supplyAsync {
            val publishedCriteria = offerCriteria.copy()
            publishedCriteria.status = setOf(OfferStatusCriteria.published)
            publishedCriteria.dateContext = DateContextType.live
            publishedCriteria.active = true
            getOffersCountsBySearch(publishedCriteria)
        }
        val expiredCount = CompletableFuture.supplyAsync {
            val expiredCriteria = offerCriteria.copy()
            expiredCriteria.status = setOf(OfferStatusCriteria.published, OfferStatusCriteria.updated)
            expiredCriteria.dateContext = DateContextType.expired
            expiredCriteria.active = null
            getOffersCountsBySearch(expiredCriteria)
        }
        val disabledCount = CompletableFuture.supplyAsync {
            val disabledCriteria = offerCriteria.copy()
            disabledCriteria.active = false
            disabledCriteria.status = null
            disabledCriteria.dateContext = DateContextType.notExpired
            getOffersCountsBySearch(disabledCriteria)
        }

        CompletableFuture.allOf(draftCount, changesPending, stagedCount, publishedCount, expiredCount, disabledCount)

        val offerCounts = OfferCounts()
        offerCounts.draft = draftCount.get()
        offerCounts.changesPending = changesPending.get()
        offerCounts.staged = stagedCount.get()
        offerCounts.live = publishedCount.get()
        offerCounts.expired = expiredCount.get()
        offerCounts.disabled = disabledCount.get()

        offerCounts.total = offerCounts.draft + offerCounts.changesPending + offerCounts.staged + offerCounts.live + offerCounts.expired + offerCounts.disabled

        logger.jsonInfo(
                desc = LogEventEnum.GetOffersCounts.value,
                value = offerCounts
        )
        return offerCounts
    }

    private fun getOffersHelper(offerCriteria: OfferCriteria): DateObjectType {
        val dateObject = DateObjectType()
        /** These are always just published status
         * and while we are using the same query for all date boundary logic,
         * have to come up with between boundaries
         * assumption: displayDate is always smaller or equal to endDate
         */
        dateObject.statuses = offerCriteria.status?.map { it.status }?.toSet()
        when (offerCriteria.dateContext) {
            DateContextType.live -> {
                // start    between BeginningOfTime and NOW
                // end     between NOW and EndOfTime
                dateObject.statuses = setOf(OfferStatus.PUBLISHED)
                dateObject.startFrom = TimeUtils.getBeginningOfTime()
                dateObject.startTill = TimeUtils.getDateAtNow()    // rightNow()
                dateObject.endFrom = TimeUtils.getDateAtNow()   // rightNow()
                dateObject.endTill = TimeUtils.getEndOfTime()
                if (offerCriteria.displayDate != null && offerCriteria.endDate != null) {
                    // we only care about expiration boundary but to override startTill looking till the end of scope
                    dateObject.startTill = TimeUtils.getDateEnd(getDiffDay(offerCriteria.endDate))
                    dateObject.endFrom = TimeUtils.getDateMidnight(getDiffDay(offerCriteria.displayDate))
                }
            }
            DateContextType.staged -> {
                // start    between Now()+1 and EndOfTime
                // end      between Now()+1 and EndOfTime
                dateObject.statuses = setOf(OfferStatus.PUBLISHED)
                dateObject.startFrom = TimeUtils.getDateAtNow().plusSeconds(1) //Now + 1 Sec
                dateObject.startTill = TimeUtils.getEndOfTime()
                dateObject.endFrom = TimeUtils.getDateAtNow().plusSeconds(1)  //Now + 1 Sec
                dateObject.endTill = TimeUtils.getEndOfTime()
                if (offerCriteria.displayDate != null && offerCriteria.endDate != null) {
                    // Too complicated ==> live in next scope, expiry doesn't really matter
                    val dateScope = getDiffDay(offerCriteria.displayDate, offerCriteria.endDate)
                    dateObject.startFrom = TimeUtils.getDateMidnight(getDiffDay(offerCriteria.endDate))
                    dateObject.startTill = TimeUtils.getDateEnd(offerCriteria.endDate, dateScope * 2)
                    dateObject.endFrom = TimeUtils.getDateMidnight(offerCriteria.displayDate, dateScope)
                }
            }
            DateContextType.expired -> {
                // start    officially doesnt matter... let it be all
                // end      between beginning of the time and NOW
                dateObject.statuses = setOf(OfferStatus.PUBLISHED, OfferStatus.UPDATED)
                dateObject.startFrom = TimeUtils.getBeginningOfTime()
                dateObject.startTill = TimeUtils.getEndOfTime()
                dateObject.endFrom = TimeUtils.getBeginningOfTime()
                dateObject.endTill = TimeUtils.getDateAtNow()
                if (offerCriteria.displayDate != null && offerCriteria.endDate != null) {
                    // any time from given scope to make the offer expired
                    dateObject.endFrom = TimeUtils.getDateMidnight(getDiffDay(offerCriteria.displayDate))
                    dateObject.endTill = TimeUtils.getDateEnd(getDiffDay(offerCriteria.endDate))
                }
            }
            DateContextType.notExpired -> {
                //EndFrom = Now()
                //EndTill = EndOfTime
                dateObject.startFrom = TimeUtils.getBeginningOfTime()
                dateObject.startTill = TimeUtils.getEndOfTime()
                dateObject.endFrom = TimeUtils.getDateAtNow().plusSeconds(1)    // rightNow()
                dateObject.endTill = TimeUtils.getEndOfTime()
            }
            else -> {
                dateObject.startFrom = TimeUtils.getBeginningOfTime()
                dateObject.startTill = TimeUtils.getEndOfTime()
                dateObject.endFrom = TimeUtils.getBeginningOfTime()
                dateObject.endTill = TimeUtils.getEndOfTime()
            }
        }
        return dateObject
    }

    private fun validateOfferDates(displayDateStr: String, startDateStr: String, endDateStr: String, firstQualificationDateStr: String?, lastQualificationDateStr: String?, eligibilityDuration:Int?, eligibilityDurationUnit: EligibilityDurationUnit?) {
        val displayDate = Instant.parse(displayDateStr)
        val startDate = Instant.parse(startDateStr)
        val endDate = Instant.parse(endDateStr)

        if (startDate.isBefore(displayDate)) {
            throw BadRequestException("Start date cannot be before display date.")
        }
        if (endDate.isBefore(displayDate)) {
            throw BadRequestException("End date cannot be before display date.")
        }
        if (endDate.isBefore(startDate)) {
            throw BadRequestException("End date cannot be before start date.")
        }
        if (endDate.isBefore(Instant.now())) {
            throw BadRequestException("End date cannot be before current date.")
        }
        if(!StringUtils.isNullOrEmpty(firstQualificationDateStr) || !StringUtils.isNullOrEmpty(lastQualificationDateStr)
                || eligibilityDuration!=null || eligibilityDurationUnit !=null){
            throw BadRequestException("First qualification date, last qualification date, eligibility duration and eligibility duration unit are valid just for event based offers.")
        }
    }

    private fun validateOfferDates(postOffer: PostOfferIO) {
        if (postOffer.eventBasedOffer){
            validateEventBasedOfferDates(postOffer.displayDate, postOffer.startDate, postOffer.endDate, postOffer.firstQualificationDate!!, postOffer.lastQualificationDate!!)
        }else{
            validateOfferDates(postOffer.displayDate!!, postOffer.startDate!!, postOffer.endDate!!, postOffer.firstQualificationDate, postOffer.lastQualificationDate, postOffer.eligibilityDuration, postOffer.eligibilityDurationUnit)
        }
    }

    private fun validateEventBasedOfferDates(displayDateStr: String?, startDateStr: String?, endDateStr: String?, firstQualificationDateStr: String, lastQualificationDateStr: String) {
        val firstQualificationDate = Instant.parse(firstQualificationDateStr)
        val lastQualificationDate = Instant.parse(lastQualificationDateStr)
        val today = LocalDate.now(ZoneOffset.UTC);
        val todayStartTime = today.atTime(0,0,0).toInstant(ZoneOffset.UTC);

        if(lastQualificationDate.isBefore(firstQualificationDate)){
            throw BadRequestException("Last qualification date cannot be before first qualification date.")
        }

        if(!StringUtils.isNullOrEmpty(endDateStr) && !StringUtils.isNullOrEmpty(startDateStr)
            && !StringUtils.isNullOrEmpty(displayDateStr)){
            // throw BadRequestException("Start date, end date and display date are not allowed for event based offers.");
            logger.info("Display date has been received for an event-based offer: $displayDateStr");
            logger.info("Start date has been received for an event-based offer: $startDateStr");
            logger.info("End date has been received for an event-based offer: $endDateStr");
        }
    }

    private fun validateOfferDates(putOffer: PutOfferIO) {
        if (putOffer.eventBasedOffer){
            validateEventBasedOfferDates(putOffer.displayDate, putOffer.startDate, putOffer.endDate, putOffer.firstQualificationDate!!, putOffer.lastQualificationDate!!)
        }else{
            validateOfferDates(putOffer.displayDate!!, putOffer.startDate!!, putOffer.endDate!!, putOffer.firstQualificationDate, putOffer.lastQualificationDate, putOffer.eligibilityDuration, putOffer.eligibilityDurationUnit)
        }
    }

    private fun validateOfferDates(bulkPostOffer: BulkPostOfferIO) {
        bulkPostOffer.offers.stream().forEach() {
            validateOfferDates(it)
            updateDates(it)
        }
    }

    private fun validateOfferDates(bulkPutOffer: BulkPutOfferIO) {
        bulkPutOffer.offers.stream().forEach() {
            validateOfferDates(it)
            updateDates(it)
        }
    }

    private infix fun Offer.validateUpdateTo(other: PutOfferIO) {
        fun err(message: String): BadRequestException{
            val exception = BadRequestException(message)
            logger.jsonError(
                    desc = LogEventEnum.Update.value,
                    throwable = exception,
                    value = object {
                        val message = exception.message
                        val storedOffer = this@validateUpdateTo
                        val putOffer = other
                    }
            )
            return exception
        }
        if (status != OfferStatus.DRAFT) {
            if (partnerId != UUID.fromString(other.partnerId)) {
                throw err("Offer partner may not be edited after publishing")
            }
            if( eventBasedOffer != other.eventBasedOffer){
                throw err("Updating the eventBasedOffer flag is not allowed after publishing")
            }
            if (massOffer != other.massOffer) {
                throw err("Offer may not be changed from mass to targeted or vice versa after publishing")
            }
        }
    }

    private fun validateOfferUniqueness(postOffer: PostOfferIO) {
        if (isUniquenessValidationNeeded(
                postOffer.sponsorCode,
                postOffer.issuanceCode,
                postOffer.partnerId,
                postOffer.awardType,
                postOffer.mechanisms
            )
        ) {

            validateOfferAvailabilityInDateRange(
                postOffer.startDate!!,
                postOffer.endDate!!,
                postOffer.sponsorCode,
                postOffer.issuanceCode
            )
        }
    }

    private fun validateOfferUniqueness(putOffer: PutOfferIO) {
        if (isUniquenessValidationNeeded(
                putOffer.sponsorCode,
                putOffer.issuanceCode,
                putOffer.partnerId,
                putOffer.awardType,
                putOffer.mechanisms
            )
        ) {
            validateOfferAvailabilityInDateRange(
                putOffer.startDate!!,
                putOffer.endDate!!,
                putOffer.sponsorCode,
                putOffer.issuanceCode,
                putOffer.id
            )
        }
    }

    private fun validateOfferUniqueness(bulkPostOffer: BulkPostOfferIO) {
        bulkPostOffer.offers.stream().forEach() { postOfferIO ->
            if (isUniquenessValidationNeeded(
                    postOfferIO.sponsorCode,
                    postOfferIO.issuanceCode,
                    postOfferIO.partnerId,
                    postOfferIO.awardType,
                    postOfferIO.mechanisms
                )
            ) {
                validateOfferAvailabilityInDateRange(
                    postOfferIO.startDate!!,
                    postOfferIO.endDate!!,
                    postOfferIO.sponsorCode,
                    postOfferIO.issuanceCode
                )
            }
        }
    }

    private fun validateOfferUniqueness(bulkPutOffer: BulkPutOfferIO) {
        bulkPutOffer.offers.stream().forEach() { putOfferIO ->
            if (isUniquenessValidationNeeded(
                    putOfferIO.sponsorCode,
                    putOfferIO.issuanceCode,
                    putOfferIO.partnerId,
                    putOfferIO.awardType,
                    putOfferIO.mechanisms

                )
            ) {
                validateOfferAvailabilityInDateRange(
                    putOfferIO.startDate!!,
                    putOfferIO.endDate!!,
                    putOfferIO.sponsorCode,
                    putOfferIO.issuanceCode,
                    putOfferIO.id
                )
            }
        }
    }


    private fun isUniquenessValidationNeeded(
        sponsorCode: String?,
        offerCode: String?,
        partnerId: String,
        awardType: AwardType,
        mechanisms: List<MechanismIO>
    ): Boolean {
        var resp = false
        if (validateAwardType(awardType) && validateSponsorCodeAndOfferCode(sponsorCode, offerCode)) {
            partnerService.getPartnerById(partnerId)?.let {
                    resp = isRice(it)
            }
        }
        return resp;
    }

    private fun validateAwardType(awardType: AwardType):Boolean{
        return awardType in setOf(AwardType.MultiplierMiles, AwardType.FlatMiles)
    }

    private fun validateSponsorCodeAndOfferCode(sponsorCode: String?, offerCode: String?):Boolean{
        return !sponsorCode.isNullOrBlank() && !offerCode.isNullOrBlank()
    }

    private fun isRice(partnerInfo: PartnerResponse): Boolean {
        return partnerInfo.type.any { it == INTERNAL_AIRMILES_CALCULATED }
    }

    private fun validateOfferAvailabilityInDateRange(startDate:String, endDate:String, sponsorCode:String?, offerCode:String?, offerId: String? = null) {

        val duplicatedOffers = offerRepo.countBysponsorCodeAndIssuanceCodebetweenDates(
            startFrom = TimeUtils.parseStringToLocalDateTime(startDate),
            endFrom = TimeUtils.parseStringToLocalDateTime(endDate),
            sponsorCode = sponsorCode,
            offerCode = offerCode,
            offerId = offerId?.let { UUID.fromString(it) }
        )
        logger.jsonInfo(
            desc = LogEventEnum.UniquenessOfferValidation.value,
            value = duplicatedOffers
        )
        if (duplicatedOffers >= MAX_OFFER_LIMIT) {
            throw BadRequestException("Offer code & sponsor code are not unique in the date range selected")
        }
    }

    fun validateCardType(offerContract: OfferContract) {

        val programType = offerContract.programType.value
        val cardTypes = offerContract.cardType?.map { it.value }?.toSet() ?: return

        if (programType == "cardlinked" && featureToggleConfig.featureToggleClo.toBoolean()) {
            val validCombinations: Set<Set<String>> = setOf(
                    setOf(CardType.NonBmoMastercard.value,CardType.BmoMastercard.value),
                    setOf(CardType.NonBmoMastercard.value, CardType.BmoMastercard.value, CardType.BmoDebit.value),
                    setOf(CardType.BmoDebit.value)
            )

            if (cardTypes !in validCombinations) {
                throw BadRequestException(
                        "Invalid cardType combination, Only allowed: (Non-BMO Mastercard + BMO Mastercard + BMO Debit), (Non-BMO Mastercard + BMO Mastercard), (BMO Debit))."
                )
            }

            if (cardTypes.isEmpty()) {
                throw BadRequestException("cardType is mandatory when programType is CardLinkedOffers.")
            }
        }
        if (programType != "cardlinked" && cardTypes.isNotEmpty()) {
            throw BadRequestException("cardType should be null or empty when programType is not CardLinkedOffers.")
        }
    }

    fun validateBMOPreApproval(offerContract: OfferContract) {
        // Valite if program type is "bmopreapp"
        if (offerContract.programType.value == ValidationConstants.PROGRAM_TYPE_BMO_PREAPP) {
            // Validate if partner is not Bank of Montreal
            if (offerContract.partnerId != partnetIdBankMontreal) {
                throw BadRequestException(ValidationConstants.ERROR_INVALID_PARTNER)
            }
            // Validate if mechanism include "OptIn"
            if (offerContract.mechanisms.none { it.mechanismType == MechanismType.OptIn }) {
                throw BadRequestException(ValidationConstants.ERROR_MISSING_OPTIN_MECHANISM)
            }
        }
    }

    fun validateCtaUrlCtaLabel(offerContract: OfferContract) {
        if (!offerContract.ctaUrl.isNullOrEmpty && offerContract.ctaLabel.isNullOrEmpty) {
            throw BadRequestException(ValidationConstants.ERROR_CTA_FIELDS_BOTH_MANDATORY)
        } else if (!offerContract.ctaLabel.isNullOrEmpty && offerContract.ctaUrl.isNullOrEmpty) {
            throw BadRequestException(ValidationConstants.ERROR_CTA_FIELDS_BOTH_MANDATORY)
        }
    }

    fun validateAMReceipt(offerContract: OfferContract) {
        val promoBoostId = "5d407839-7138-48d7-811f-557feccbe5b3";

        if (!featureToggleConfig.featureToggleAmReceipts.toBoolean()) return
        // Validate if program type is "amreceipts"
        if (offerContract.programType.value === "amreceipts") {
            when {
                offerContract.mechanisms.none { it.mechanismType == MechanismType.ScanReceipt } ->
                    throw BadRequestException(ValidationConstants.ERROR_MISSING_SCANRECEIPT_MECHANISM)
                promoBoostId !in offerContract.tags && offerContract.usageLimit === null ->
                    throw BadRequestException(ValidationConstants.ERROR_MISSING_USAGE_LIMIT)
                promoBoostId in offerContract.tags && offerContract.usageLimit !== null ->
                    throw BadRequestException(ValidationConstants.ERROR_USAGE_LIMIT_NOT_NEEDED)
                offerContract.tiers.size > 1 ->
                    throw BadRequestException(ValidationConstants.ERROR_TIERS_GREATER_THAN_ONE)
            }
        } else {
            when {
                offerContract.mechanisms.any { it.mechanismType == MechanismType.ScanReceipt } ->
                    throw BadRequestException(ValidationConstants.ERROR_SCANRECEIPT_MECHANISM_NOT_NEEDED)
                !offerContract.retailerGroupId.isNullOrEmpty() ->
                    throw BadRequestException(ValidationConstants.ERROR_RETAILER_GROUP_NOT_NEEDED)
                offerContract.usageLimit !== null ->
                    throw BadRequestException(ValidationConstants.ERROR_USAGE_LIMIT_NOT_NEEDED)
                offerContract.offerType === OfferType.Base ->
                    throw BadRequestException(ValidationConstants.ERROR_OFFER_TYPE_BASE)
                offerContract.qualifier in listOf(Qualifier.PerProduct, Qualifier.PerUnit, Qualifier.PerDollar, Qualifier.Frequency) ->
                    throw BadRequestException(ValidationConstants.ERROR_QUALIFIER_NOT_VALID)
            }
        }
    }
}