package com.loyalty.offermanagement.services.kinesis

import com.amazonaws.auth.AWSStaticCredentialsProvider
import com.amazonaws.auth.DefaultAWSCredentialsProviderChain
import com.amazonaws.auth.STSAssumeRoleSessionCredentialsProvider
import com.amazonaws.client.builder.AwsClientBuilder
import com.amazonaws.services.kinesis.AmazonKinesisClientBuilder
import com.amazonaws.services.kinesis.model.PutRecordsRequest
import com.amazonaws.services.kinesis.model.PutRecordsRequestEntry
import com.amazonaws.services.kinesis.model.PutRecordsResult
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.databind.SerializationFeature
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.loyalty.nova.common.logging.jsonError
import com.loyalty.offermanagement.models.v3.inout.OfferIO
import com.loyalty.offermanagement.novabus.NovaBusRepository
import com.loyalty.offermanagement.services.OfferTransformService
import com.loyalty.offermanagement.utils.LogEventEnum
import org.slf4j.Logger
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Component
import java.nio.ByteBuffer

@Component
class KinesisEventWriter {

    @Autowired
    private lateinit var offerTransformService: OfferTransformService

    @Autowired
    private lateinit var novaBusRepository: NovaBusRepository

    @Autowired
    private lateinit var logger: Logger

    @Autowired
    lateinit var objectMapper: ObjectMapper

    @Value("\${KinesisName}")
    lateinit var destinationStreamName: String

    @Value("\${KinesisRegion}")
    lateinit var destinationStreamRegion: String

    @Value("\${environment}")
    lateinit var environment: String

    @Value("\${KinesisAccountId}")
    lateinit var KinesisAccountId: String

    @Value("\${KinesisNameForUSEast}")
    lateinit var destinationStreamNameUE: String

    @Value("\${KinesisUSRegion}")
    lateinit var destinationStreamRegionUE: String

    @Value("\${KinesisUSEastAccountId}")
    lateinit var KinesisUSEastAccountId: String

    fun sendOfferToKinesis(offer: OfferIO, correlationId: String, region: String) {

        val eventData = offerTransformService.transformOfferV3(offer)
        val event = novaBusRepository.buildEvent(eventData, correlationId)

        logger.info("Pushing offers to kinesis: \n$event")
        val partitionKey = event.meta.partitionKey;

        // Create an STSAssumeRoleSessionCredentialsProvider to assume the IAM role in Account A(ca-central-1)


        // Configure the AmazonKinesis client to call the PutRecords API in Account A(ca-central-1)
        if(region=="ca-central-1") {

            val roleArn = "arn:aws:iam::${KinesisAccountId}:role/${environment}-nova-common-events-kinesis-role"
            logger.info("Kinesis rolearn:${roleArn}")

            // Create an STSAssumeRoleSessionCredentialsProvider to assume the IAM role in Account A(ca-central-1)

            val stsCredentialsProvider = STSAssumeRoleSessionCredentialsProvider.Builder(roleArn, "session-name")
                    .build().credentials

            val kinesisClient = AmazonKinesisClientBuilder.standard()
                    .withCredentials(AWSStaticCredentialsProvider(stsCredentialsProvider))
                    .withEndpointConfiguration(AwsClientBuilder.EndpointConfiguration("https://kinesis.${destinationStreamRegion}.amazonaws.com", "${destinationStreamRegion}"))
                    .build()

            logger.info("Kinesis Client: \n${kinesisClient}")


            logger.info("partitionKey : ${partitionKey}");
            val objectmapper = jacksonObjectMapper().apply {
                configure(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false)
                registerModule(JavaTimeModule())
            }

            val record = PutRecordsRequestEntry()
                    .withData(ByteBuffer.wrap(objectmapper.writeValueAsBytes(event)))
                    .withPartitionKey(partitionKey)

            // Create a PutRecordsRequest object to send data to the Kinesis stream
            val putRecordsRequest = PutRecordsRequest()
                    .withStreamName(destinationStreamName)
                    .withRecords(record)
            try {
                // Call the PutRecords API to send data to the Kinesis stream

                val putRecordsResult: PutRecordsResult = kinesisClient.putRecords(putRecordsRequest)
                logger.info("Successfully put record to kinesis: \n${putRecordsResult}")

            } catch (e: Throwable) {
                logger.jsonError(
                        desc = LogEventEnum.KinesisStream.value,
                        value = object {
                            val message = "Failed to put record to kinesis"
                        },
                        throwable = e
                )
            }
        }else if(region=="us-east-1") {
            //changes for us-east-1
            val roleArn = "arn:aws:iam::${KinesisUSEastAccountId}:role/${environment}-nova-common-events-kinesis-role"
            logger.info("Kinesis rolearn:${roleArn}")

            val kinesisClientForUE1 = AmazonKinesisClientBuilder.standard()
                    .withCredentials(DefaultAWSCredentialsProviderChain())
                    .build()
            logger.info("Kinesis Client for UE1: \n${kinesisClientForUE1}")

            val putRecordsRequest = PutRecordsRequest()
            putRecordsRequest.setStreamName(destinationStreamNameUE)
            val putRecordsRequestEntry = PutRecordsRequestEntry()
                    .withData(ByteBuffer.wrap(objectMapper.writeValueAsBytes(event)))
                    .withPartitionKey(partitionKey)
            try {
                putRecordsRequest.setRecords(listOf(putRecordsRequestEntry))
                kinesisClientForUE1.putRecords(putRecordsRequest)
                logger.info("Successfully put record to kinesis: \n${putRecordsRequest}")

            } catch (e: Throwable) {
                logger.jsonError(
                        desc = LogEventEnum.KinesisStream.value,
                        value = object {
                            val message = "Failed to put record to kinesis"
                        },
                        throwable = e
                )
            }
        }

    }
}

