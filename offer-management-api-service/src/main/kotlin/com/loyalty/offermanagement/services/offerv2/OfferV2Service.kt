package com.loyalty.offermanagement.services.offerv2

import com.loyalty.offermanagement.SpringConfig
import com.loyalty.offermanagement.models.offerv2.OfferCreateResponseV2
import com.loyalty.offermanagement.models.offerv2.OfferV2
import com.loyalty.offermanagement.models.v3.inout.IdIO
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.ResponseEntity
import org.springframework.stereotype.Service
import org.springframework.web.client.RestTemplate
import org.springframework.web.client.postForEntity

@Service
class OfferV2Service(val config: SpringConfig) {
    @Autowired
    private lateinit var restTemplate: RestTemplate

    fun create(offer: OfferV2): ResponseEntity<OfferCreateResponseV2> =
            restTemplate.postForEntity("${config.offerV2Endpoint}/$path", offer, OfferCreateResponseV2::class)

    fun update(offer: OfferV2) = restTemplate.put("${config.offerV2Endpoint}/$path", offer)

    fun publish(offerId: String): ResponseEntity<Any> {
        val body = IdIO(offerId)
        return restTemplate.postForEntity("${config.offerV2Endpoint}/$path/id", body, Any::class)
    }

    companion object {
        private const val path = "v1/offers"
    }
}
