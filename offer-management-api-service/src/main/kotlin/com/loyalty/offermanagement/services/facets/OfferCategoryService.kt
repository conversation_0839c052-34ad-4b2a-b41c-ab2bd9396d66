package com.loyalty.offermanagement.services.facets

import com.loyalty.offermanagement.SpringConfig
import com.loyalty.offermanagement.models.LocalizedString
import com.loyalty.offermanagement.models.facets.OfferCategory
import com.loyalty.offermanagement.models.facets.OfferCategoryResponse
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.stereotype.Service
import org.springframework.web.client.HttpClientErrorException
import org.springframework.web.client.RestTemplate

@Service
class OfferCategoryService(val config: SpringConfig) {
    @Autowired
    private lateinit var restTemplate: RestTemplate

    @Throws(OfferCategoryServiceException::class)
    fun getCategoryInfo(item: String): LocalizedString? {
        try {
            // Fetch all categories using the wrapper class
            val responseEntity: ResponseEntity<OfferCategoryResponse> =
                restTemplate.getForEntity("${config.offerFacetsEndpoint}/v1/categories", OfferCategoryResponse::class.java)

            if (responseEntity.statusCode == HttpStatus.OK) {

                val category = responseEntity.body?.results?.find { it.id == item }
                    ?: responseEntity.body?.results?.flatMap { it.childCategories }
                        ?.find { it.id == item }

                return category?.translations
                    ?: throw OfferCategoryServiceException("Category with ID $item not found")
            } else {
                throw OfferCategoryServiceException("Failure on finding category $item")
            }
        } catch (e: HttpClientErrorException) {
            throw OfferCategoryServiceException(
                message = "CategoriesService - Exception body: ${e.responseBodyAsString} and parameter: $item",
                cause = e
            )
        }
    }
}
