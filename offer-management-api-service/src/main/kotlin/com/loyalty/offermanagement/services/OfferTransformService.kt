@file:Suppress(
        "ComplexMethod",
        "LargeClass",
        "LongMethod",
        "NestedBlockDepth",
        "ThrowsCount",
        "TooGenericExceptionThrown",
        "TooManyFunctions",
        "UnsafeCallOnNullableType"
)

package com.loyalty.offermanagement.services

import com.fasterxml.jackson.databind.ObjectMapper
import com.loyalty.nova.common.events.definitions.data.v3.OfferPublishedEventData
import com.loyalty.offermanagement.entities.Content
import com.loyalty.offermanagement.entities.Integration
import com.loyalty.offermanagement.entities.Mechanism
import com.loyalty.offermanagement.entities.Offer
import com.loyalty.offermanagement.entities.OfferContent
import com.loyalty.offermanagement.entities.Tier
import com.loyalty.offermanagement.models.LocalizedString
import com.loyalty.offermanagement.models.isNullOrEmpty
import com.loyalty.offermanagement.models.offerv2.AwardTypeV2
import com.loyalty.offermanagement.models.offerv2.ImageRootV2
import com.loyalty.offermanagement.models.offerv2.ImageV2
import com.loyalty.offermanagement.models.offerv2.LocalizedArrayOfString
import com.loyalty.offermanagement.models.offerv2.LocalizedImageV2
import com.loyalty.offermanagement.models.offerv2.MechanismTypeV2
import com.loyalty.offermanagement.models.offerv2.OfferV2
import com.loyalty.offermanagement.models.offerv2.RedemptionV2
import com.loyalty.offermanagement.models.offerv2.RegionV2
import com.loyalty.offermanagement.models.offerv2.SpendTypeV2
import com.loyalty.offermanagement.models.v3.Availability
import com.loyalty.offermanagement.models.v3.*
import com.loyalty.offermanagement.models.v3.inout.ContentDetailsIO
import com.loyalty.offermanagement.models.v3.inout.IntegrationIO
import com.loyalty.offermanagement.models.v3.inout.MechanismIO
import com.loyalty.offermanagement.models.v3.inout.OfferIO
import com.loyalty.offermanagement.models.v3.inout.TierIO
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import java.time.LocalDateTime
import java.time.ZoneId
import java.time.ZoneOffset
import java.util.UUID
import com.loyalty.nova.common.events.definitions.data.v3.models.Availability as V3Availability
import com.loyalty.nova.common.events.definitions.data.v3.models.AwardType as V3AwardType
import com.loyalty.nova.common.events.definitions.data.v3.models.ContentDetails as V3ContentDetails
import com.loyalty.nova.common.events.definitions.data.v3.models.Image as V3Image
import com.loyalty.nova.common.events.definitions.data.v3.models.ImageProps as V3ImageProps
import com.loyalty.nova.common.events.definitions.data.v3.models.Integration as V3Integration
import com.loyalty.nova.common.events.definitions.data.v3.models.LocalizedString as V3LocalizedString
import com.loyalty.nova.common.events.definitions.data.v3.models.Mechanism as V3Mechanism
import com.loyalty.nova.common.events.definitions.data.v3.models.MechanismType as V3MechanismType
import com.loyalty.nova.common.events.definitions.data.v3.models.OfferLimitation as V3OfferLimitation
import com.loyalty.nova.common.events.definitions.data.v3.models.OfferType as V3OfferType
import com.loyalty.nova.common.events.definitions.data.v3.models.ProgramType as V3ProgramType
import com.loyalty.nova.common.events.definitions.data.v3.models.Qualifier as V3Qualifier
import com.loyalty.nova.common.events.definitions.data.v3.models.Region as V3Region
import com.loyalty.nova.common.events.definitions.data.v3.models.Tier as V3Tier
import com.loyalty.nova.common.events.definitions.data.v3.models.EligibilityDurationUnit as V3EligibilityDurationUnit
import com.loyalty.nova.common.events.definitions.data.v3.models.CardType as V3CardType
@Service
class OfferTransformService {
    @Autowired
    private lateinit var mapper: ObjectMapper

    companion object {
        const val imageV2Title = "@2x"
        const val imageENContentType = "image/jpeg"
        const val imageENFileName = "enUS"
        const val imageFRContentType = "image/jpeg"
        const val imageFRFileName = "frCA"
    }

    fun transform(offer: Offer): OfferIO {
        val offerContent = mapper.readValue(offer.offerContent, OfferContent::class.java)

        return OfferIO(
                id = offer.id.toString(),
                createdAt = offer.createdAt,
                partnerId = offer.partnerId.toString(),
                partnerName = offer.partnerName,
                partnerBaseEarnRate = offerContent.partnerBaseEarnRate,
                baseCashRedemption = offerContent.baseCashRedemption,
                displayDate = offer.displayDate?.toInstant(ZoneOffset.UTC),
                startDate = offer.startDate?.toInstant(ZoneOffset.UTC),
                endDate = offer.endDate?.toInstant(ZoneOffset.UTC),
                offerType = offer.offerType,
                qualifier = offer.qualifier,
                awardType = offer.awardType,
                issuanceCode = offer.issuanceCode,
                image = LocalizedImage(
                        enUS = ImageProps(
                                path = offer.imageEN
                        ),
                        frCA = ImageProps(
                                path = offer.imageFR
                        )
                ),
                mechanisms = offer.mechanisms.map {
                    MechanismIO(
                            mechanismType = it.mechanismType,
                            mechanismLabel = it.mechanismLabelEN?.let { it1 ->
                                it.mechanismLabelFR?.let { it2 ->
                                    LocalizedString(
                                            enUS = it1,
                                            frCA = it2
                                    )
                                }
                            },
                            mechanismTitle = it.mechanismTitleEN?.let { it1 ->
                                it.mechanismTitleFR?.let { it2 ->
                                    LocalizedString(
                                            enUS = it1,
                                            frCA = it2
                                    )
                                }
                            },
                            mechanismText = it.mechanismTextEN?.let { it1 ->
                                it.mechanismTextFR?.let { it2 ->
                                    LocalizedString(
                                            enUS = it1,
                                            frCA = it2
                                    )
                                }
                            },
                            mechanismValue = it.mechanismValueEN?.let { it1 ->
                                it.mechanismValueFR?.let { it2 ->
                                    LocalizedString(
                                            enUS = it1,
                                            frCA = it2
                                    )
                                }
                            }
                    )
                },
                integrations = offer.integrations.map {
                    IntegrationIO(
                            systemName = it.systemName,
                            systemCode = it.systemCode
                    )
                },
                cashierInstruction = offer.cashierInstructionEN?.let { it_e ->
                    offer.cashierInstructionFR?.let { it_f ->
                        LocalizedString(
                                enUS = it_e,
                                frCA = it_f
                        )
                    }
                },
                tiers = offer.tiers.map {
                    TierIO(
                            awardValue = it.awardValue,
                            qualifierValue = it.qualifierValue,
                            qualifierFrequency = it.qualifierFrequency,
                            awardLong = LocalizedString(
                                    enUS = it.awardLongEN,
                                    frCA = it.awardLongFR
                            ),
                            qualifierLong = LocalizedString(
                                    enUS = it.qualifierLongEN,
                                    frCA = it.qualifierLongFR
                            ),
                            content = it.content.map { j ->
                                ContentDetailsIO(
                                        enUS = j.labelEN,
                                        frCA = j.labelFR,
                                        productSKU = j.productSKU,
                                        skus = j.skus.toList(),
                                        masterProduct = j.masterProduct,
                                        upc = j.upc
                                )
                            }.toList()
                    )
                },
                displayPriority = offer.displayPriority,
                regions = offer.regions.map {
                    Region.valueOf(it.toString())
                },
                offerLimitation = offerContent.offerLimitation,
                offerLimitationText = offerContent.offerLimitationText,
                includedLocations = offerContent.includedLocations,
                excludedLocations = offerContent.excludedLocations,
                includedBanners = offerContent.includedBanners,
                excludedBanners = offerContent.excludedBanners,
                canBeCombined = offerContent.canBeCombined,
                combinationsText = offerContent.combinationsText,
                exclusions = offerContent.exclusions,
                availability = offer.availability.map {
                    Availability.valueOf(it.toString())
                },
                tags = offer.tags.toList(),
                partnerUrl = offerContent.partnerUrl,
                daysToApply = offerContent.daysToApply,
                trademarkInfo = offerContent.trademarkInfo,
                description = offer.descriptionEN?.let {
                    offer.descriptionFR?.let { it1 ->
                        LocalizedString(
                                enUS = it,
                                frCA = it1
                        )
                    }
                },
                partnerLegalName = offerContent.partnerLegalName,
                hasCustomLegal = offer.hasCustomLegal,
                legalText = LocalizedString(
                        enUS = offer.legalTextEN,
                        frCA = offer.legalTextFR
                ),
                createdBy = offer.createdBy,
                updatedAt = offer.updatedAt,
                updatedBy = offer.updatedBy,
                publishedAt = offer.publishedAt,
                publishedBy = offer.publishedBy,
                status = offer.status,
                duplicatedFrom = offer.duplicatedFrom?.toString(),
                contentfulId = offerContent.contentfulId,
                detailsId = offerContent.detailsId,
                bulkId = offer.batch?.id?.toString(),
                bulkName = offer.batch?.batchName,
                awardShort = LocalizedString(
                        enUS = offer.awardShortEN,
                        frCA = offer.awardShortFR
                ),
                qualifierShort = LocalizedString(
                        enUS = offer.qualifierShortEN,
                        frCA = offer.qualifierShortFR
                ),
                massOffer = offer.massOffer,
                active = offer.active,
                offerCategory1 = offer.offerCategory1,
                offerCategory2 = offer.offerCategory2,
                offerCategory3 = offer.offerCategory3,
                productName = offer.productName,
                productBrand = offer.productBrand,
                campaignCode = offer.campaignCode,
                programPriority = offer.programPriority,
                partnerOfferId = offer.partnerOfferId,
                programType = offer.programType,
                ctaLabel = offer.ctaLabelEN?.let { it_e ->
                    offer.ctaLabelFR?.let { it_f ->
                        LocalizedString(
                            enUS = it_e,
                            frCA = it_f
                        )
                    }
                },
                ctaUrl = offer.ctaUrlEN?.let { it_e ->
                    offer.ctaUrlFR?.let { it_f ->
                        LocalizedString(
                            enUS = it_e,
                            frCA = it_f
                        )
                    }
                },
                sponsorCode = offer.sponsorCode,
                eventBasedOffer = offer.eventBasedOffer,
                eligibilityDuration = offer.eligibilityDuration,
                firstQualificationDate = offer.firstQualificationDate?.toInstant(ZoneOffset.UTC),
                lastQualificationDate = offer.lastQualificationDate?.toInstant(ZoneOffset.UTC),
                eligibilityDurationUnit = offer.eligibilityDurationUnit,
                cardType = offer.cardType,
                retailerGroupId = offer.retailerGroupId,
                usageLimit = offer.usageLimit
        )
    }

    fun transform(offer: OfferIO): Offer {
        return Offer(
                id = UUID.fromString(offer.id),
                status = offer.status,
                createdAt = offer.createdAt,
                createdBy = offer.createdBy,
                updatedAt = offer.updatedAt,
                updatedBy = offer.updatedBy,
                publishedAt = offer.publishedAt,
                publishedBy = offer.publishedBy,
                partnerId = UUID.fromString(offer.partnerId),
                displayDate = offer.displayDate?.let { instant -> LocalDateTime.ofInstant(instant, ZoneOffset.UTC).withNano(0) },
                startDate = offer.startDate?.let { instant -> LocalDateTime.ofInstant(instant, ZoneOffset.UTC).withNano(0) },
                endDate = offer.endDate?.let { instant -> LocalDateTime.ofInstant(instant, ZoneOffset.UTC).withNano(0) },
                offerType = offer.offerType,
                qualifier = offer.qualifier,
                awardType = offer.awardType,
                displayPriority = offer.displayPriority,
                duplicatedFrom = if (offer.duplicatedFrom != null) UUID.fromString(offer.duplicatedFrom) else null,
                partnerName = offer.partnerName,
                issuanceCode = offer.issuanceCode,
                partnerOfferId = offer.partnerOfferId,
                offerContent = mapper.writeValueAsString(OfferContent(
                        detailsId = offer.detailsId,
                        contentfulId = offer.contentfulId,
                        partnerBaseEarnRate = offer.partnerBaseEarnRate,
                        baseCashRedemption = offer.baseCashRedemption,
                        offerLimitation = offer.offerLimitation,
                        offerLimitationText = offer.offerLimitationText,
                        includedLocations = offer.includedLocations,
                        excludedLocations = offer.excludedLocations,
                        includedBanners = offer.includedBanners,
                        excludedBanners = offer.excludedBanners,
                        canBeCombined = offer.canBeCombined,
                        combinationsText = offer.combinationsText,
                        exclusions = offer.exclusions,
                        partnerUrl = offer.partnerUrl,
                        daysToApply = offer.daysToApply,
                        trademarkInfo = offer.trademarkInfo,
                        partnerLegalName = offer.partnerLegalName
                )),
                awardShortEN = offer.awardShort?.enUS!!,
                awardShortFR = offer.awardShort?.frCA!!,
                qualifierShortEN = offer.qualifierShort?.enUS!!,
                qualifierShortFR = offer.qualifierShort?.frCA!!,
                legalTextEN = offer.legalText?.enUS!!,
                legalTextFR = offer.legalText?.frCA!!,
                imageEN = if (offer.image.isNullOrEmpty) getOfferDefaultImage(offer).enUS.path else offer.image?.enUS!!.path,
                imageFR = if (offer.image.isNullOrEmpty) getOfferDefaultImage(offer).frCA.path else offer.image?.frCA!!.path,
                descriptionEN = offer.description?.enUS,
                descriptionFR = offer.description?.frCA,
                cashierInstructionEN = offer.cashierInstruction?.enUS,
                cashierInstructionFR = offer.cashierInstruction?.frCA,
                hasCustomLegal = offer.hasCustomLegal,
                contentVersion = 0,
                regions = offer.regions.toMutableSet(),
                availability = offer.availability.toMutableSet(),
                tags = offer.tags.toHashSet(),
                massOffer = offer.massOffer,
                active = offer.active,
                offerCategory1 = offer.offerCategory1,
                offerCategory2 = offer.offerCategory2,
                offerCategory3 = offer.offerCategory3,
                productName = offer.productName,
                productBrand = offer.productBrand,
                campaignCode = offer.campaignCode,
                programPriority = offer.programPriority,
                programType = offer.programType,
                ctaLabelEN = offer.ctaLabel?.enUS,
                ctaLabelFR = offer.ctaLabel?.frCA,
                ctaUrlEN = offer.ctaUrl?.enUS,
                ctaUrlFR = offer.ctaUrl?.frCA,
                sponsorCode = offer.sponsorCode,
                eventBasedOffer = offer.eventBasedOffer,
                eligibilityDuration = offer.eligibilityDuration,
                firstQualificationDate = offer.firstQualificationDate?.let { instant -> LocalDateTime.ofInstant(instant, ZoneOffset.UTC).withNano(0) },
                lastQualificationDate = offer.lastQualificationDate?.let { instant -> LocalDateTime.ofInstant(instant, ZoneOffset.UTC).withNano(0) },
                eligibilityDurationUnit = offer.eligibilityDurationUnit
            ).apply {
            this.mechanisms = offer.mechanisms.map {
                Mechanism(
                        mechanismType = it.mechanismType,
                        mechanismLabelEN = it.mechanismLabel?.enUS,
                        mechanismLabelFR = it.mechanismLabel?.frCA,
                        mechanismValueEN = it.mechanismValue?.enUS,
                        mechanismValueFR = it.mechanismValue?.frCA,
                        mechanismTitleEN = it.mechanismTitle?.enUS,
                        mechanismTitleFR = it.mechanismTitle?.frCA,
                        mechanismTextEN = it.mechanismText?.enUS,
                        mechanismTextFR = it.mechanismText?.frCA,
                        offer = this
                )
            }
            this.integrations = offer.integrations.map {
                Integration(
                        systemName = it.systemName,
                        systemCode = it.systemCode,
                        offer = this
                )
            }
            this.tiers = offer.tiers.map {
                Tier(
                        awardValue = it.awardValue,
                        qualifierValue = it.qualifierValue,
                        qualifierFrequency = it.qualifierFrequency,
                        qualifierLongEN = it.qualifierLong?.enUS!!,
                        qualifierLongFR = it.qualifierLong?.frCA!!,
                        awardLongEN = it.awardLong?.enUS!!,
                        awardLongFR = it.awardLong?.frCA!!,
                        content = it.content.map { content ->
                            Content(
                                    labelEN = content.enUS,
                                    labelFR = if (content.frCA === "") content.enUS else content.frCA,
                                    productSKU = content.productSKU,
                                    skus = content.skus.toMutableList(),
                                    upc = content.upc,
                                    masterProduct = content.masterProduct as Boolean
                            )
                        }.toList(),
                        offer = this
                ).apply {
                    this.content.map { content ->
                        content.tier = this
                    }
                }
            }
            cardType = offer.cardType?.map { CardType.fromValue(it.value) } ?: emptyList()
            retailerGroupId = offer.retailerGroupId;
            usageLimit = offer.usageLimit;
        }
    }

    @Deprecated(message = "Offer V2 is not being used anymore. All related classes will be removed in a future ticket")
    fun transformOfferV2(offer: OfferIO): OfferV2 {
        val redemption = offer.tiers.map {
            val awardLong = it.awardLong
            val qualifierLong = it.qualifierLong
            val spendType = when (offer.qualifier) {
                Qualifier.Product -> SpendTypeV2.Quantity
                Qualifier.StoreWide, Qualifier.Category -> SpendTypeV2.Dollar
                Qualifier.CashRedemption, Qualifier.CashDiscount -> SpendTypeV2.CashMiles
                Qualifier.Custom -> SpendTypeV2.Custom
                Qualifier.Fuel -> SpendTypeV2.Fuel
                Qualifier.PerProduct -> SpendTypeV2.PerProduct
                Qualifier.PerUnit -> SpendTypeV2.PerUnit
                Qualifier.PerDollar -> SpendTypeV2.PerDollar
                Qualifier.Frequency -> SpendTypeV2.PerDollar
            }
            val awardType = when (offer.awardType) {
                AwardType.FlatMiles -> AwardTypeV2.MilesFlat
                AwardType.MultiplierMiles -> AwardTypeV2.MilesMultiplier
                AwardType.FlatDiscount -> AwardTypeV2.DollarsOff
                AwardType.PercentDiscount -> AwardTypeV2.PercentDiscount
                AwardType.CashDiscount -> AwardTypeV2.CashDiscount
                AwardType.Custom -> AwardTypeV2.Custom
            }
            val spendAmount = it.qualifierValue ?: 0f
            val value = it.awardValue ?: 0f

            // TODO redemptionV2 value and spendAmount is int while in TierIO, qualifierValue and awardValue is float

            RedemptionV2(
                    awardLong = awardLong!!,
                    qualifierLong = qualifierLong!!,
                    spendType = spendType,
                    awardType = awardType,
                    spendAmount = spendAmount.toInt(),
                    value = value.toInt()
            )
        }.sortedBy { it.value }

        val mechanismTypeIO = offer.mechanisms.first().mechanismType
        val mechanismValueIO = offer.mechanisms.first().mechanismValue

        val mechanismType = when (mechanismTypeIO) {
            MechanismType.NoAction -> MechanismTypeV2.NoAction
            MechanismType.Button -> MechanismTypeV2.Button
            MechanismType.PLU -> MechanismTypeV2.PLU
            MechanismType.CouponCode -> MechanismTypeV2.PLU
            MechanismType.LoadGo -> MechanismTypeV2.LoadGo
            MechanismType.BarcodeUPC, MechanismType.BarcodeEAN,
            MechanismType.BarcodeCODE39, MechanismType.BarcodeCODE128 -> MechanismTypeV2.Barcode
            MechanismType.OptIn, MechanismType.ScanReceipt -> throw Exception("$mechanismTypeIO not available for OfferV2")
        }

        val mechanismValue: Any? = when (mechanismTypeIO) {
            MechanismType.Button, MechanismType.CouponCode -> mechanismValueIO
            MechanismType.BarcodeUPC, MechanismType.BarcodeEAN, MechanismType.BarcodeCODE39,
            MechanismType.BarcodeCODE128, MechanismType.PLU -> mechanismValueIO?.enUS
            else -> null
        }

        val barcodeType = when (mechanismTypeIO) {
            MechanismType.BarcodeUPC, MechanismType.BarcodeEAN,
            MechanismType.BarcodeCODE39, MechanismType.BarcodeCODE128 ->
                mechanismTypeIO.value.split("barcode").last().toLowerCase()
            else -> null
        }

        val buttonTitle = when (mechanismTypeIO) {
            MechanismType.Button -> offer.mechanisms.first().mechanismLabel
            else -> null
        }

        val header = LocalizedString(
                enUS = "${offer.awardShort?.enUS} , ${offer.qualifierShort?.enUS}",
                frCA = "${offer.awardShort?.frCA} , ${offer.qualifierShort?.frCA}"
        )

        val image = ImageRootV2().apply {
            this.title = imageV2Title
            this.file = LocalizedImageV2(
                    enUS = ImageV2(
                            contentType = imageENContentType,
                            fileName = imageENFileName,
                            link = if (offer.image.isNullOrEmpty) getOfferDefaultImage(offer).enUS.path else offer.image?.enUS!!.path
                    ),
                    frCA = ImageV2(
                            contentType = imageFRContentType,
                            fileName = imageFRFileName,
                            link = if (offer.image.isNullOrEmpty) getOfferDefaultImage(offer).frCA.path else offer.image?.frCA!!.path
                    )
            )
        }

        return OfferV2().apply {
            this.id = offer.contentfulId
            this.detailsId = offer.detailsId
            this.partnerId = offer.partnerId
            this.offerCode = offer.issuanceCode
            this.displayDate = offer.displayDate.toString()
            this.startDate = offer.startDate.toString()
            this.endDate = offer.endDate.toString()
            this.priority = offer.displayPriority
            this.tags = offer.tags
            this.redemption = redemption
            this.mechanismType = mechanismType
            this.mechanismValue = mechanismValue
            this.barcodeType = barcodeType
            this.buttonTitle = buttonTitle
            this.region = offer.regions.map { RegionV2.valueOf(it.value) }
            this.qualifierShort = offer.qualifierShort
            this.awardShort = offer.awardShort
            this.header = header
            this.termsAndCond = offer.legalText
            this.image = image
            this.description = offer.description
            if (!offer.offerPromotionLabel.isNullOrEmpty) {
                this.labels = LocalizedArrayOfString(
                        enUS = mutableListOf("${offer.offerPromotionLabel?.enUS}"),
                        frCA = mutableListOf("${offer.offerPromotionLabel?.frCA}")
                )
            }
        }
    }

    fun transformOfferV3(offer: OfferIO): OfferPublishedEventData {
        val convertedCardType = offer.cardType?.map{
            V3CardType.valueOf(it.toString())
        } ?: emptyList()
        var cardTypeValue: List<V3CardType>? = null
        if(offer.programType.toString() == "cardlinked" && convertedCardType.isNotEmpty())
        {
            cardTypeValue = convertedCardType
        }
        else{null}
        val basedEventData = OfferPublishedEventData(
            id = UUID.fromString(offer.id),
            displayDate = offer.displayDate
                ?.let { instant -> LocalDateTime.ofInstant(instant, ZoneId.of("UTC")) }
                ?: throw Exception("${offer::displayDate.name} cannot be null"),
            startDate = offer.startDate
                ?.let { instant -> LocalDateTime.ofInstant(instant, ZoneId.of("UTC")) }
                ?: throw Exception("${offer::startDate.name} cannot be null"),
            endDate = offer.endDate
                ?.let { instant -> LocalDateTime.ofInstant(instant, ZoneId.of("UTC")) }
                ?: throw Exception("${offer::endDate.name} cannot be null"),
            displayPriority = offer.displayPriority,
            partnerId = offer.partnerId,
            partnerName = offer.partnerName,
            partnerBaseEarnRate = offer.partnerBaseEarnRate,
            baseCashRedemption = offer.baseCashRedemption,
            partnerOfferId = offer.partnerOfferId,
            createdAt = offer.createdAt,
            createdBy = offer.createdBy,
            updatedAt = offer.updatedAt,
            updatedBy = offer.updatedBy,
            publishedAt = offer.publishedAt ?: throw Exception("${offer::publishedAt.name} cannot be null"),
            publishedBy = offer.publishedBy ?: throw Exception("${offer::publishedBy.name} cannot be null"),
            offerType = V3OfferType.valueOf(offer.offerType.toString()),
            qualifier = V3Qualifier.valueOf(offer.qualifier.toString()),
            awardType = V3AwardType.valueOf(offer.awardType.toString()),
            awardShort = offer.awardShort?.fromIOToV3()
                ?: throw Exception("${offer::awardShort.name} not nullable"),
            qualifierShort = offer.qualifierShort?.fromIOToV3()
                ?: throw Exception("${offer::qualifierShort.name} not nullable"),
            legalText = offer.legalText?.fromIOToV3() ?: throw Exception("${offer::legalText.name} not nullable"),
            image = if (offer.image.isNullOrEmpty) getOfferDefaultImage(offer).fromIOToV3() else offer.image!!.fromIOToV3(),
            regions = regionsfromIOToV3(offer.regions),
            issuanceCode = offer.issuanceCode,
            cashierInstruction = offer.cashierInstruction?.fromIOToV3(),
            tiers = tiersfromIOToV3(offer.tiers),
            mechanisms = mechanismfromIOToV3(offer.mechanisms),
            integrations = offer.integrations.map { mapper.convertValue(it, V3Integration::class.java) },
            description = offer.description?.fromIOToV3(),
            availability = availabilityfromIOToV3(offer.availability),
            tags = offer.tags,
            massOffer = offer.massOffer,
            active = offer.active,
            offerCategory1 = offer.offerCategory1,
            offerCategory1Label = offer.offerCategory1Label?.fromIOToV3(),
            offerCategory2 = offer.offerCategory2,
            offerCategory2Label = offer.offerCategory2Label?.fromIOToV3(),
            offerCategory3 = offer.offerCategory3,
            offerCategory3Label = offer.offerCategory3Label?.fromIOToV3(),
            promotionLabel = offer.offerPromotionLabel?.fromIOToV3(),
            productName = offer.productName,
            productBrand = offer.productBrand,
            programPriority = offer.programPriority,
            offerLimitation = V3OfferLimitation.valueOf(offer.offerLimitation.toString()),
            offerLimitationText = offer.offerLimitationText?.fromIOToV3(),
            includedLocations = offer.includedLocations.map {
                it.fromIOToV3()
            }.toList(),
            excludedLocations = offer.excludedLocations.map {
                it.fromIOToV3()
            }.toList(),
            includedBanners = offer.includedBanners.map {
                it.fromIOToV3()
            }.toList(),
            excludedBanners = offer.excludedBanners.map {
                it.fromIOToV3()
            }.toList(),
            canBeCombined = offer.canBeCombined,
            combinationsText = offer.combinationsText?.fromIOToV3(),
            exclusions = offer.exclusions?.fromIOToV3(),
            partnerUrl = offer.partnerUrl?.fromIOToV3(),
            daysToApply = offer.daysToApply,
            trademarkInfo = offer.trademarkInfo?.fromIOToV3(),
            partnerLegalName = offer.partnerLegalName?.fromIOToV3(),
            hasCustomLegal = offer.hasCustomLegal,
            duplicatedFrom = offer.duplicatedFrom,
            contentfulId = offer.contentfulId,
            detailsId = offer.detailsId,
            bulkId = offer.bulkId,
            bulkName = offer.bulkName,
            campaignCode = offer.campaignCode,
            programType = V3ProgramType.valueOf(offer.programType.toString()),
            ctaLabel = offer.ctaLabel?.fromIOToV3(),
            ctaUrl = offer.ctaUrl?.fromIOToV3(),
            sponsorCode = offer.sponsorCode,
            eventBasedOffer = offer.eventBasedOffer,
            eligibilityDuration = offer.eligibilityDuration,
            firstQualificationDate = offer.firstQualificationDate?.let { instant -> LocalDateTime.ofInstant(instant, ZoneId.of("UTC")) },
            lastQualificationDate = offer.lastQualificationDate?.let { instant -> LocalDateTime.ofInstant(instant, ZoneId.of("UTC")) },
            eligibilityDurationUnit = offer.eligibilityDurationUnit?.let{V3EligibilityDurationUnit.valueOf(it.toString())},
            retailerGroupId = offer.retailerGroupId,
            usageLimit = offer.usageLimit
        )
        return if(cardTypeValue.isNullOrEmpty()){
            basedEventData
        }
        else{
            basedEventData.copy(cardType=cardTypeValue)
        }
    }
}

// helpers
fun LocalizedString.fromIOToV3(): V3LocalizedString = V3LocalizedString(
        enUS = this.enUS,
        frCA = this.frCA
)

fun ImageProps.fromIOToV3(): V3ImageProps = V3ImageProps(
        path = this.path
)

fun LocalizedImage.fromIOToV3(): V3Image = V3Image(
        enUS = this.enUS.fromIOToV3(),
        frCA = this.frCA.fromIOToV3()
)

fun getOfferDefaultImage(offerIO: OfferIO): LocalizedImage {
    val imagePath = System.getenv().get("OFFER_DEFAULT_IMAGE_BASE") + offerIO.qualifier.toString() + ".png"
    return LocalizedImage(ImageProps(imagePath), ImageProps(imagePath))
}

fun regionsfromIOToV3(regions: List<Region>): List<V3Region> =
        regions.map {
            V3Region.valueOf(it.toString())
        }.toList()

fun availabilityfromIOToV3(availabilities: List<Availability>): List<V3Availability> =
        availabilities.map {
            V3Availability.valueOf(it.toString())
        }.toList()

fun tiersfromIOToV3(tiers: List<TierIO>): List<V3Tier> = tiers.map {
    V3Tier(
            awardValue = it.awardValue,
            qualifierValue = it.qualifierValue,
            awardLong = it.awardLong?.fromIOToV3()!!,
            qualifierLong = it.qualifierLong?.fromIOToV3()!!,
            content = contentDetailsfromIOToV3(it.content),
            qualifierFrequency = it.qualifierFrequency
    )
}

fun contentDetailsfromIOToV3(contents: List<ContentDetailsIO>): List<V3ContentDetails> = contents.map { content ->
    return@map V3ContentDetails(
            label = V3LocalizedString(enUS = content.enUS, frCA = content.frCA),
            productSKU = content.productSKU,
            skus = content.skus,
            upc = content.upc,
            masterProduct = content.masterProduct
    )
}.toList()

fun mechanismfromIOToV3(mechanism: List<MechanismIO>): List<V3Mechanism> = mechanism.map {
    V3Mechanism(
            mechanismType = V3MechanismType.valueOf(it.mechanismType.toString()),
            mechanismLabel = it.mechanismLabel?.fromIOToV3(),
            mechanismText = it.mechanismText?.fromIOToV3(),
            mechanismTitle = it.mechanismTitle?.fromIOToV3(),
            mechanismValue = it.mechanismValue?.fromIOToV3()
    )
}

fun cardTypefromIOToV3(cardType: List<CardType>): List<V3CardType> =
    cardType.map {
        V3CardType.valueOf(it.toString())
    }.toList()