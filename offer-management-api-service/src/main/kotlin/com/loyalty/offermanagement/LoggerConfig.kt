package com.loyalty.offermanagement

import com.loyalty.offermanagement.utils.AccessLogEnhancerFilter
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.InjectionPoint
import org.springframework.boot.web.servlet.FilterRegistrationBean
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.context.annotation.Scope

@Configuration
class LoggerConfig {
    @Bean
    fun loggingFilter(): FilterRegistrationBean<MdcLogEnhancerFilter> {
        val registrationBean = FilterRegistrationBean<MdcLogEnhancerFilter>()
        registrationBean.order = Integer.MAX_VALUE - 1
        registrationBean.filter = MdcLogEnhancerFilter()
        return registrationBean
    }

    @Bean
    fun accessLogging(): FilterRegistrationBean<AccessLogEnhancerFilter> {
        val registrationBean = FilterRegistrationBean<AccessLogEnhancerFilter>()
        registrationBean.filter = AccessLogEnhancerFilter()
        return registrationBean
    }

    @Bean
    @Scope(value = "prototype")
    fun exposeLogger(injectionPoint: InjectionPoint): Logger =
            LoggerFactory.getLogger(injectionPoint.member.declaringClass.name)
}
