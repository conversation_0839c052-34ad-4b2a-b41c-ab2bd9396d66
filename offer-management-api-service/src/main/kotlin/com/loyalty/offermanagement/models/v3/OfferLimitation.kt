package com.loyalty.offermanagement.models.v3

import com.fasterxml.jackson.annotation.JsonProperty

enum class OfferLimitation constructor(val value: String) {
    @JsonProperty("noLimit")
    NoLimit("noLimit"),

    @JsonProperty("perCollector")
    PerCollection("perCollector"),

    @JsonProperty("perCollectorPerTransaction")
    PerCollectorPerTransaction("perCollectorPerTransaction"),

    @JsonProperty("perCollectorPerDay")
    PerCollectorPerDayY("perCollectorPerDay"),

    @JsonProperty("custom")
    Custom("custom"),
}
