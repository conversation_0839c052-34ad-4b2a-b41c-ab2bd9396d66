package com.loyalty.offermanagement.models

import com.loyalty.offermanagement.models.v3.*
import org.springframework.format.annotation.DateTimeFormat
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID
import java.util.regex.Pattern
import javax.validation.Constraint
import javax.validation.ConstraintValidator
import javax.validation.ConstraintValidatorContext
import javax.validation.Payload
import kotlin.reflect.KClass

data class OfferCriteria(
    val id: Set<UUID>? = null,

    @field:UserListConstraint
        val user: Set<String>? = null,

    val partnerId: Set<UUID>? = null,

    val bulkId: Set<UUID>? = null,

    val offerType: Set<OfferTypeCriteria>? = null,

    val tags: Set<String>? = null,

    val awardType: Set<AwardTypeCriteria>? = null,

    val qualifier: Set<QualifierCriteria>? = null,

    val mechanismType: Set<MechanismTypeCriteria>? = null,

    val regions: Set<Region>? = null,

    var status: Set<OfferStatusCriteria>? = null,

    val massOffer: Boolean? = null,

    var active: Boolean? = null,

    val offerCategory1: Set<String>? = null,

    val offerCategory2: Set<String>? = null,

    val offerCategory3: Set<String>? = null,

    var dateContext: DateContextType? = null,

        // "yyyy-MM-dd"
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE)
        val displayDate: LocalDate? = null,

    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE)
        val endDate: LocalDate? = null,

    val onlyEmptyCategory: Boolean? = null,

    val hasIssuanceCode: Boolean? = null,

    val campaignCode: Set<String>? = null,

    val programType: Set<ProgramTypeCriteria>? = null,

    val eventbasedOffer: Boolean? = null,

    var eligibilityDuration: Int? = null,

    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE)
    val firstQualificationDate: LocalDateTime? = null,

    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE)
    val lastQualificationDate: LocalDateTime? = null,

    var eligibilityDurationUnit: Set<EligibilityDurationUnitCriteria>? = null
)

enum class DateContextType {
    live,
    staged,
    expired,
    notExpired
}

enum class OfferTypeCriteria(val type: OfferType) {
    buy(OfferType.Buy),
    spend(OfferType.Spend),
    base(OfferType.Base),
    amCashEarn(OfferType.AmCashEarn),
    amCashDiscount(OfferType.AmCashDiscount),
    custom(OfferType.Custom)
}

enum class AwardTypeCriteria(val type: AwardType) {
    flatMiles(AwardType.FlatMiles),
    multiplierMiles(AwardType.MultiplierMiles),
    flatDiscount(AwardType.FlatDiscount),
    percentDiscount(AwardType.PercentDiscount),
    cashDiscount(AwardType.CashDiscount),
    custom(AwardType.Custom)
}

enum class QualifierCriteria(val qualifier: Qualifier) {
    product(Qualifier.Product),
    storewide(Qualifier.StoreWide),
    category(Qualifier.Category),
    cashRedemption(Qualifier.CashRedemption),
    cashDiscount(Qualifier.CashDiscount),
    custom(Qualifier.Custom),
    fuel(Qualifier.Fuel)
}

enum class OfferStatusCriteria(val status: OfferStatus) {
    draft(OfferStatus.DRAFT),
    updated(OfferStatus.UPDATED),
    published(OfferStatus.PUBLISHED)
}

enum class ProgramTypeCriteria(val type: ProgramType) {
    traditionalcore(ProgramType.traditionalcore),
    airmilesshops(ProgramType.airmilesshops),
    cardlinked(ProgramType.cardlinked),
    bmopreapp(ProgramType.bmopreapp),
    amreceipts(ProgramType.amreceipts)
}

enum class EligibilityDurationUnitCriteria(val unit: EligibilityDurationUnit) {
    days(EligibilityDurationUnit.DAYS),
    hours(EligibilityDurationUnit.HOURS),
    minutes(EligibilityDurationUnit.MINUTES)
}

enum class MechanismTypeCriteria(val mechanismType: MechanismType) {
    noAction(MechanismType.NoAction),
    barcodeUPC(MechanismType.BarcodeUPC),
    barcodeEAN(MechanismType.BarcodeEAN),
    barcodeCODE39(MechanismType.BarcodeCODE39),
    barcodeCODE128(MechanismType.BarcodeCODE128),
    plu(MechanismType.PLU),
    couponCode(MechanismType.CouponCode),
    button(MechanismType.Button),
    loadGo(MechanismType.LoadGo),
    optIn(MechanismType.OptIn),
    scanReceipt(MechanismType.ScanReceipt)
}

@Constraint(validatedBy = [UserListConstraintValidator::class])
@Target(AnnotationTarget.FIELD)
@Retention(AnnotationRetention.RUNTIME)
annotation class UserListConstraint(
        val message: String = "Invalid user identifier, valid email required",
        val groups: Array<KClass<*>> = [],
        val payload: Array<KClass<out Payload>> = [])

class UserListConstraintValidator : ConstraintValidator<UserListConstraint, Collection<String>> {
    override fun isValid(value: Collection<String>?, context: ConstraintValidatorContext?): Boolean {
        var valid = true
        value?.forEach {
            if (!isEmailValid(it)) {
                valid = false
                return@forEach
            }
        }
        return valid
    }
}

fun isEmailValid(email: String): Boolean {
    return Pattern.compile(
            "^[A-Z0-9._%+-]+@[A-Z0-9.-]+\\.[A-Z]{2,6}$", Pattern.CASE_INSENSITIVE
    ).matcher(email).matches()
}
