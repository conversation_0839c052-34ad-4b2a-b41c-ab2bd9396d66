package com.loyalty.offermanagement.models.offerv2

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.annotation.JsonProperty
import com.fasterxml.jackson.annotation.JsonRootName
import com.loyalty.offermanagement.models.Localized

@JsonRootName("offerio")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
data class LocalizedImageV2(
        @JsonProperty("en-US")
        override var enUS: ImageV2,

        @JsonProperty("fr-CA")
        override var frCA: ImageV2
) : Localized<ImageV2>
