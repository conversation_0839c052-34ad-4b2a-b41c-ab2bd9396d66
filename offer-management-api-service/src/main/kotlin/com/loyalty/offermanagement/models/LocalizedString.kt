package com.loyalty.offermanagement.models

import com.fasterxml.jackson.annotation.JsonIgnore
import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.annotation.JsonProperty
import com.loyalty.offermanagement.models.v3.FrenchFallback

@JsonInclude(JsonInclude.Include.NON_NULL)
data class LocalizedString(
        @JsonProperty("en-US")
        override var enUS: String,

        @JsonProperty("fr-CA")
        override var frCA: String = ""
) : Localized<String?>, EmptyComparable, FrenchFallback {
    override fun fallback() {
        if (frCA.isEmpty()) {
            frCA = enUS
        }
    }

    @JsonIgnore
    override fun isEmpty(): Boolean = enUS.isEmpty() && frCA.isEmpty()
}

val EmptyComparable?.isNullOrEmpty: Boolean
    get() = this?.isEmpty() ?: true
