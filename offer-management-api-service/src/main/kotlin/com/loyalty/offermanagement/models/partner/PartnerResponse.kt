package com.loyalty.offermanagement.models.partner

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.annotation.JsonProperty
import java.time.LocalDateTime

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
data class PartnerResponse(
    @JsonProperty("name")
    val name: String,

    @JsonProperty("sponsorCodes")
    val sponsorCodes: List<String>,

    @JsonProperty("type")
    val type: List<String>,

    @JsonProperty("fullLogo")
    val fullLogo: List<Logo>?,

    @JsonProperty("baseEarnRateShort")
    val baseEarnRateShort: String?,

    @JsonProperty("baseEarnRate")
    val baseEarnRate: String?,

    @JsonProperty("regions")
    val regions: List<String>?,

    @JsonProperty("priority")
    val priority: Int?,

    @JsonProperty("getMilesFeatured")
    val getMilesFeatured: Boolean?,

    @JsonProperty("heartLogo")
    val heartLogo: List<Logo>?,

    @JsonProperty("partnerCategory")
    val partnerCategory: List<String>?,

    @JsonProperty("categoryPriority")
    val categoryPriority: Int?,

    @JsonProperty("partnerUrl")
    val partnerUrl: String?,

    @JsonProperty("betaPartner")
    val betaPartner: Boolean?,

    @JsonProperty("id")
    val id: String?,

    @JsonProperty("createdAt")
    val createdAt: LocalDateTime?,

    @JsonProperty("updatedAt")
    val updatedAt: LocalDateTime?,

    @JsonProperty("revision")
    val revision: Int?,

    @JsonProperty("terms")
    val terms: String?
)

data class Logo(
    @JsonProperty("title")
    val title: String?,

    @JsonProperty("description")
    val description: String?,

    @JsonProperty("file")
    val file: File?
)

data class File(
    @JsonProperty("url")
    val url: String?,

    @JsonProperty("details")
    val details: Details?,

    @JsonProperty("fileName")
    val fileName: String?,

    @JsonProperty("contentType")
    val contentType: String?
)

data class Details(
    @JsonProperty("size")
    val size: Int?,

    @JsonProperty("image")
    val image: Image?
)

data class Image(
    @JsonProperty("width")
    val width: Int?,

    @JsonProperty("height")
    val height: Int?
)
