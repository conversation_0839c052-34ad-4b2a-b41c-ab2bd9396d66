package com.loyalty.offermanagement.models.offerv2

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.annotation.JsonRootName
import com.loyalty.offermanagement.models.LocalizedString

@JsonRootName("offerio")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
data class OfferV2(
        var id: String? = null,
        var detailsId: String? = null,
        var partnerId: String? = null,
        var offerCode: String? = null,
        var displayDate: String? = null,
        var startDate: String? = null,
        var endDate: String? = null,
        var priority: Int? = null,
        var tags: List<String> = emptyList(),
        var redemption: List<RedemptionV2> = emptyList(),
        var mechanismType: MechanismTypeV2? = null,
        var mechanismValue: Any? = null,
        var barcodeType: String? = null,
        var buttonTitle: LocalizedString? = null,
        var region: List<RegionV2> = emptyList(),
        var qualifierShort: LocalizedString? = null,
        var awardShort: LocalizedString? = null,
        var header: LocalizedString? = null,
        var termsAndCond: LocalizedString? = null,
        var image: ImageRootV2? = null,
        var description: LocalizedString? = null,
        var labels: LocalizedArrayOfString? = null
)
