@file:Suppress(
        "VariableNaming",
        "SerialVersionUIDInSerializableClass"
)

package com.loyalty.offermanagement.models.v3

import com.fasterxml.jackson.annotation.JsonIgnore
import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.annotation.JsonProperty
import com.loyalty.offermanagement.models.EmptyComparable
import com.loyalty.offermanagement.models.Localized

@JsonInclude(JsonInclude.Include.NON_NULL)
data class LocalizedImage(
        @JsonProperty("en-US")
        override var enUS: ImageProps,

        @JsonProperty("fr-CA")
        override var frCA: ImageProps = ImageProps()
) : EmptyComparable, Localized<ImageProps?>, FrenchFallback {
    override fun fallback() {
        if (frCA.path.isEmpty()) {
            frCA.path = enUS.path
        }
    }

    @JsonIgnore
    override fun isEmpty(): Boolean = enUS.isEmpty() && frCA.isEmpty()
}
