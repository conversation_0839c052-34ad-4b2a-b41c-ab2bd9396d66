package com.loyalty.offermanagement.models.v3

import com.fasterxml.jackson.annotation.JsonProperty

enum class MechanismType constructor(val value: String) {
    @JsonProperty("noAction")
    NoAction("noAction"),

    @JsonProperty("barcodeUPC")
    BarcodeUPC("barcodeUPC"),

    @JsonProperty("barcodeEAN")
    BarcodeEAN("barcodeEAN"),

    @<PERSON>sonProperty("barcodeCODE39")
    BarcodeCODE39("barcodeCODE39"),

    @JsonProperty("barcodeCODE128")
    BarcodeCODE128("barcodeCODE128"),

    @JsonProperty("plu")
    PLU("plu"),

    @JsonProperty("couponCode")
    CouponCode("couponCode"),

    @JsonProperty("button")
    Button("button"),

    @JsonProperty("load+go")
    LoadGo("load+go"),

    @JsonProperty("optIn")
    OptIn("optIn"),

    @Json<PERSON>roperty("scanReceipt")
    ScanReceipt("scanReceipt")
}
