package com.loyalty.offermanagement.models.offerv2

import com.fasterxml.jackson.annotation.JsonProperty

enum class RegionV2 constructor(val value: String) {
    @JsonProperty("bc")
    BC("BC"),

    @JsonProperty("ab")
    AB("AB"),

    @JsonProperty("sk")
    SK("SK"),

    @JsonProperty("mb")
    MB("MB"),

    @JsonProperty("on")
    ON("ON"),

    @JsonProperty("qc")
    QC("QC"),

    @JsonProperty("nb")
    NB("NB"),

    @JsonProperty("pe")
    PE("PE"),

    @JsonProperty("ns")
    NS("NS"),

    @JsonProperty("nl")
    NL("NL"),

    @JsonProperty("yt")
    YT("YT"),

    @JsonProperty("nt")
    NT("NT"),

    @JsonProperty("nu")
    NU("NU"),

    @JsonProperty("tb")
    TB("TB")
}
