package com.loyalty.offermanagement.models.offerv2

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.annotation.JsonRootName
import com.loyalty.offermanagement.models.LocalizedString

@JsonRootName("offerio")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
data class RedemptionV2(
        var awardLong: LocalizedString,
        var qualifierLong: LocalizedString,
        var spendType: SpendTypeV2,
        var awardType: AwardTypeV2,
        var spendAmount: Int,
        var value: Int
)
