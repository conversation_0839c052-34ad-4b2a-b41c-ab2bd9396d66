package com.loyalty.offermanagement.models.offerv2

import com.fasterxml.jackson.annotation.JsonProperty

enum class SpendTypeV2 constructor(val value: String) {
    @JsonProperty("Quantity")
    Quantity("Quantity"),

    @JsonProperty("Dollar")
    Dollar("Dollar"),

    @JsonProperty("CashMiles")
    CashMiles("CashMiles"),

    @JsonProperty("Custom")
    Custom("Custom"),

    @JsonProperty("Fuel")
    Fuel("Fuel"),

    @JsonProperty("perProduct")
    PerProduct("perProduct"),

    @JsonProperty("perUnit")
    PerUnit("perUnit"),

    @JsonProperty("perDollar")
    PerDollar("perDollar"),

    @JsonProperty("frequency")
    Frequency("frequency")
}
