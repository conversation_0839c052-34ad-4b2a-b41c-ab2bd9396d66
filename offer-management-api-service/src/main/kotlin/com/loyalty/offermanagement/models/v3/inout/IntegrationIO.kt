package com.loyalty.offermanagement.models.v3.inout

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.annotation.JsonRootName
import com.loyalty.offermanagement.models.v3.ExternalSystem

@JsonRootName("offerio")
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
data class IntegrationIO(
        var systemName: ExternalSystem,
        var systemCode: String
)
