package com.loyalty.offermanagement.models.offerv2

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.annotation.JsonProperty

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
data class ResponseOfferV2(
        var id: String? = null,

        @JsonProperty("en-US")
        var enUS: SystemObjV2? = null
)
