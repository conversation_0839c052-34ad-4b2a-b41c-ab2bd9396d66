package com.loyalty.offermanagement.models.v3.inout

import com.fasterxml.jackson.annotation.JsonFormat
import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.annotation.JsonProperty
import java.time.Instant

@JsonInclude(JsonInclude.Include.NON_NULL)
data class Batch<PERSON>ob<PERSON>(
        @JsonProperty
        var id: String,

        @JsonProperty
        var name: String,

        @JsonProperty
        var partnerId: String,

        @JsonProperty
        var partnerName: String,

        @JsonProperty
        var totalOffers: Int,

        @JsonProperty
        @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss[.SSS]XXX", timezone = "UTC")
        var createdAt: Instant,

        @JsonProperty
        var createdBy: String,

        @JsonProperty
        @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss[.SSS]XXX", timezone = "UTC")
        var updatedAt: Instant? = null,

        @JsonProperty
        var updatedBy: String? = null
)
