package com.loyalty.offermanagement.models.v3

import com.fasterxml.jackson.annotation.JsonProperty

/** ENUM type for Offer Qualifiers  */
enum class Qualifier constructor(val value: String) {
    @JsonProperty("product")
    Product("product"),

    @JsonProperty("storewide")
    StoreWide("storewide"),

    @JsonProperty("category")
    Category("category"),

    @JsonProperty("cashRedemption")
    CashRedemption("cashRedemption"),

    @JsonProperty("cashDiscount")
    CashDiscount("cashDiscount"),

    @JsonProperty("custom")
    Custom("custom"),

    @JsonProperty("fuel")
    Fuel("fuel"),

    @JsonProperty("perProduct")
    PerProduct("perProduct"),

    @JsonProperty("perUnit")
    PerUnit("perUnit"),

    @JsonProperty("perDollar")
    PerDollar("perDollar"),

    @JsonProperty("frequency")
    Frequency("frequency")
}
