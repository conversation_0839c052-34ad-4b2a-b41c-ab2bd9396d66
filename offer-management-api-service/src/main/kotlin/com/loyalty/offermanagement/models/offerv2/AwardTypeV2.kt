package com.loyalty.offermanagement.models.offerv2

import com.fasterxml.jackson.annotation.JsonProperty

enum class AwardTypeV2 constructor(val value: String) {
    @JsonProperty("Miles Flat")
    MilesFlat("Miles Flat"),

    @JsonProperty("Miles Multiplier")
    MilesMultiplier("Miles Multiplier"),

    @JsonProperty("Dollars Off")
    DollarsOff("Dollars Off"),

    @JsonProperty("Percent Discount")
    PercentDiscount("Percent Discount"),

    @JsonProperty("Cash Discount")
    CashDiscount("Cash Discount"),

    @JsonProperty("Cents Per Litre")
    CentsPerLitre("Cents Per Litre"),

    @JsonProperty("Custom")
    Custom("Custom")
}
