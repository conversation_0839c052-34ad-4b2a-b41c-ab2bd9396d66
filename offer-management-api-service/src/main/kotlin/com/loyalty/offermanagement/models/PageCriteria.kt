@file:Suppress(
        "MagicNumber"
)

package com.loyalty.offermanagement.models

import org.springframework.data.domain.Sort
import javax.validation.constraints.Max
import javax.validation.constraints.Min

data class PageCriteria(

        @field:Min(0)
        val page: Int = 0,

        @field:Min(1)
        @field:Max(100)
        val size: Int = 100,

        val sort: SortBy = SortBy.createdAt,

        val direction: Direction? = Direction.desc
)

enum class SortBy {
    createdAt,
    partnerName,
    createdBy,
    publishedAt,
    updatedAt,
    startDate,
    endDate,
    displayPriority,
    region
}

enum class Direction(val sortDir: Sort.Direction) {
    asc(Sort.Direction.ASC),
    desc(Sort.Direction.DESC)
}
