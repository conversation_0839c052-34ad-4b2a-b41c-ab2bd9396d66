package com.loyalty.offermanagement.models.offerv2

import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.annotation.JsonProperty
import com.loyalty.offermanagement.models.Localized

@JsonInclude(JsonInclude.Include.NON_NULL)
data class LocalizedArrayOfString(
        @JsonProperty("en-US")
        override var enUS: List<String> = mutableListOf(),

        @JsonProperty("fr-CA")
        override var frCA: List<String> = mutableListOf()
) : Localized<List<String>>
