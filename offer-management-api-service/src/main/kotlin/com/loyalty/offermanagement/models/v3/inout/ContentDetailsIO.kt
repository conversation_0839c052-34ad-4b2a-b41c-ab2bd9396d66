@file:Suppress(
        "SerialVersionUIDInSerializableClass",
        "UnusedPrivateMember"
)

package com.loyalty.offermanagement.models.v3.inout

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.annotation.JsonProperty
import com.loyalty.nova.common.events.definitions.data.v3.models.LocalizedString
import java.io.Serializable

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
data class ContentDetailsIO(
        // because of backward compatibility we are not using label with localizedString
    @JsonProperty("en-US")
        var enUS: String,
    @JsonProperty("fr-CA")
        var frCA: String = "",

    val productSKU: String? = null,
    var skus: List<String> = emptyList(),
    val upc: String? = null,
    val masterProduct: Boolean? = false
) : Serializable {
    private companion object {
        private val translation = LocalizedString(enUS = "")
    }
}
