package com.loyalty.offermanagement.models.v3

import com.fasterxml.jackson.annotation.JsonProperty

enum class OfferType constructor(val value: String) {
    @JsonProperty("buy")
    Buy("buy"),

    @JsonProperty("spend")
    Spend("spend"),

    @JsonProperty("base")
    Base("base"),

    @JsonProperty("amCashEarn")
    AmCashEarn("amCashEarn"),

    @JsonProperty("amCashDiscount")
    AmCashDiscount("amCashDiscount"),

    @JsonProperty("custom")
    Custom("custom")
}
