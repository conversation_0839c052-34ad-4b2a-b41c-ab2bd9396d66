package com.loyalty.offermanagement.models.v3.inout

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.annotation.JsonRootName
import com.loyalty.offermanagement.models.LocalizedString
import com.loyalty.offermanagement.models.v3.FrenchFallback

@JsonRootName("postofferio")
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
data class TierIO(
        var awardValue: Float? = null,
        var qualifierValue: Float? = null,
        var awardLong: LocalizedString? = null,
        var qualifierLong: LocalizedString? = null,
        var content: List<ContentDetailsIO> = emptyList(),
        var qualifierFrequency: Int = 1
) : FrenchFallback {
    override fun fallback() {
        awardLong?.fallback()
        qualifierLong?.fallback()
    }
}
