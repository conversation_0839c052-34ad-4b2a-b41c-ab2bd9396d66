@file:Suppress(
        "LongMethod"
)

package com.loyalty.offermanagement.models.v3.inout

import com.fasterxml.jackson.annotation.JsonFormat
import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.annotation.JsonPropertyOrder
import com.fasterxml.jackson.annotation.JsonRootName
import com.loyalty.offermanagement.models.LocalizedString
import com.loyalty.offermanagement.models.v3.*
import java.time.Instant
import java.time.LocalDateTime
import java.util.UUID

@JsonRootName("offerio")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonPropertyOrder(alphabetic = true)
data class OfferIO(
    var id: String = UUID.randomUUID().toString(),

    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss[.SSS]XXX", timezone = "UTC")
        var createdAt: Instant = Instant.now(),

    var partnerId: String,
    var partnerName: String,
    var partnerBaseEarnRate: Int? = null,
    var baseCashRedemption: Int = 95,
    var partnerOfferId: String? = null,

    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss[.SSS]XXX", timezone = "UTC")
        var displayDate: Instant? = null,

    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss[.SSS]XXX", timezone = "UTC")
        var startDate: Instant? = null,

    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss[.SSS]XXX", timezone = "UTC")
        var endDate: Instant? = null,

    var offerType: OfferType,
    var qualifier: Qualifier,
    var awardType: AwardType,
    var issuanceCode: String? = null,
    var image: LocalizedImage?,
    var mechanisms: List<MechanismIO>,
    var integrations: List<IntegrationIO> = emptyList(),
    var cashierInstruction: LocalizedString? = null,
    var tiers: List<TierIO>,
    var displayPriority: Int = 0,
    var regions: List<Region>,
    var offerLimitation: OfferLimitation = OfferLimitation.NoLimit,
    var offerLimitationText: LocalizedString? = null,
    var includedLocations: List<LocalizedString> = emptyList(),
    var excludedLocations: List<LocalizedString> = emptyList(),
    var includedBanners: List<LocalizedString> = emptyList(),
    var excludedBanners: List<LocalizedString> = emptyList(),
    var canBeCombined: Boolean = true,
    var combinationsText: LocalizedString? = null,
    var exclusions: LocalizedString? = null,
    var availability: List<Availability> = emptyList(),
    var tags: List<String> = emptyList(),
    var partnerUrl: LocalizedString? = null,
    var daysToApply: Int? = null,
    var trademarkInfo: LocalizedString? = null,
    var description: LocalizedString? = null,
    var partnerLegalName: LocalizedString? = null,
    var hasCustomLegal: Boolean = false,
    var legalText: LocalizedString? = null,
    var createdBy: String = "",

    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss[.SSS]XXX", timezone = "UTC")
        var updatedAt: Instant? = null,

    var updatedBy: String? = null,

    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss[.SSS]XXX", timezone = "UTC")
        var publishedAt: Instant? = null,

    var publishedBy: String? = null,
    var status: OfferStatus = OfferStatus.DRAFT,
    var duplicatedFrom: String? = null,
    var contentfulId: String? = null,
    var detailsId: String? = null,
    var bulkId: String? = null,
    var bulkName: String? = null,
    var awardShort: LocalizedString? = null,
    var qualifierShort: LocalizedString? = null,
    var massOffer: Boolean = true,
    var active: Boolean = true,
    val offerCategory1: String? = null,
    var offerCategory1Label: LocalizedString? = null,
    val offerCategory2: String? = null,
    var offerCategory2Label: LocalizedString? = null,
    val offerCategory3: String? = null,
    var offerCategory3Label: LocalizedString? = null,
    var offerPromotionLabel: LocalizedString? = null,
    var productName: String? = null,
    var productBrand: String? = null,
    var programPriority: Int = 0,
    var campaignCode: String? = null,
    var programType: ProgramType,
    var ctaLabel: LocalizedString? = null,
    var ctaUrl: LocalizedString? = null,
    var sponsorCode: String? = null,
    var eventBasedOffer: Boolean = false,
    var eligibilityDuration: Int? = null,
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss[.SSS]XXX", timezone = "UTC")
    var firstQualificationDate: Instant? = null,
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss[.SSS]XXX", timezone = "UTC")
    var lastQualificationDate: Instant? = null,
    var eligibilityDurationUnit: EligibilityDurationUnit? = null,
    var cardType: List<CardType>? = emptyList(),
    var retailerGroupId: List<UUID>? = null,
    var usageLimit: Int? = null
) : FrenchFallback {
    override fun fallback() {
        image?.fallback()
        mechanisms.fallback()
        cashierInstruction?.fallback()
        tiers.fallback()
        offerLimitationText?.fallback()
        includedLocations.fallback()
        excludedLocations.fallback()
        includedBanners.fallback()
        excludedBanners.fallback()
        combinationsText?.fallback()
        exclusions?.fallback()
        partnerUrl?.fallback()
        trademarkInfo?.fallback()
        description?.fallback()
        partnerLegalName?.fallback()
        legalText?.fallback()
        awardShort?.fallback()
        qualifierShort?.fallback()
        offerCategory1Label?.fallback()
        offerCategory2Label?.fallback()
        offerCategory3Label?.fallback()
        // offerPromotionLabel?.fallback() note: fallback covered as special case in transformer
    }
}
