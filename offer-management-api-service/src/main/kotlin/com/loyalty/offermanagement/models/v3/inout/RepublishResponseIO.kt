package com.loyalty.offermanagement.models.v3.inout

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.annotation.JsonRootName

@JsonRootName("offerio")
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
data class RepublishResponseIO(
        var totalCount: Long,
        var successCount: Long,
        var failureCount: Long
)
