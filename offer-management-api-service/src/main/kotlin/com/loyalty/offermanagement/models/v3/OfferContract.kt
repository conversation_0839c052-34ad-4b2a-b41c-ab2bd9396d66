package com.loyalty.offermanagement.models.v3

import com.loyalty.offermanagement.models.LocalizedString
import com.loyalty.offermanagement.models.v3.inout.MechanismIO
import com.loyalty.offermanagement.models.v3.inout.TierIO
import java.util.*

interface OfferContract {
    val programType: ProgramType
    val cardType: List<CardType>?
    val offerType: OfferType
    val partnerId: String
    var mechanisms: List<MechanismIO>
    var ctaUrl: LocalizedString?
    var ctaLabel: LocalizedString?
    var retailerGroupId: List<UUID>?
    var tags: List<String>
    var usageLimit: Int?
    var qualifier: Qualifier
    var tiers: List<TierIO>
}