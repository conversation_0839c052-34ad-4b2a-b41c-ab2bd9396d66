package com.loyalty.offermanagement.models.offerv2

import com.fasterxml.jackson.annotation.JsonProperty

@Deprecated(message = "Offer V2 is not being used anymore. All related classes will be removed in a future ticket")
enum class MechanismTypeV2 constructor(val value: String) {
    @JsonProperty("noAction")
    NoAction("noAction"),

    @JsonProperty("barcode")
    Barcode("barcode"),

    @JsonProperty("plu")
    PLU("plu"),

    @JsonProperty("button")
    Button("button"),

    @JsonProperty("load+go")
    LoadGo("load+go")
}
