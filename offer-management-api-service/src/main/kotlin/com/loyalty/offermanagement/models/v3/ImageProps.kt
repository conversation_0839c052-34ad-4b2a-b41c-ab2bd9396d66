@file:Suppress(
        "SerialVersionUIDInSerializableClass"
)

package com.loyalty.offermanagement.models.v3

import com.fasterxml.jackson.annotation.JsonIgnore
import com.fasterxml.jackson.annotation.JsonInclude
import com.loyalty.offermanagement.models.EmptyComparable
import java.io.Serializable

@JsonInclude(JsonInclude.Include.NON_NULL)
data class ImageProps(
        var path: String = ""
) : Serializable, EmptyComparable {
    @JsonIgnore
    override fun isEmpty(): Boolean = path.isEmpty()
}
