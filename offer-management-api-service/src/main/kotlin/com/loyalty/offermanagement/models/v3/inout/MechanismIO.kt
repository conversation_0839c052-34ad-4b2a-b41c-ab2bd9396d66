package com.loyalty.offermanagement.models.v3.inout

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.annotation.JsonRootName
import com.loyalty.offermanagement.models.LocalizedString
import com.loyalty.offermanagement.models.v3.FrenchFallback
import com.loyalty.offermanagement.models.v3.MechanismType
import org.slf4j.LoggerFactory

@JsonRootName("postofferio")
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
data class MechanismIO(
        var mechanismType: MechanismType,
        var mechanismLabel: LocalizedString? = null,
        var mechanismTitle: LocalizedString? = null,
        var mechanismText: LocalizedString? = null,
        var mechanismValue: LocalizedString? = null
) : FrenchFallback {
    private val logger by lazy { LoggerFactory.getLogger(this::class.java) }

    companion object {
        val mechanismTypeWithEnglishAndFrench = listOf(
                MechanismType.Button,
                MechanismType.CouponCode
        )

        val mechanismTypeWithEnglishOnly = listOf(
                MechanismType.BarcodeCODE39,
                MechanismType.BarcodeCODE128,
                MechanismType.BarcodeUPC,
                MechanismType.BarcodeEAN,
                MechanismType.PLU
        )

        val mechanismTypeWithNoMechanismValue = listOf(
                MechanismType.NoAction,
                MechanismType.LoadGo,
                MechanismType.OptIn,
                MechanismType.ScanReceipt
        )
    }

    override fun fallback() {
        mechanismLabel?.fallback()
        mechanismTitle?.fallback()
        mechanismText?.fallback()
        when {
            mechanismTypeWithEnglishOnly.contains(mechanismType) ->
                mechanismValue?.frCA = mechanismValue?.enUS ?: ""
            mechanismTypeWithNoMechanismValue.contains(mechanismType) ->
                mechanismValue = null
            mechanismTypeWithEnglishAndFrench.contains(mechanismType) ->
                mechanismValue?.fallback()
            else ->
                this.logger.error("Invalid Mechanism Type: $mechanismType")
        }
    }
}
