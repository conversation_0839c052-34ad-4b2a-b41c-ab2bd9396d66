package com.loyalty.offermanagement.models.v3

import com.fasterxml.jackson.annotation.JsonProperty

enum class ProgramType constructor(val value: String) {
    @JsonProperty("traditionalcore")
    traditionalcore("traditionalcore"),

    @JsonProperty("airmilesshops")
    airmilesshops("airmilesshops"),

    @JsonProperty("cardlinked")
    cardlinked("cardlinked"),

    @JsonProperty("bmopreapp")
    bmopreapp("bmopreapp"),

    @JsonProperty("amreceipts")
    amreceipts("amreceipts")
}
