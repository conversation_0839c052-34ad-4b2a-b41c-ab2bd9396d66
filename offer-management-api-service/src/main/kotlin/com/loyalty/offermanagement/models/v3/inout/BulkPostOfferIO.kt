package com.loyalty.offermanagement.models.v3.inout

import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.annotation.JsonRootName
import com.loyalty.offermanagement.models.v3.FrenchFallback
import com.loyalty.offermanagement.utils.BULK_OFFER_IO_OFFERS_MAX_SIZE
import com.loyalty.offermanagement.utils.BULK_OFFER_IO_OFFERS_MIN_SIZE
import com.loyalty.offermanagement.utils.BULK_OFFER_IO_OFFERS_SIZE_ERROR_MESSAGE
import javax.validation.constraints.Size

@JsonRootName("bulkpostoffer")
@JsonInclude(JsonInclude.Include.NON_NULL)
data class BulkPostOfferIO(
        var bulkName: String,

        @field:Size(min = BULK_OFFER_IO_OFFERS_MIN_SIZE,
            max = BULK_OFFER_IO_OFFERS_MAX_SIZE,
            message = BULK_OFFER_IO_OFFERS_SIZE_ERROR_MESSAGE)
        var offers: List<PostOfferIO>
) : FrenchFallback {
    override fun fallback() {
        this.offers.map {
            it.fallback()
        }
    }
}
