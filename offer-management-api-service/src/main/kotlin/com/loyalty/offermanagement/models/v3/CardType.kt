package com.loyalty.offermanagement.models.v3;

import com.fasterxml.jackson.annotation.JsonProperty;

enum class CardType constructor(val value: String) {
    @JsonProperty("NonBmoMastercard")
    NonBmoMastercard("NonBmoMastercard"),

    @<PERSON>sonProperty("BmoMastercard")
    BmoMastercard("BmoMastercard"),

    @JsonProperty("BmoDebit")
    BmoDebit("BmoDebit");

    companion object {
        fun fromValue(value: String): CardType {
            return values().find { it.value == value }
                    ?: throw IllegalArgumentException("No enum constant for value: $value")
        }
    }
}
