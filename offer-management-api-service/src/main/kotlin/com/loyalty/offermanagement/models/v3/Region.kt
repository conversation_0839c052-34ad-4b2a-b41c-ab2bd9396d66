package com.loyalty.offermanagement.models.v3

import com.fasterxml.jackson.annotation.JsonProperty

enum class Region constructor(val value: String) {
    @JsonProperty("BC")
    BC("BC"),

    @JsonProperty("AB")
    AB("AB"),

    @JsonProperty("SK")
    SK("SK"),

    @JsonProperty("MB")
    MB("MB"),

    @JsonProperty("ON")
    ON("ON"),

    @JsonProperty("QC")
    QC("QC"),

    @JsonProperty("NB")
    NB("NB"),

    @JsonProperty("PE")
    PE("PE"),

    @JsonProperty("NS")
    NS("NS"),

    @JsonProperty("NL")
    NL("NL"),

    @JsonProperty("YT")
    YT("YT"),

    @JsonProperty("NT")
    NT("NT"),

    @JsonProperty("NU")
    NU("NU"),

    @JsonProperty("TB")
    TB("TB")
}
