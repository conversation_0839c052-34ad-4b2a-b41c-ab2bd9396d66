package com.loyalty.offermanagement.models.v3

import com.fasterxml.jackson.annotation.JsonProperty

enum class AwardType constructor(val value: String) {
    @JsonProperty("flatMiles")
    FlatMiles("flatMiles"),

    @JsonProperty("multiplierMiles")
    MultiplierMiles("multiplierMiles"),

    @JsonProperty("flatDiscount")
    FlatDiscount("flatDiscount"),

    @JsonProperty("percentDiscount")
    PercentDiscount("percentDiscount"),

    @JsonProperty("cashDiscount")
    CashDiscount("cashDiscount"),

    @JsonProperty("custom")
    Custom("custom")
}
