@file:Suppress(
        "LongMethod",
        "ParameterListWrapping"
)

package com.loyalty.offermanagement.models.v3.inout

import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.annotation.JsonRootName
import com.loyalty.offermanagement.models.LocalizedString
import com.loyalty.offermanagement.models.v3.*
import java.util.*

@JsonRootName("putofferio")
@JsonInclude(JsonInclude.Include.NON_NULL)
data class PutOfferIO(
        var partnerOfferId: String? = null,
        var id: String,
        override var partnerId: String,
        var partnerName: String,
        var partnerBaseEarnRate: Int? = null,
        var baseCashRedemption: Int = 95,
        var displayDate: String? = null,
        var startDate: String? = null,
        var endDate: String? = null,
        override var offerType: OfferType,
        override var qualifier: Qualifier,
        var awardType: AwardType,
        var issuanceCode: String? = null,
        var image: LocalizedImage,
        override var mechanisms: List<MechanismIO>,
        var cashierInstruction: LocalizedString? = null,
        override var tiers: List<TierIO>,
        var displayPriority: Int = 0,
        var regions: List<Region>,
        var offerLimitation: OfferLimitation = OfferLimitation.NoLimit,
        var offerLimitationText: LocalizedString? = null,
        var includedLocations: List<LocalizedString> = emptyList(),
        var excludedLocations: List<LocalizedString> = emptyList(),
        var includedBanners: List<LocalizedString> = emptyList(),
        var excludedBanners: List<LocalizedString> = emptyList(),
        var canBeCombined: Boolean = true,
        var combinationsText: LocalizedString? = null,
        var exclusions: LocalizedString? = null,
        var availability: List<Availability> = emptyList(),
        override var tags: List<String> = emptyList(),
        var partnerUrl: LocalizedString? = null,
        var daysToApply: Int? = null,
        var trademarkInfo: LocalizedString? = null,
        var description: LocalizedString? = null,
        var partnerLegalName: LocalizedString? = null,
        var hasCustomLegal: Boolean = false,
        var legalText: LocalizedString? = null,
        var duplicatedFrom: String? = null,
        var awardShort: LocalizedString? = null,
        var qualifierShort: LocalizedString? = null,
        var massOffer: Boolean = true,
        var active: Boolean = true,
        var offerCategory1: String? = null,
        var offerCategory2: String? = null,
        var offerCategory3: String? = null,
        var productName: String? = null,
        var productBrand: String? = null,
        var campaignCode: String? = null,
        var programPriority: Int = 0,
        override var programType: ProgramType = ProgramType.traditionalcore,
        override var ctaUrl: LocalizedString? = null,
        override var ctaLabel: LocalizedString? = null,
        var sponsorCode: String? = null,
        var eventBasedOffer: Boolean = false,
        var eligibilityDuration: Int? = null,
        var firstQualificationDate: String? = null,
        var lastQualificationDate: String? = null,
        var eligibilityDurationUnit: EligibilityDurationUnit? = null,
        override var cardType: List<CardType>? = emptyList(),
        override var retailerGroupId: List<UUID>? = null,
        override var usageLimit: Int? = null
) : OfferContract, FrenchFallback {
    override fun fallback() {
        image.fallback()
        mechanisms.fallback()
        cashierInstruction?.fallback()
        tiers.fallback()
        offerLimitationText?.fallback()
        includedLocations.fallback()
        excludedLocations.fallback()
        includedBanners.fallback()
        excludedBanners.fallback()
        combinationsText?.fallback()
        exclusions?.fallback()
        partnerUrl?.fallback()
        trademarkInfo?.fallback()
        description?.fallback()
        partnerLegalName?.fallback()
        legalText?.fallback()
        awardShort?.fallback()
        qualifierShort?.fallback()
        ctaUrl?.fallback()
        ctaLabel?.fallback()
    }
}
