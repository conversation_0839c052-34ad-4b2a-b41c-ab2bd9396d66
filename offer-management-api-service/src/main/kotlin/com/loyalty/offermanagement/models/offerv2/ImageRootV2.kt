package com.loyalty.offermanagement.models.offerv2

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.annotation.JsonRootName

@JsonRootName("offerio")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
data class ImageRootV2(
        var title: String? = null,
        var file: LocalizedImageV2? = null
)
