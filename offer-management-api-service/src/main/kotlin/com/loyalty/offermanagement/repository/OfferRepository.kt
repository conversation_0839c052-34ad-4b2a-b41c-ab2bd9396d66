@file:Suppress(
        "<PERSON><PERSON>eth<PERSON>",
        "LongParameterList"
)

package com.loyalty.offermanagement.repository

import com.loyalty.offermanagement.entities.Offer
import com.loyalty.offermanagement.models.v3.*
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.CrudRepository
import org.springframework.data.repository.PagingAndSortingRepository
import org.springframework.data.repository.query.Param
import org.springframework.stereotype.Repository
import java.time.LocalDateTime
import java.util.UUID
import javax.transaction.Transactional

// language=SQL
const val OFFER_REPOSITORY_SEARCH_QUERY = """ FROM Offer o
        LEFT JOIN Mechanism m ON o.id = m.offer
        LEFT JOIN Integration n ON o.id = n.offer
        WHERE
        (o.displayDate between :startFrom AND :startTill)
        AND
        (o.endDate between :endFrom AND :endTill)
        AND
        (COALESCE(:ids, NULL) IS NULL OR o.id in (:ids))
        AND
        ((:users) IS NULL OR o.createdBy in (:users))
        AND
        (COALESCE(:partners, NULL) IS NULL OR o.partnerId in (:partners))
        AND
        (COALESCE(:bulkIds, NULL) IS NULL OR o.batch.id in (:bulkIds))
        AND
        ((:tag1) IS NULL OR (:tag1) in elements(o.tags) OR (:tag2) in elements(o.tags) OR (:tag3) in elements(o.tags))
        AND
        ((:offerTypes) IS NULL OR o.offerType in (:offerTypes))
        AND
        ((:awardTypes) IS NULL OR o.awardType in (:awardTypes))
        AND
        ((:qualifiers) IS NULL OR o.qualifier in (:qualifiers))
        AND
        ((:mechanismType) IS NULL OR m.mechanismType in (:mechanismType))
        AND
        ((:region1) IS NULL OR (:region1) in elements(o.regions) OR (:region2) in elements(o.regions)
        OR (:region3) in elements(o.regions) OR (:region4) in elements(o.regions) OR (:region5) in elements(o.regions)
        OR (:region6) in elements(o.regions) OR (:region7) in elements(o.regions) OR (:region8) in elements(o.regions)
        OR (:region9) in elements(o.regions) OR (:region10) in elements(o.regions) OR (:region11) in elements(o.regions)
        OR (:region12) in elements(o.regions) OR (:region13) in elements(o.regions)
        OR (:region14) in elements(o.regions) OR (:region15) in elements(o.regions)
        )
        AND
        ((:statuses) IS NULL OR o.status in (:statuses))
        AND
        ((:massOffer) IS NULL OR o.massOffer = (:massOffer))
        AND
        ((:active) IS NULL OR o.active = (:active))
        AND
        ((:offerCategory1) IS NULL OR o.offerCategory1 in (:offerCategory1))
        AND
        ((:offerCategory2) IS NULL OR o.offerCategory2 in (:offerCategory2))
        AND
        ((:offerCategory3) IS NULL OR o.offerCategory3 in (:offerCategory3))
        AND
        ((:onlyEmptyCategory) IS NULL OR (:onlyEmptyCategory) IS FALSE OR ((:onlyEmptyCategory) IS TRUE AND (o.offerCategory1) IS NULL))
        AND
        ((:hasIssuanceCode) IS NULL OR ((:hasIssuanceCode) IS FALSE AND (o.issuanceCode IS NULL)) OR ((:hasIssuanceCode) IS TRUE AND (o.issuanceCode) IS NOT NULL))
        AND
        ((:programTypes) IS NULL OR o.programType in (:programTypes))
        AND
        ((:eventBasedOffer) IS NULL OR o.eventBasedOffer = (:eventBasedOffer))
        AND
        ((:eligibilityDuration) IS NULL OR o.eligibilityDuration = (:eligibilityDuration))
        AND
        ((:eligibilityDurationUnits) IS NULL OR o.eligibilityDurationUnit in (:eligibilityDurationUnits))
        AND
        (COALESCE(:campaignCode, NULL) IS NULL OR UPPER(o.campaignCode) in (:campaignCode))"""

const val OFFER_REPOSITORY_UNIQUENESS_QUERY = """ FROM Offer o        
        WHERE
        o.sponsorCode = :sponsorCode 
        AND
        o.issuanceCode = :offerCode 
        AND
        o.active IS TRUE 
        AND
        (COALESCE(:offerId, NULL) IS NULL OR o.id != (:offerId)) 
        AND
        (
                (o.startDate between :startFrom AND :endFrom)
                OR
                (o.endDate between :startFrom AND :endFrom)
        )"""

@Repository
interface OfferRepository : PagingAndSortingRepository<Offer, UUID>, CrudRepository<Offer, UUID> {
    fun findByIdIn(offerIds: List<UUID>): List<Offer>

    @Transactional
    fun deleteByIdIn(offerIds: List<UUID>): List<Offer>

    @Query(value = "SELECT o $OFFER_REPOSITORY_SEARCH_QUERY")
    fun doSearch(
            @Param("startFrom") startFrom: LocalDateTime,
            @Param("startTill") startTill: LocalDateTime,
            @Param("endFrom") endFrom: LocalDateTime,
            @Param("endTill") endTill: LocalDateTime,
            @Param("ids") ids: Set<UUID>?,
            @Param("users") users: Set<String>?,
            @Param("partners") partners: Set<UUID>?,
            @Param("bulkIds") bulkIds: Set<UUID>?,
            @Param("tag1") tag1: String?,
            @Param("tag2") tag2: String?,
            @Param("tag3") tag3: String?,
            @Param("offerTypes") offerTypes: Set<OfferType>?,
            @Param("awardTypes") awardTypes: Set<AwardType>?,
            @Param("qualifiers") qualifiers: Set<Qualifier>?,
            @Param("mechanismType") mechanismType: Set<MechanismType>?,
            @Param("region1") region1: Region?,
            @Param("region2") region2: Region?,
            @Param("region3") region3: Region?,
            @Param("region4") region4: Region?,
            @Param("region5") region5: Region?,
            @Param("region6") region6: Region?,
            @Param("region7") region7: Region?,
            @Param("region8") region8: Region?,
            @Param("region9") region9: Region?,
            @Param("region10") region10: Region?,
            @Param("region11") region11: Region?,
            @Param("region12") region12: Region?,
            @Param("region13") region13: Region?,
            @Param("region14") region14: Region?,
            @Param("region15") region15: Region?,
            @Param("statuses") statuses: Set<OfferStatus>?,
            @Param("massOffer") massOffer: Boolean?,
            @Param("active") active: Boolean?,
            @Param("offerCategory1") offerCategory1: Set<String>?,
            @Param("offerCategory2") offerCategory2: Set<String>?,
            @Param("offerCategory3") offerCategory3: Set<String>?,
            @Param("onlyEmptyCategory") onlyEmptyCategory: Boolean?,
            @Param("hasIssuanceCode") hasIssuanceCode: Boolean?,
            @Param("campaignCode") campaignCode: Set<String>?,
            @Param("programTypes") programTypes: Set<ProgramType>?,
            @Param("eventBasedOffer") eventBasedOffer: Boolean?,
            @Param("eligibilityDuration") eligibilityDuration: Int?,
            @Param("eligibilityDurationUnits") eligibilityDurationUnits: Set<EligibilityDurationUnit>?,
            pageReq: Pageable
    ): Page<Offer>

    @Query(value = "SELECT COUNT(o.id) $OFFER_REPOSITORY_SEARCH_QUERY")
    fun countBySearch(
            @Param("startFrom") startFrom: LocalDateTime,
            @Param("startTill") startTill: LocalDateTime,
            @Param("endFrom") endFrom: LocalDateTime,
            @Param("endTill") endTill: LocalDateTime,
            @Param("ids") ids: Set<UUID>?,
            @Param("users") users: Set<String>?,
            @Param("partners") partners: Set<UUID>?,
            @Param("bulkIds") bulkIds: Set<UUID>?,
            @Param("tag1") tag1: String?,
            @Param("tag2") tag2: String?,
            @Param("tag3") tag3: String?,
            @Param("offerTypes") offerTypes: Set<OfferType>?,
            @Param("awardTypes") awardTypes: Set<AwardType>?,
            @Param("qualifiers") qualifiers: Set<Qualifier>?,
            @Param("mechanismType") mechanismType: Set<MechanismType>?,
            @Param("region1") region1: Region?,
            @Param("region2") region2: Region?,
            @Param("region3") region3: Region?,
            @Param("region4") region4: Region?,
            @Param("region5") region5: Region?,
            @Param("region6") region6: Region?,
            @Param("region7") region7: Region?,
            @Param("region8") region8: Region?,
            @Param("region9") region9: Region?,
            @Param("region10") region10: Region?,
            @Param("region11") region11: Region?,
            @Param("region12") region12: Region?,
            @Param("region13") region13: Region?,
            @Param("region14") region14: Region?,
            @Param("region15") region15: Region?,
            @Param("statuses") statuses: Set<OfferStatus>?,
            @Param("massOffer") massOffer: Boolean?,
            @Param("active") active: Boolean?,
            @Param("offerCategory1") offerCategory1: Set<String>?,
            @Param("offerCategory2") offerCategory2: Set<String>?,
            @Param("offerCategory3") offerCategory3: Set<String>?,
            @Param("onlyEmptyCategory") onlyEmptyCategory: Boolean?,
            @Param("hasIssuanceCode") hasIssuanceCode: Boolean?,
            @Param("campaignCode") campaignCode: Set<String>?,
            @Param("programTypes") programTypes: Set<ProgramType>?,
            @Param("eventBasedOffer") eventBasedOffer: Boolean?,
            @Param("eligibilityDuration") eligibilityDuration: Int?,
            @Param("eligibilityDurationUnits") eligibilityDurationUnits: Set<EligibilityDurationUnit>?,
    ): Long

    @Query(value = "SELECT COUNT(o.id) $OFFER_REPOSITORY_UNIQUENESS_QUERY")
    fun countBysponsorCodeAndIssuanceCodebetweenDates(
        @Param("startFrom") startFrom: LocalDateTime,
        @Param("endFrom") endFrom: LocalDateTime,
        @Param("sponsorCode") sponsorCode: String?,
        @Param("offerCode") offerCode: String?,
        @Param("offerId") offerId: UUID?
    ): Long
}