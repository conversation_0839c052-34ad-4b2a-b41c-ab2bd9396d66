package com.loyalty.offermanagement.repository

import com.loyalty.offermanagement.entities.BatchJob
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.CrudRepository
import org.springframework.data.repository.PagingAndSortingRepository
import org.springframework.data.repository.query.Param
import org.springframework.stereotype.Repository
import java.util.UUID

@Repository
interface BatchJobRepository : PagingAndSortingRepository<BatchJob, UUID>, CrudRepository<BatchJob, UUID> {
    @Query(value = """
        select bj.id, bj.batch_name, o.partner_id, o.partner_name, count(*) as total_offers,
            bj.created_at, bj.created_by, bj.updated_at, bj.updated_by from batch_jobs as bj left join
            batchjob_offers as bo on bj.id = bo.batch_id left join offers as o
            on bo.offer_id = o.id group by bj.id, o.partner_id, o.partner_name
            """, nativeQuery = true)
    fun getJobs(): List<BatchJob>

    @Query("SELECT job FROM BatchJob job LEFT JOIN FETCH job.offers WHERE job.id in (:ids)")
    fun findAllByIdFetchEagerly(@Param("ids") id: Set<UUID>): Set<BatchJob>
}
