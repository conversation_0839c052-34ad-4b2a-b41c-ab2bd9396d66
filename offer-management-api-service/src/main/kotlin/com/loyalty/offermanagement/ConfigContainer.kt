package com.loyalty.offermanagement

import org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory
import org.springframework.boot.web.server.WebServerFactoryCustomizer
import org.springframework.stereotype.Component

@Component
class ConfigContainer(config: com.loyalty.offermanagement.SpringConfig) :
        WebServerFactoryCustomizer<TomcatServletWebServerFactory> {
    val urlPrefix = config.urlPrefix

    override fun customize(factory: TomcatServletWebServerFactory) {
        factory.contextPath = "/$urlPrefix"
    }
}
