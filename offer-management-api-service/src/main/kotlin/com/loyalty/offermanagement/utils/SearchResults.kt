@file:Suppress(
        "TooGenericExceptionThrown",
        "UnsafeCallOnNullableType"
)

package com.loyalty.offermanagement.utils

import java.util.ArrayList

class SearchResults<T> {
    private companion object {
        private const val defaultPageSize = 25
        private const val defaultTotalCount = 0L
        private const val defaultPageNumber = 0
        private const val defaultReturnedCount = 0
    }

    var totalCount = defaultTotalCount
    var pageSize = defaultPageSize
    var pageNumber = defaultPageNumber
    var returnedCount = defaultReturnedCount

    var content: List<T>? = null
        set(aRcontent) {
            var current = aRcontent
            if (current == null) {
                current = ArrayList()
            }
            field = current
        }
    private val numberOfPages: Long
        get() = if (this.content == null || this.content?.size == 0) {
            0
        } else this.totalCount / this.pageSize + if (this.totalCount % this.pageSize > 0) 1 else 0

    fun getResults(page: Int): List<T> {
        if (page <= 0 || page > this.numberOfPages) {
            throw RuntimeException("Page number is zero or there are no that many page content.")
        }
        val subList = ArrayList<T>()
        val start = (page - 1) * this.pageSize
        var end = start + this.pageSize
        if (end > this.content!!.size) {
            end = this.content!!.size
        }
        for (i in start until end) {
            subList.add(this.content!![i])
        }
        return subList
    }
}
