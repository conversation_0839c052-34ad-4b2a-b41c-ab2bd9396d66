package com.loyalty.offermanagement.utils.converters

import com.fasterxml.jackson.databind.ObjectMapper
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component

@Component
class ListToStringJsonConverter {
    @Autowired
    private lateinit var mapper: ObjectMapper

    fun <T> convert(value: List<T>): String = mapper.writeValueAsString(value)
}
