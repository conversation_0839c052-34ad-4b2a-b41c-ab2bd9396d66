package com.loyalty.offermanagement.utils.converters

import com.fasterxml.jackson.databind.ObjectMapper
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component

@Component
class StringJsonToListConverter {
    @Autowired
    private lateinit var mapper: ObjectMapper

    fun <T> convert(value: String): List<T> {
        val typeFactory = mapper.typeFactory
        return mapper.readValue(value, typeFactory.constructCollectionType(List::class.java, Any::class.java))
    }
}
