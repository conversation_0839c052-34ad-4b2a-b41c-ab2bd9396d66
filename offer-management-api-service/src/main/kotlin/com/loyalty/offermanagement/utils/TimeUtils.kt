@file:Suppress(
        "MagicNumber"
)

package com.loyalty.offermanagement.utils

import java.time.LocalDate
import java.time.LocalDateTime
import java.time.format.DateTimeFormatterBuilder
import java.time.temporal.ChronoField
import java.time.temporal.ChronoUnit

class TimeUtils {
    private constructor() // solution to get rid of UtilityClassWithPublicConstructor instead of removing class

    companion object {
        private const val dateDiff = 73000 // almost 200 years

        fun getDateAtNow(): LocalDateTime = LocalDateTime.now().withNano(0)

        fun getDateMidnight(numDays: Long = 1): LocalDateTime = LocalDateTime.now().toLocalDate().atStartOfDay().plus(numDays, ChronoUnit.DAYS)

        fun getDateMidnight(givenDate: LocalDate, numDays: Long = 1): LocalDateTime {
            return givenDate.atStartOfDay().plus(numDays, ChronoUnit.DAYS)
        }

        fun getDateEnd(numDays: Long = 1): LocalDateTime = LocalDateTime.now().toLocalDate().atTime(23, 59, 59).plus(numDays, ChronoUnit.DAYS)

        fun getDateEnd(givenDate: LocalDate, numDays: Long = 1): LocalDateTime {
            return givenDate.atTime(23, 59, 59).plus(numDays, ChronoUnit.DAYS)
        }

        fun getBeginningOfTime() = getDateMidnight(-TimeUtils.dateDiff.toLong())

        fun getEndOfTime() = getDateEnd(TimeUtils.dateDiff.toLong())

        fun getDiffDay(date: LocalDate): Long {
            return ChronoUnit.DAYS.between(LocalDate.now(), date)
        }

        fun getDiffDay(date1: LocalDate, date2: LocalDate): Long {
            return ChronoUnit.DAYS.between(date1, date2)
        }

        fun parseStringToLocalDateTime(dateTimeString: String): LocalDateTime {
            val jsonSchemaFormatter = DateTimeFormatterBuilder()
                .appendPattern("yyyy-MM-dd'T'HH:mm:ss")
                .appendOptional(DateTimeFormatterBuilder()
                    .appendFraction(ChronoField.NANO_OF_SECOND, 1, 9, true)
                    .toFormatter())
                .appendPattern("XXX")
                .toFormatter()
            return LocalDateTime.parse(dateTimeString, jsonSchemaFormatter)
        }
    }
}
