package com.loyalty.offermanagement.utils

object ValidationConstants {
    // Constants to bmo pre-approval
    const val PROGRAM_TYPE_BMO_PREAPP = "bmopreapp"

    // Error messages to bmo pre-approval
    const val ERROR_INVALID_PARTNER = "Program Type BMO Pre-approval only allowed with partner Bank of Montreal."
    const val ERROR_MISSING_OPTIN_MECHANISM = "Program Type BMO Pre-approval must have optIn as mechanism."
    const val ERROR_CTA_FIELDS_BOTH_MANDATORY = "ctaLabel and ctaUrl fields are both mandatory."

    // Error messages for AM Receipts
    const val ERROR_MISSING_SCANRECEIPT_MECHANISM = "Program Type AM Receipts must have scanReceipt as mechanism."
    const val ERROR_SCANRECEIPT_MECHANISM_NOT_NEEDED = "Mechanism type scanReceipt is valid for AMReceipts offers only."
    const val ERROR_RETAILER_GROUP_NOT_NEEDED = "RetailerGroupId should be null or empty."
    const val ERROR_MISSING_USAGE_LIMIT = "UsageLimit is a mandatory field and must be an Integer value."
    const val ERROR_USAGE_LIMIT_NOT_NEEDED = "UsageLimit should be null or empty."
    const val ERROR_OFFER_TYPE_BASE = "Offer Type must be one of these values: 'buy', 'spend', 'amCashEarn', 'amCashDiscount', 'custom'."
    const val ERROR_QUALIFIER_NOT_VALID = "The Qualifier values 'perProduct', 'perUnit', 'perDollar' and 'frequency' can only be used for AMReceipts offers."
    const val ERROR_TIERS_GREATER_THAN_ONE = "Tiers length should not be greater than one for AMReceipts offers."
}