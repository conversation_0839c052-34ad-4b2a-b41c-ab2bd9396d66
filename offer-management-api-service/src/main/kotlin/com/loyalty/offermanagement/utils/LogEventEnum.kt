package com.loyalty.offermanagement.utils

enum class LogEventEnum constructor(val value: String) {
    GetOffer("get-offer"),
    GetOffersCounts("get-offer-counts"),
    <PERSON><PERSON>("create-offer"),
    Update("update-offer"),
    Deploy("deploy-offer"),
    Redeploy("re-deploy-offer"),
    UpdateDB("update-db"),
    Publish("publish-offer"),
    RePublish("republish-offers"),
    CreateBulk("create-bulk"),
    UpdateBulk("update-bulk"),
    GetBulk("get-bulk"),
    Search("search-offers"),
    Stream("stream"),
    KinesisStream("kinesis-stream"),
    Delete("delete-offers"),
    Disable("disable-offers"),
    Enable("enable-offers"),
    StreamFailure("StreamFailure"),
    UniquenessOfferValidation("uniqueness Offer Validation")
}
