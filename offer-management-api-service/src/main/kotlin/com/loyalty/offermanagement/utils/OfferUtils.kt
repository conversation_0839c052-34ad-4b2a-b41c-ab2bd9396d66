package com.loyalty.offermanagement.utils

import org.everit.json.schema.Schema
import org.everit.json.schema.loader.SchemaClient
import org.everit.json.schema.loader.SchemaLoader
import org.json.JSONObject
import org.json.JSONTokener

class OfferUtils private constructor() {
    companion object {
        fun getSchema(path: String): Schema {
            val inputStream = this::class.java.getResourceAsStream(path)
            val rawSchema = JSONObject(JSONTokener(inputStream))
            return SchemaLoader.SchemaLoaderBuilder()
                    .schemaClient(SchemaClient.classPathAwareClient())
                    .schemaJson(rawSchema)
                    .resolutionScope("classpath://schemas/")
                    .build()
                    .load().build()
        }

        /** Fields like regions/availability and tags with format of
         *  array of string need this for search
         */
        fun attachWildCards(param: String) = param.split(",").toTypedArray().map { "%$it%" }.joinToString()
    }
}
