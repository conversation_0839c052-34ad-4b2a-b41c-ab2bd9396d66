package com.loyalty.offermanagement.utils

import com.loyalty.offermanagement.SpringConfig
import com.loyalty.offermanagement.errors.SecretNotFoundException
import com.loyalty.offermanagement.services.secret.SecretGrabber
import org.slf4j.Logger
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.jdbc.DataSourceBuilder
import org.springframework.stereotype.Component
import javax.sql.DataSource

@Component
class DataSourceFactory(private val config: SpringConfig, private val secretGrabber: SecretGrabber) {
    @Autowired
    private lateinit var logger: Logger

    fun createDataSource(secretName: String): DataSource {
        val databaseName = config.databaseName
        val databaseWriterEndpoint = config.databaseWriterEndpoint

        val dataSourceBuilder = DataSourceBuilder.create()

        logger.info("aws Postgres DB is being used")

        val secretMap = secretGrabber.getSecret(secretName)

        secretMap?.let {
            dataSourceBuilder.url(
                    "*******************************************************")
                    .username(it["username"].toString())
                    .password(it["password"].toString())
        } ?: throw SecretNotFoundException("Secret $secretName is not found")

        return dataSourceBuilder.build()
    }
}
