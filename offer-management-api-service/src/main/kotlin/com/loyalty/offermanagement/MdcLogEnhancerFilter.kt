package com.loyalty.offermanagement

import org.slf4j.MDC
import javax.servlet.Filter
import javax.servlet.FilterChain
import javax.servlet.ServletRequest
import javax.servlet.ServletResponse
import javax.servlet.http.HttpServletRequest

class MdcLogEnhancerFilter : Filter {
    override fun doFilter(request: ServletRequest, response: ServletResponse, chain: FilterChain) {
        var headerNamesList = emptyList<String>()

        if (request is HttpServletRequest) {
            request.headerNames.toList()
                    .also { headerNames -> headerNamesList = headerNames }
                    .forEach { headerName -> MDC.put(headerName, request.getHeader(headerName)) }
        }
        chain.doFilter(request, response)
        headerNamesList.forEach(MDC::remove)
    }
}