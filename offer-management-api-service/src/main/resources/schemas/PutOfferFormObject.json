{"$id": "PutOfferFormObject.json", "$schema": "http://json-schema.org/draft-07/schema#", "allOf": [{"$ref": "PostOfferFormObject.json"}], "properties": {"id": {"$comment": "required if PUT", "format": "uuid", "type": "string", "maxLength": 36}, "eventBasedOffer": {"type": "boolean"}, "displayDate": {"type": "string", "format": "date-time"}, "startDate": {"type": "string", "format": "date-time"}, "endDate": {"type": "string", "format": "date-time"}, "firstQualificationDate": {"type": "string", "format": "date-time"}, "lastQualificationDate": {"type": "string", "format": "date-time"}, "eligibilityDuration": {"type": "integer"}, "eligibilityDurationUnit": {"type": "string"}, "massOffer": {"type": "boolean"}, "retailerGroupId": {"type": ["array", "null"], "items": {"type": "string", "format": "uuid"}, "uniqueItems": true}, "usageLimit": {"type": ["integer", "null"], "minimum": 1}}, "required": ["id"], "if": {"properties": {"eventBasedOffer": {"const": true}}}, "then": {"properties": {"firstQualificationDate": {"type": "string"}, "lastQualificationDate": {"type": "string"}, "eligibilityDuration": {"type": "integer"}, "massOffer": {"const": false}}, "required": ["firstQualificationDate", "lastQualificationDate", "eligibilityDuration", "massOffer"]}, "else": {"properties": {"displayDate": {"type": "string"}, "startDate": {"type": "string"}, "endDate": {"type": "string"}}, "required": ["displayDate", "startDate", "endDate"]}}