feature.toggle.stream.event.v3=true
feature.toggle.stream.event.v2=false
feature.toggle.clo=true
feature.toggle.am.receipts=true
spring.datasource.masteruser.secret=uat-offer-management-api-resources-master-user
spring.datasource.adminuser.secret=uat-offer-management-api-resources-admin-user
spring.datasource.appuser.secret=uat-offer-management-api-resources-app-user
spring.datasource.reportinguser.secret=uat-offer-management-api-resources-reporting-user
environment=uat
KinesisName=uat-nova-offer-published-eventbus
KinesisRegion=ca-central-1
KinesisAccountId=************
KinesisUSEastAccountId=************
KinesisNameForUSEast=uat-nova-event-bus-offer-event-stream
KinesisUSRegion=us-east-1
partner.id.bank.montreal=22a2cdfd-ff82-45f6-bc94-c14a3a533922