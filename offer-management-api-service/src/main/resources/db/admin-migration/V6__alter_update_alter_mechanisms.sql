ALTER TABLE mechanisms ADD mechanism_type_new varchar(255);

UPDATE mechanisms SET mechanism_type_new='NoAction' WHERE mechanism_type=0;
UPDATE mechanisms SET mechanism_type_new='BarcodeUPC' WHERE mechanism_type=1;
UPDATE mechanisms SET mechanism_type_new='BarcodeEAN' WHERE mechanism_type=2;
UPDATE mechanisms SET mechanism_type_new='BarcodeCODE39' WHERE mechanism_type=3;
UPDATE mechanisms SET mechanism_type_new='BarcodeCODE128' WHERE mechanism_type=4;
UPDATE mechanisms SET mechanism_type_new='PLU' WHERE mechanism_type=5;
UPDATE mechanisms SET mechanism_type_new='CouponCode' WHERE mechanism_type=6;
UPDATE mechanisms SET mechanism_type_new='Button' WHERE mechanism_type=7;
UPDATE mechanisms SET mechanism_type_new='LoadGo' WHERE mechanism_type=8;

ALTER TABLE mechanisms DROP COLUMN mechanism_type;
ALTER TABLE mechanisms RENAME COLUMN mechanism_type_new TO mechanism_type;



