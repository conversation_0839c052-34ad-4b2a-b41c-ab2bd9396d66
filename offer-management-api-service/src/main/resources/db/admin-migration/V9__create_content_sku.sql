DROP TABLE IF EXISTS content_product;

CREATE TABLE content_skus (content_id int8 NOT NULL, skus varchar(255));
CREATE INDEX contents_skus ON content_skus USING btree (content_id);
ALTER TABLE IF EXISTS content_skus ADD CONSTRAINT FKa7wac5tflhljw9uau4rbneha5 foreign key (content_id) references tier_contents;


GRANT USAGE ON SCHEMA offermanagement TO offer_management_readwrite_role;
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA offermanagement TO offer_management_readwrite_role;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA offermanagement TO offer_management_readwrite_role;

GRANT USAGE ON SCHEMA offermanagement TO offer_management_readonly_role;
GRANT SELECT ON ALL TABLES IN SCHEMA offermanagement TO offer_management_readonly_role;
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA offermanagement TO offer_management_readonly_role;