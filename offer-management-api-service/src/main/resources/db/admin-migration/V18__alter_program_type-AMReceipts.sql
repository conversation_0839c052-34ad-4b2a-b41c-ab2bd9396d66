ALTER TABLE offers ALTER COLUMN program_type DROP DEFAULT;
ALTER TABLE offers ALTER COLUMN program_type TYPE VARCHAR;
DROP DOMAIN program_type_domain;
CREATE DOMAIN program_type_domain AS varchar NOT NULL DEFAULT 'traditionalcore' CHECK(VALUE in ('traditionalcore', 'cardlinked', 'airmilesshops', 'bmopreapp','amreceipts'));
ALTER TABLE offers ALTER COLUMN program_type TYPE program_type_domain;
ALTER TABLE offers ALTER COLUMN program_type SET DEFAULT 'traditionalcore';