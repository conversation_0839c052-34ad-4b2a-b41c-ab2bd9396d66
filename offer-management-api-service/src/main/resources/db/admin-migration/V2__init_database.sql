CREATE SEQUENCE hibernate_sequence start 1 INCREMENT 1;
CREATE SEQUENCE integration_sq start 1 INCREMENT 1;
CREATE SEQUENCE mechanism_sq start 1 INCREMENT 1;
CREATE SEQUENCE tiers_sq start 1 INCREMENT 1;
CREATE TABLE batch_jobs (id uuid NOT NULL, batch_name varchar(255), created_at timestamp, created_by varchar(255), partner_id uuid, partner_name varchar(255), total_offers int4 NOT NULL, updated_at timestamp, updated_by varchar(255), primary key (id));
CREATE TABLE batchjob_offers (batch_id uuid, offer_id uuid NOT NULL, primary key (offer_id));
CREATE TABLE integrations (id int8 NOT NULL, system_code varchar(255), system_name varchar(255), offer_id uuid NOT NULL, primary key (id));
CREATE TABLE mechanisms (id int8 NOT NULL, mechanism_label_en varchar(255), mechanism_label_fr varchar(255), mechanism_text_en varchar(255), mechanism_text_fr varchar(255), mechanism_title_en varchar(255), mechanism_title_fr varchar(255), mechanism_type int4, mechanism_value_en TEXT, mechanism_value_fr TEXT, offer_id uuid NOT NULL, primary key (id));
CREATE TABLE offer_availability (offer_id uuid NOT NULL, availability varchar(255));
CREATE TABLE offer_regions (offer_id uuid NOT NULL, regions varchar(255));
CREATE TABLE offer_tags (offer_id uuid NOT NULL, tags varchar(255));
CREATE TABLE offers (id uuid NOT NULL, active Boolean default True NOT NULL, award_short_en varchar(255), award_short_fr varchar(255), award_type varchar(255), cashier_instruction_en varchar(255), cashier_instruction_fr varchar(255), content_version int4, created_at timestamp, created_by varchar(255), description_en varchar(255), description_fr varchar(255), display_date timestamp, display_priority int4, duplicated_from uuid, end_date timestamp, has_custom_legal boolean, image_en TEXT, image_fr TEXT, issuance_code varchar(255), legal_text_en TEXT, legal_text_fr TEXT, mass_offer Boolean default True NOT NULL, offer_category_1 varchar(255), offer_category_2 varchar(255), offer_category_3 varchar(255), offer_content TEXT, offer_type varchar(255), partner_id uuid, partner_name varchar(255), partner_offer_id varchar(255), product_brand varchar(255), product_name varchar(255), program_priority Int default 0 NOT NULL, published_at timestamp, published_by varchar(255), qualifier varchar(255), qualifier_short_en varchar(255), qualifier_short_fr varchar(255), start_date timestamp, status varchar(255), updated_at timestamp, updated_by varchar(255), primary key (id));
CREATE TABLE tier_contents (id int8 NOT NULL, content_en varchar(255), content_fr varchar(255), master_product Boolean default False NOT NULL, product_sku varchar(255), upc varchar(255), tier_id int8 NOT NULL, primary key (id));
CREATE TABLE tiers (id int8 NOT NULL, award_long_en varchar(255), award_long_fr varchar(255), award_value float4, qualifier_long_en varchar(255), qualifier_long_fr varchar(255), qualifier_value float4, offer_id uuid NOT NULL, primary key (id));
CREATE INDEX offers_availability ON offers USING btree (display_date);
CREATE INDEX offers_display_date ON offers USING btree (display_date);
CREATE INDEX offers_end_date ON offers USING btree (end_date);
CREATE INDEX offers_regions ON offer_regions USING btree (offer_id);
CREATE INDEX offers_start_date ON offers USING btree (start_date);
CREATE INDEX offers_tags ON offer_tags USING btree (offer_id);
ALTER TABLE IF EXISTS batchjob_offers ADD CONSTRAINT FK11alshqs7bej4npi8bs3mayid foreign key (batch_id) references batch_jobs;
ALTER TABLE IF EXISTS batchjob_offers ADD CONSTRAINT FKq8je4xtsom8d4t2t0t3bfo7sg foreign key (offer_id) references offers;
ALTER TABLE IF EXISTS integrations ADD CONSTRAINT FKrkaq56ht40v1p9ln0sc0rlhqr foreign key (offer_id) references offers;
ALTER TABLE IF EXISTS mechanisms ADD CONSTRAINT FKgvbccrjahhg7b9ndj200ikcv6 foreign key (offer_id) references offers;
ALTER TABLE IF EXISTS offer_availability ADD CONSTRAINT FKh67ospqdfko25s1j1we4fg8rs foreign key (offer_id) references offers;
ALTER TABLE IF EXISTS offer_regions ADD CONSTRAINT FKocw50g9j75ywc92pujpun6uap foreign key (offer_id) references offers;
ALTER TABLE IF EXISTS offer_tags ADD CONSTRAINT FKc7bsbawdiyqg62it3cs52ihuc foreign key (offer_id) references offers;
ALTER TABLE IF EXISTS tier_contents ADD CONSTRAINT FKbahxgyg1d576nqdq7xg6bbbwa foreign key (tier_id) references tiers;
ALTER TABLE IF EXISTS tiers ADD CONSTRAINT FKrly4thf4ak6qfxoqk9y27g3vm foreign key (offer_id) references offers;

GRANT USAGE ON SCHEMA offermanagement TO offer_management_readwrite_role;
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA offermanagement TO offer_management_readwrite_role;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA offermanagement TO offer_management_readwrite_role;

GRANT USAGE ON SCHEMA offermanagement TO offer_management_readonly_role;
GRANT SELECT ON ALL TABLES IN SCHEMA offermanagement TO offer_management_readonly_role;
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA offermanagement TO offer_management_readonly_role;