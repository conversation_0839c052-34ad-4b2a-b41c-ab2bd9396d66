UPDATE offers
SET image_en=REPLACE(image_en, '${env}-post-public', 'archive-${env}-post-public'), image_fr=REPLACE(image_fr, '${env}-post-public', 'archive-${env}-post-public')
WHERE image_en LIKE '%/${env}-post-public%' AND image_fr LIKE '%/${env}-post-public%';

UPDATE offers
SET image_en=REPLACE(image_en, '${env}-l1-post-images', 'archive-${env}-l1-post-images'), image_fr=REPLACE(image_fr, '${env}-l1-post-images', 'archive-${env}-l1-post-images')
WHERE image_en LIKE '%/${env}-l1-post-images%' AND image_fr LIKE '%/${env}-l1-post-images%';

-- Sandbox exceptions (Detect sandbox and update to uat for new env)
UPDATE offers
SET image_en=REPLACE(image_en, 'sandbox-post-public', 'archive-uat-post-public'), image_fr=REPLACE(image_fr, 'sandbox-post-public', 'archive-uat-post-public')
WHERE image_en LIKE '%/sandbox-post-public%' AND image_fr LIKE '%/sandbox-post-public%';

UPDATE offers
SET image_en=REPLACE(image_en, 'sandbox-l1-post-images', 'archive-uat-l1-post-images'), image_fr=REPLACE(image_fr, 'sandbox-l1-post-images', 'archive-uat-l1-post-images')
WHERE image_en LIKE '%/sandbox-l1-post-images%' AND image_fr LIKE '%/sandbox-l1-post-images%';

-- ROLLBACK
--
-- UPDATE offers
-- SET image_en=REPLACE(image_en, 'archive-${env}-post-public', '${env}-post-public'), image_fr=REPLACE(image_fr, 'archive-${env}-post-public', '${env}-post-public')
-- WHERE image_en LIKE '%/archive-${env}-post-public%' AND image_fr LIKE '%/archive-${env}-post-public%'
--
-- UPDATE offers
-- SET image_en=REPLACE(image_en, 'archive-${env}-l1-post-images', '${env}-l1-post-images'), image_fr=REPLACE(image_fr, 'archive-${env}-l1-post-images', '${env}-l1-post-images')
-- WHERE image_en LIKE '%/archive-${env}-l1-post-images%' AND image_fr LIKE '%/archive-${env}-l1-post-images%'
--
