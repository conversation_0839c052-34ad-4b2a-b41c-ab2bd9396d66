### New Database

##### First Jenkins run
- V1__create_users.sql migration added
- Use dbappadmin 
- V1 is applied, no tables created

##### Second Jenkins run
- V2__init_database.sql migration added
- Use dbappuser
- V2 is applied, schema is created

### Links
https://stackoverflow.com/questions/29072628/how-to-override-spring-boot-application-properties-programmatically

### Drop Roles/users
REASSIGN OWNED BY offer_management_readonly_role TO postgres;
DROP OWNED BY offer_management_readonly_role;
DROP ROLE offer_management_readonly_role;
REASSIGN OWNED BY offer_management_readwrite_role TO postgres;
DROP OWNED BY offer_management_readwrite_role;
DROP ROLE offer_management_readwrite_role;
REASSIGN OWNED BY offer_management_admin_role TO postgres;
DROP OWNED BY offer_management_admin_role;
DROP ROLE offer_management_admin_role;
DROP ROLE dbreportinguser;
DROP ROLE dbappuser;DROP ROLE dbadminuser;

### Using PSQL locally 
docker exec -it postgres psql -U postgres
SHOW search_path;
SET search_path TO offermanagement, postgres, public;


### Getting dump from other envs
1. tunnel
    ssh -N -f -l YourUser -L 5432:uat-offer-management-api-resource-auroradbcluster-el0ynqahxwr7.cluster-cmu13oetyypk.us-east-1.rds.amazonaws.com:5432 **********
2. dump just the data (without structure using postgres user)
    pg_dump --data-only -U dbadminuser -h localhost -p 5432 -W -F p offerManagement > ~/dumps/ENV-offer-management-api_DATE.sql	 
    
    
to restore:     
ssh -N -f -l YourUser -L 5432:uat-offer-management-api-resource-auroradbcluster-1y02mtiowfgyk.cluster-cmu13oetyypk.us-east-1.rds.amazonaws.com:5432 **********  
psql -U dbappuser -h localhost -p 5432 -W -F p offermanagement < ~/dumps/ENV-offer-management-api_DATE.sql

### Restoring a dump
1. Run the app so your privilages are generated (dbadminuser is our new admin user not postgres admin user like command before)
2. docker exec -i postgres psql postgres -U dbadminuser  -W -F p offermanagement < ~/dumps/ENV-offer-management-api_DATE.sql
