CREATE SCHEMA IF NOT EXISTS ${offermanagementdb};

DO
$do$
    BEGIN
        IF NOT EXISTS (
                SELECT
                FROM   pg_catalog.pg_roles
                WHERE  rolname = 'offer_management_admin_role') THEN

            CREATE ROLE offer_management_admin_role INHERIT;
            GRANT CONNECT ON DATABASE ${offermanagementdb} TO offer_management_admin_role;
            REVOKE ALL ON SCHEMA offermanagement FROM offer_management_admin_role;
            GRANT ALL ON SCHEMA offermanagement TO offer_management_admin_role;
            CREATE USER ${dbadminuser} WITH PASSWORD '${dbadminuserpasswd}' INHERIT;
        END IF;
        IF NOT EXISTS (
                SELECT
                FROM   pg_catalog.pg_roles
                WHERE  rolname = 'offer_management_readwrite_role') THEN

            CREATE ROLE offer_management_readwrite_role INHERIT;
            GRANT CONNECT ON DATABASE ${offermanagementdb} TO offer_management_readwrite_role;
            REVOKE ALL ON SCHEMA offermanagement FROM offer_management_readwrite_role;
            GRANT USAGE ON SCHEMA offermanagement TO offer_management_readwrite_role;
            CREATE USER ${dbappuser} WITH PASSWORD '${dbappuserpasswd}' INHERIT;
        END IF;
        IF NOT EXISTS (
                SELECT
                FROM   pg_catalog.pg_roles
                WHERE  rolname = 'offer_management_readonly_role') THEN

            CREATE ROLE offer_management_readonly_role INHERIT;
            GRANT CONNECT ON DATABASE ${offermanagementdb} TO offer_management_readonly_role;
            REVOKE ALL ON SCHEMA offermanagement FROM offer_management_readonly_role;
            GRANT USAGE ON SCHEMA offermanagement TO offer_management_readonly_role;
            CREATE USER ${dbreportinguser} WITH PASSWORD '${dbreportinguserpasswd}' INHERIT;
        END IF;
    END
$do$;

REVOKE CREATE ON SCHEMA public FROM PUBLIC;
REVOKE ALL ON DATABASE ${offermanagementdb} FROM PUBLIC;

-- Grant privileges
GRANT offer_management_admin_role TO ${dbadminuser};
GRANT offer_management_readwrite_role TO ${dbappuser};
GRANT offer_management_readonly_role TO ${dbreportinguser};

GRANT USAGE ON SCHEMA offermanagement TO offer_management_admin_role;
GRANT SELECT, INSERT, UPDATE, REFERENCES, TRIGGER, DELETE ON ALL TABLES IN SCHEMA offermanagement TO offer_management_admin_role;
GRANT ALL ON ALL SEQUENCES IN SCHEMA offermanagement TO offer_management_admin_role;

GRANT USAGE ON SCHEMA offermanagement TO offer_management_readwrite_role;
GRANT SELECT, INSERT, UPDATE, REFERENCES, TRIGGER, DELETE ON ALL TABLES IN SCHEMA offermanagement TO offer_management_readwrite_role;
GRANT ALL ON ALL SEQUENCES IN SCHEMA offermanagement TO offer_management_readwrite_role;

GRANT USAGE ON SCHEMA offermanagement TO offer_management_readonly_role;
GRANT SELECT ON ALL TABLES IN SCHEMA offermanagement TO offer_management_readonly_role;
GRANT USAGE ON ALL SEQUENCES IN SCHEMA offermanagement TO offer_management_readonly_role;

-- Change Schema Ownership
-- ALTER SCHEMA offermanagement OWNER TO offer_management_admin_role;