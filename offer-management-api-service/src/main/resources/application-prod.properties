feature.toggle.stream.event.v3=true
feature.toggle.stream.event.v2=false
feature.toggle.clo=true
feature.toggle.am.receipts=false
spring.datasource.masteruser.secret=prod-offer-management-api-resources-master-user
spring.datasource.adminuser.secret=prod-offer-management-api-resources-admin-user
spring.datasource.appuser.secret=prod-offer-management-api-resources-app-user
spring.datasource.reportinguser.secret=prod-offer-management-api-resources-reporting-user
environment=prod
KinesisName=prod-nova-offer-published-eventbus
KinesisRegion=ca-central-1
KinesisAccountId=************
KinesisUSEastAccountId=************
KinesisNameForUSEast=prod-nova-event-bus-offer-event-stream
KinesisUSRegion=us-east-1
partner.id.bank.montreal=22a2cdfd-ff82-45f6-bc94-c14a3a533922