feature.toggle.stream.event.v3=true
feature.toggle.stream.event.v2=false
feature.toggle.clo=true
feature.toggle.am.receipts=true
#spring.jpa.properties.hibernate.hbm2ddl.auto=create-drop
###### generate schema ########
#spring.jpa.properties.javax.persistence.schema-generation.create-source=metadata
#spring.jpa.properties.javax.persistence.schema-generation.scripts.action=create
#spring.jpa.properties.javax.persistence.schema-generation.scripts.create-target=offer-management-api-service/build/db/create.sql
###############################
url.prefix=offer-management-api
cgs.endpoint=http://localhost:9090/content-generation-service
offer.api.v2.endpoint=http://localhost:9090/offer-management
offer.promotions.endpoint=http://localhost:9090/promotion-service
offer.facets.endpoint=http://localhost:9090/offer-facets
partners.endpoint=https://0636c19e-6af2-4241-9a38-3d5f093b8bf4.mock.pstmn.io/partners
aws.region=us-east-1
spring.datasource.masteruser.secret=local-offer-management-api-resources-master-user
spring.datasource.adminuser.secret=local-offer-management-api-resources-admin-user
spring.datasource.appuser.secret=local-offer-management-api-resources-app-user
spring.datasource.reportinguser.secret=local-offer-management-api-resources-reporting-user
spring.datasource.driverClassName=org.postgresql.Driver
database.writer.endpoint=localhost:5432
database.name=postgres
nova.bus.table.name=local-nova-event-bus-offer-event-store
nova.bus.endpoint=http://localhost:8000
nova.bus.region=us-east-1
environment=local
offer.default.image.base=
spring.jpa.properties.hibernate.jdbc.lob.non_contextual_creation=true
KinesisName=local-nova-offer-published-eventbus
KinesisRegion=ca-central-1
KinesisAccountId=************
KinesisUSEastAccountId=************
KinesisNameForUSEast=local-nova-event-bus-offer-event-stream
KinesisUSRegion=us-east-1
partner.id.bank.montreal=22a2cdfd-ff82-45f6-bc94-c14a3a533922