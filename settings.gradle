pluginManagement {
    apply from: 'dependency.gradle'

    plugins {
        id 'com.jfrog.artifactory' version "${versions.jfrogVersion}"
        id "io.gitlab.arturbosch.detekt" version "${versions.detektVersion}"
        id "io.spring.dependency-management" version "${versions.springDependencyManagementVersion}"
        id 'org.owasp.dependencycheck' version "${versions.dependencycheckOWASPVersion}"
        id 'com.adarshr.test-logger' version "${versions.testLoggerVersion}"
        id "org.jetbrains.kotlin.jvm" version "${versions.kotlinVersion}"
        id 'org.jetbrains.kotlin.plugin.allopen' version "${versions.kotlinVersion}"
        id 'org.jetbrains.kotlin.plugin.noarg' version "${versions.kotlinVersion}"
        id 'org.jetbrains.kotlin.plugin.spring' version "${versions.kotlinVersion}"
        id "net.researchgate.release" version "${versions.gradleReleaseVersion}"
        id 'org.springframework.boot' version "${versions.springBootVersion}"
        id 'com.palantir.docker' version "${versions.palantirVersion}"
    }
}

rootProject.name = 'offer-management-api'
include 'offer-management-api-test-utils', 'offer-management-api-service', 'offer-management-api-it'
