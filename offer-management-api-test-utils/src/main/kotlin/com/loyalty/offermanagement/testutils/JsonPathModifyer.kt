@file:Suppress(
        "MagicNumber"
)

package com.loyalty.offermanagement.testutils

import com.jayway.jsonpath.Configuration
import com.jayway.jsonpath.JsonPath
import org.jetbrains.kotlin.cli.common.environment.setIdeaIoUseFallback
import java.time.LocalDateTime
import java.util.concurrent.ThreadLocalRandom
import javax.script.ScriptEngineManager
import javax.script.ScriptException
import kotlin.streams.asSequence
import org.json.JSONArray
import org.json.JSONObject

object JsonPathModifyer {
    init {
        setIdeaIoUseFallback()
    }

    val scriptEngine = ScriptEngineManager().getEngineByExtension("kts").also {
        it.eval("import com.loyalty.offermanagement.testutils.JsonPathModifyer.shiftStartDateHours")
        it.eval("import com.loyalty.offermanagement.testutils.JsonPathModifyer.shiftEndDateHours")
        it.eval("import com.loyalty.offermanagement.testutils.JsonPathModifyer.shiftDisplayDateHours")
        it.eval("import com.loyalty.offermanagement.testutils.JsonPathModifyer.cardTypeList")
        it.eval("import com.loyalty.offermanagement.testutils.JsonPathModifyer.alphanumericStringGenerator")
    }

    fun modifyJson(json: String, transformationData: Map<String, Any>): String {
        val document = JsonPath.using(Configuration.defaultConfiguration()).parse(json)

        for ((fullPath, value) in transformationData.filterKeys {
            !it.contains("path")
        }) {
            try {
                val key = fullPath.split(".").last()
                val path = fullPath.removeSuffix(".".plus(key))
                val result = scriptEngine.eval(value.toString())
                document.put(path, key, result)
            } catch (e: ScriptException) {
                println("Warning: ScriptException evaluating $fullPath $value")
            }
        }
        return document.jsonString()
    }

    fun shiftStartDateHours(hours: Long): String {
        return LocalDateTime.now()
                .withMinute(0)
                .withSecond(0)
                .withNano(0)
                .plusHours(hours)
                .let(LocalDateTime::toString) + ":00Z" // TODO check validity
    }

    fun shiftDisplayDateHours(hours: Long): String {
        return LocalDateTime.now()
                .withMinute(0)
                .withSecond(0)
                .withNano(0)
                .plusHours(hours)
                .let(LocalDateTime::toString) + ":00Z" // TODO check validity
    }

    fun shiftEndDateHours(hours: Long): String {
        return LocalDateTime.now()
                .withMinute(59)
                .withSecond(0)
                .withNano(0)
                .plusHours(hours)
                .let(LocalDateTime::toString) + ":00Z" // TODO check validity
    }

    fun alphanumericStringGenerator(length: Long): String {
        val charPool: List<Char> = ('a'..'z') + ('A'..'Z') + ('0'..'9')
        return ThreadLocalRandom.current()
                .ints(length, 0, charPool.size)
                .asSequence()
                .map(charPool::get)
                .joinToString("")
    }

    fun cardTypeList(cardType: String): List<String> {
        val jsonArray = JSONArray(cardType.split(",").map { it.trim() })
        val jsonObject = JSONObject()
        jsonObject.put("cardType", jsonArray)
        val document = JsonPath.parse(jsonObject.toString())
        return document.read<List<String>>("$.cardType")
    }
}

fun LocalDateTime.toDateTimeArray(): Array<Int> = arrayOf(year, monthValue, dayOfMonth, hour, minute)
