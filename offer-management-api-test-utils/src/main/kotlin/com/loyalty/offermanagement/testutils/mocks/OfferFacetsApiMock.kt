@file:Suppress(
        "<PERSON><PERSON>eth<PERSON>",
        "MagicN<PERSON>ber"
)

package com.loyalty.offermanagement.testutils.mocks

import com.github.tomakehurst.wiremock.client.WireMock
import com.github.tomakehurst.wiremock.client.WireMock.aResponse
import com.github.tomakehurst.wiremock.client.WireMock.get
import com.github.tomakehurst.wiremock.client.WireMock.stubFor
import com.github.tomakehurst.wiremock.client.WireMock.urlMatching

class OfferFacetsApiMock {
    enum class StubType {
        ok, badRequest, notFound, internalServerError
    }

    fun mockFor(type: StubType) {
        when (type) {
            StubType.ok -> stubFor(get(urlMatching("^/offer-facets/v1/categories(.*)"))
                    .willReturn(aResponse()
                            .withStatus(200)
                            .withHeader("Content-Type", "application/json")
                            .withBody(OfferFacetsApiMock::class.java.getResource("/wiremock/body-content-categories-service-v1-get-category-by-id-ok.json").readText())))
            StubType.badRequest -> stubFor(get(urlMatching("^/offer-facets/v1/categories(.*)"))
                    .willReturn(aResponse()
                            .withHeader("Content-Type", "application/json")
                            .withStatus(400)
                            .withBody("""{"error": "Bad Request"}""")))
            StubType.notFound -> stubFor(get(urlMatching("^/offer-facets/v1/categories(.*)"))
                    .willReturn(aResponse()
                            .withHeader("Content-Type", "application/json")
                            .withStatus(404)
                            .withBody("""{"error": "Not Found"}""")))
            StubType.internalServerError -> stubFor(get(urlMatching("^/offer-facets/v1/categories(.*)"))
                    .willReturn(aResponse()
                            .withHeader("Content-Type", "application/json")
                            .withStatus(500)
                            .withBody("""{"error": "Internal Server Error"}""")))
        }
    }
}

fun main() {
    WireMock.configureFor("localhost", 9090)
    WireMock.removeAllMappings()
    OfferFacetsApiMock().mockFor(OfferFacetsApiMock.StubType.ok)
}
