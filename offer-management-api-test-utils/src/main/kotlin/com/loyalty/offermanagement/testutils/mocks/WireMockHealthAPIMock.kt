@file:Suppress(
        "LongMethod",
        "MagicN<PERSON>ber"
)

package com.loyalty.offermanagement.testutils.mocks

import com.github.tomakehurst.wiremock.client.WireMock
import com.github.tomakehurst.wiremock.client.WireMock.aResponse
import com.github.tomakehurst.wiremock.client.WireMock.get
import com.github.tomakehurst.wiremock.client.WireMock.stubFor
import com.github.tomakehurst.wiremock.client.WireMock.urlMatching

class WireMockHealthAPIMock {
    enum class StubType {
        ok, badRequest, notFound, internalServerError
    }

    fun mockFor(type: StubType) {
        when (type) {
            StubType.ok -> stubFor(get(urlMatching("^/health/200"))
                    .willReturn(aResponse()
                            .withStatus(200)
                            .withHeader("Content-Type", "application/json")
                            .withBody("""{"info": "200"}""")))
            StubType.badRequest -> stubFor(get(urlMatching("^/health/400"))
                    .willReturn(aResponse()
                            .withHeader("Content-Type", "application/json")
                            .withStatus(400)
                            .withBody("""{"error": "Bad Request"}""")))
            StubType.notFound -> stubFor(get(urlMatching("^/health/404"))
                    .willReturn(aResponse()
                            .withHeader("Content-Type", "application/json")
                            .withStatus(404)
                            .withBody("""{"error": "Not Found"}""")))
            StubType.internalServerError -> stubFor(get(urlMatching("^/health/500"))
                    .willReturn(aResponse()
                            .withHeader("Content-Type", "application/json")
                            .withStatus(500)
                            .withBody("""{"error": "Internal Server Error"}""")))
        }
    }
}

fun main() {
    WireMock.configureFor("localhost", 9090)
    WireMock.removeAllMappings()
    WireMockHealthAPIMock().mockFor(WireMockHealthAPIMock.StubType.ok)
}
