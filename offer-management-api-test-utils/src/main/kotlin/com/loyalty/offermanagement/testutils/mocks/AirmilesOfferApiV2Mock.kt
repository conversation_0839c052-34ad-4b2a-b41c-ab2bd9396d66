@file:Suppress(
        "<PERSON><PERSON>eth<PERSON>",
        "MagicN<PERSON>ber"
)

package com.loyalty.offermanagement.testutils.mocks

import com.github.tomakehurst.wiremock.client.WireMock
import com.github.tomakehurst.wiremock.client.WireMock.aResponse
import com.github.tomakehurst.wiremock.client.WireMock.post
import com.github.tomakehurst.wiremock.client.WireMock.put
import com.github.tomakehurst.wiremock.client.WireMock.stubFor
import com.github.tomakehurst.wiremock.client.WireMock.urlMatching

class AirmilesOfferApiV2Mock {
    enum class StubType {
        ok, badRequest, notFound, internalServerError
    }

    fun mockFor(type: StubType) {
        when (type) {
            StubType.ok -> {
                stubFor(post(urlMatching("^/offer-management/v1/offers$"))
                        .willReturn(aResponse()
                                .withStatus(200)
                                .withHeader("Content-Type", "application/json")
                                .withBody(AirmilesOfferApiV2Mock::class.java.getResource("/wiremock/body-offer-management-v1-offers-ok.json").readText())))
                stubFor(post(urlMatching("^/offer-management/v1/offers/id$"))
                        .willReturn(aResponse()
                                .withStatus(204)
                                .withHeader("Content-Type", "application/json")))
                stubFor(put(urlMatching("^/offer-management/v1/offers$"))
                        .willReturn(aResponse()
                                .withStatus(204)
                                .withHeader("Content-Type", "application/json")))
            }
            StubType.badRequest -> stubFor(post(urlMatching("^/offer-management/v1/offers(.*)"))
                    .willReturn(aResponse()
                            .withHeader("Content-Type", "application/json")
                            .withStatus(400)
                            .withBody("""{"error": "Bad Request"}""")))
            StubType.notFound -> stubFor(post(urlMatching("^/offer-management/v1/offers(.*)"))
                    .willReturn(aResponse()
                            .withHeader("Content-Type", "application/json")
                            .withStatus(404)
                            .withBody("""{"error": "Not Found"}""")))
            StubType.internalServerError -> stubFor(post(urlMatching("^/offer-management/v1/offers(.*)"))
                    .willReturn(aResponse()
                            .withHeader("Content-Type", "application/json")
                            .withStatus(500)
                            .withBody("""{"error": "Internal Server Error"}""")))
        }
    }
}

fun main() {
    WireMock.configureFor("localhost", 9090)
    WireMock.removeAllMappings()
    AirmilesOfferApiV2Mock().mockFor(AirmilesOfferApiV2Mock.StubType.ok)
}
