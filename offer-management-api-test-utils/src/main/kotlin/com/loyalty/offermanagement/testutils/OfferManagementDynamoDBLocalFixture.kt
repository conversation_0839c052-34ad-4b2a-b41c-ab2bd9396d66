@file:Suppress(
        "MagicNumber"
)

package com.loyalty.offermanagement.testutils

import com.amazonaws.services.dynamodbv2.model.AttributeDefinition
import com.amazonaws.services.dynamodbv2.model.CreateTableRequest
import com.amazonaws.services.dynamodbv2.model.KeySchemaElement
import com.amazonaws.services.dynamodbv2.model.KeyType
import com.amazonaws.services.dynamodbv2.model.ProvisionedThroughput
import com.amazonaws.services.dynamodbv2.model.ScalarAttributeType
import com.loyalty.nova.common.test.dynamodb.DynamoDBLocalFixture
import java.util.ArrayList

class OfferManagementDynamoDBLocalFixture : DynamoDBLocalFixture() {
    fun createOffersTable(tableName: String) {
        val attributeDefinitions = ArrayList<AttributeDefinition>()
        attributeDefinitions.add(AttributeDefinition("id", ScalarAttributeType.S))

        val tableKeySchema = ArrayList<KeySchemaElement>()
        tableKeySchema.add(KeySchemaElement("id", KeyType.HASH))

        createTable(CreateTableRequest()
                .withTableName(tableName)
                .withProvisionedThroughput(ProvisionedThroughput(10L, 10L))
                .withAttributeDefinitions(attributeDefinitions)
                .withKeySchema(tableKeySchema))
    }
}

/**
 *
 * var params = {
 * TableName: 'local-nova-event-bus-offer-event-store',
 * };
 * dynamodb.scan(params, function(err, data) {
 * if (err) print(err); // an error occurred
 * else print(data); // successful response
 * });
 *
 * http://localhost:8000/shell/
 */

fun main() {
    val server = OfferManagementDynamoDBLocalFixture()
    server.start(8000)
    server.createOffersTable("local-nova-event-bus-offer-event-store")
}
