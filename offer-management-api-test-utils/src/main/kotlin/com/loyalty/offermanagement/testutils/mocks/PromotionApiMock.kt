@file:Suppress(
        "<PERSON><PERSON>eth<PERSON>",
        "MagicN<PERSON>ber"
)

package com.loyalty.offermanagement.testutils.mocks

import com.github.tomakehurst.wiremock.client.WireMock
import com.github.tomakehurst.wiremock.client.WireMock.aResponse
import com.github.tomakehurst.wiremock.client.WireMock.get
import com.github.tomakehurst.wiremock.client.WireMock.stubFor
import com.github.tomakehurst.wiremock.client.WireMock.urlMatching

class PromotionApiMock {
    enum class StubType {
        ok, badRequest, notFound, internalServerError
    }

    fun mockFor(type: StubType) {
        when (type) {
            StubType.ok -> stubFor(get(urlMatching("^/promotion-service/v1/promotions(.*)"))
                    .willReturn(aResponse()
                            .withStatus(200)
                            .withHeader("Content-Type", "application/json")
                            .withBody(PromotionApiMock::class.java.getResource("/wiremock/body-content-promotion-service-v1-get-promotion-by-id-ok.json").readText())))
            StubType.badRequest -> stubFor(get(urlMatching("^/promotion-service/v1/promotions(.*)"))
                    .willReturn(aResponse()
                            .withHeader("Content-Type", "application/json")
                            .withStatus(400)
                            .withBody("""{"error": "Bad Request"}""")))
            StubType.notFound -> stubFor(get(urlMatching("^/promotion-service/v1/promotions(.*)"))
                    .willReturn(aResponse()
                            .withHeader("Content-Type", "application/json")
                            .withStatus(404)
                            .withBody("""{"error": "Not Found"}""")))
            StubType.internalServerError -> stubFor(get(urlMatching("^/promotion-service/v1/promotions(.*)"))
                    .willReturn(aResponse()
                            .withHeader("Content-Type", "application/json")
                            .withStatus(500)
                            .withBody("""{"error": "Internal Server Error"}""")))
        }
    }
}

fun main() {
    WireMock.configureFor("localhost", 9090)
    WireMock.removeAllMappings()
    PromotionApiMock().mockFor(PromotionApiMock.StubType.ok)
}
