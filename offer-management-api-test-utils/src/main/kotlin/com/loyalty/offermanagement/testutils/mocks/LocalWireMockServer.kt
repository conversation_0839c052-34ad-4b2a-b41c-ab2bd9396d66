@file:Suppress(
        "MagicNumber"
)

package com.loyalty.offermanagement.testutils.mocks

import com.github.tomakehurst.wiremock.WireMockServer
import com.github.tomakehurst.wiremock.client.WireMock
import com.github.tomakehurst.wiremock.core.WireMockConfiguration

fun main() {
    val wireMockServer = WireMockServer(WireMockConfiguration.options().port(9090))
    wireMockServer.start()

    WireMock.configureFor("localhost", 9090)

    WireMock.removeAllMappings()
    AirmilesOfferApiV2Mock().mockFor(AirmilesOfferApiV2Mock.StubType.ok)
    ContentGenerationServiceMock().mockFor(ContentGenerationServiceMock.StubType.ok)
    OfferFacetsApiMock().mockFor(OfferFacetsApiMock.StubType.ok)
    PromotionApiMock().mockFor(PromotionApiMock.StubType.ok)
}
