@file:Suppress(
        "<PERSON><PERSON>eth<PERSON>",
        "MagicN<PERSON>ber"
)

package com.loyalty.offermanagement.testutils.mocks

import com.github.tomakehurst.wiremock.client.WireMock
import com.github.tomakehurst.wiremock.client.WireMock.aResponse
import com.github.tomakehurst.wiremock.client.WireMock.post
import com.github.tomakehurst.wiremock.client.WireMock.stubFor
import com.github.tomakehurst.wiremock.client.WireMock.urlMatching

class ContentGenerationServiceMock {
    enum class StubType {
        ok, badRequest, notFound, internalServerError
    }

    fun mockFor(type: StubType) {
        when (type) {
            StubType.ok -> stubFor(post(urlMatching("^/content-generation-service/v1/generate-content(.*)"))
                    .willReturn(aResponse()
                            .withStatus(200)
                            .withHeader("Content-Type", "application/json")
                            .withBody(ContentGenerationServiceMock::class.java.getResource("/wiremock/body-content-generation-service-v1-generate-content-ok.json").readText())))
            StubType.badRequest -> stubFor(post(urlMatching("^/content-generation-service/v1/generate-content(.*)"))
                    .willReturn(aResponse()
                            .withHeader("Content-Type", "application/json")
                            .withStatus(400)
                            .withBody("""{"error": "Bad Request"}""")))
            StubType.notFound -> stubFor(post(urlMatching("^/content-generation-service/v1/generate-content(.*)"))
                    .willReturn(aResponse()
                            .withHeader("Content-Type", "application/json")
                            .withStatus(404)
                            .withBody("""{"error": "Not Found"}""")))
            StubType.internalServerError -> stubFor(post(urlMatching("^/content-generation-service/v1/generate-content(.*)"))
                    .willReturn(aResponse()
                            .withHeader("Content-Type", "application/json")
                            .withStatus(500)
                            .withBody("""{"error": "Internal Server Error"}""")))
        }
    }
}

fun main() {
    WireMock.configureFor("localhost", 9090)
    WireMock.removeAllMappings()
    ContentGenerationServiceMock().mockFor(ContentGenerationServiceMock.StubType.ok)
}
