package com.loyalty.offermanagement.testutils

import java.io.BufferedReader
import java.io.InputStreamReader
import java.nio.charset.StandardCharsets
import java.util.stream.Collectors

object CreateOfferRecordFactory {
    fun getOfferData(fileName: String, transformationData: Map<String, String>): String {
        val resource = javaClass.getResourceAsStream(fileName)
        val inputStreamReader = InputStreamReader(resource, StandardCharsets.UTF_8)
        val jsonString = BufferedReader(inputStreamReader)
                .lines()
                .collect(Collectors.joining())
                .let { jsonString -> JsonPathModifyer.modifyJson(jsonString, transformationData) }
        return jsonString
    }
}
