apply plugin: 'kotlin-spring'

dependencies {
    implementation(
            'com.fasterxml.jackson.module:jackson-module-kotlin',
            'org.jetbrains.kotlin:kotlin-stdlib-jdk8',
            'org.jetbrains.kotlin:kotlin-reflect',
            'com.amazonaws:aws-java-sdk-core',
            'com.amazonaws:aws-java-sdk-dynamodb',
            'com.amazonaws:aws-lambda-java-core',
            'com.amazonaws:aws-lambda-java-events',
            'com.fasterxml.jackson.core:jackson-databind',
            'com.amazonaws:DynamoDBLocal',
            'com.loyalty.nova:nova-common-test',
            'org.jetbrains.kotlin:kotlin-compiler-embeddable',
            'com.jayway.jsonpath:json-path',
            'com.github.tomakehurst:wiremock-jre8',
            'org.jetbrains.kotlin:kotlin-compiler-embeddable',
            'org.jetbrains.kotlin:kotlin-reflect',
            'org.jetbrains.kotlin:kotlin-script-runtime',
            'org.jetbrains.kotlin:kotlin-script-util',
            'org.jetbrains.kotlin:kotlin-scripting-compiler-embeddable',
            'org.jetbrains.kotlin:kotlin-stdlib-jdk8',
            'org.jetbrains.kotlin:kotlin-test',
            'org.json:json',
            'io.cucumber:cucumber-junit',
            'io.cucumber:cucumber-java8',
            'io.cucumber:cucumber-spring',
    )

    compile(
            'com.almworks.sqlite4java:libsqlite4java-osx',
            'com.almworks.sqlite4java:libsqlite4java-linux-amd64',
            'com.almworks.sqlite4java:libsqlite4java-linux-i386',
            'com.almworks.sqlite4java:sqlite4java-win32-x64',
            'com.almworks.sqlite4java:sqlite4java-win32-x86'
    )
}

/* Copy the native files */
task copyNativeDeps(type: Copy) {
    from(configurations.compile) {
        include "*.dylib"
        include "*.so"
        include "*.dll"
    }
    into 'build/libs'
}

task startWireMock(type: JavaExec) {
    dependsOn 'compileKotlin'
    classpath = sourceSets.main.runtimeClasspath
    main = 'com.loyalty.offermanagement.testutils.mocks.LocalWireMockServerKt'
}

compileKotlin.dependsOn copyNativeDeps