#!/usr/bin/env python

import subprocess as sp
import json

def getCredentials():
    # Define command.
    command = "aws secretsmanager get-secret-value --secret-id dev-offer-management-api-resources-admin-user"

    # Retrieve the output.
    output = sp.getoutput(command)

    # Parse output.
    parsed_output=  json.loads(output)
    secret_value = json.loads(parsed_output["SecretString"])

    # Extract credentials.
    username = secret_value["username"]
    password = secret_value["password"]

    return username, password


if __name__ == "__main__":
    username, password = getCredentials()

    # Output credentials.
    print(f"{username} {password}")
