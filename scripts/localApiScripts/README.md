# local-api-scripts

To install dependencies:

```bash
bun install
```

To run:

```bash
bun run index.ts
```

This project was created using `bun init` in bun v1.1.22. [Bun](https://bun.sh) is a fast all-in-one JavaScript runtime.


# Use cases

## Offer Publish Failure

This script can be used to publish offers again, so that the event gets created in the kinesis stream for consumers that had failed to consume the offer.

To run the script, the user must get three things:
- file/data of offerId’s (replace this placeholder in the script: REPLACE_WITH_FILE_PATH)
- token from POSTUI, should have rights to publish (replace this placeholder in the script: REPLACE_WITH_POST_UI_TOKEN)
- email associated with the token (replace this placeholder in the script: REPLACE_WITH_EMAIL)
