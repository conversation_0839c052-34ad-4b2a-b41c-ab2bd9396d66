console.log("Hello To Publish Script!");
const file = Bun.file("{REPLACE_WITH_FILE_PATH}");

const content = await file.text()

const contentArray = content.split("\n")
console.log(contentArray.length)

const myheader = new Headers();
myheader.append("x-user-email", "{REPLACE_WITH_EMAIL}");
myheader.append("Content-Type", "application/json");
myheader.append("Authorization", "Bearer {REPLACE_WITH_POST_UI_TOKEN}")


for(let i = 0; i < contentArray.length; i++){
    const body = {
        "id": `${contentArray[i]}`,
    }
    let timer1 = performance.now()
    const response = await fetch("https://uat-postui.toolbox.loyalty.com/api/v1/offers/publish",{
        method: "POST",
        headers: myheader,
        body: JSON.stringify(body),
        redirect: "follow"
    })
    let timer2 = performance.now()
    if(!response.ok){
        console.log("Error Publishing: "+ contentArray[i])
        throw new Error("Error Publishing: "+ contentArray[i])
    }
    
    console.log("Published: "+ contentArray[i])
    console.log("time taken to publish: "+ (timer2 - timer1))

}
