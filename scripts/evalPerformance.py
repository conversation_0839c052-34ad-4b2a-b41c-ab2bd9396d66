#!/usr/bin/env python
import requests
import sys
import logging

def get_http_latency(endpoint, auth):
    headers = {
        "Authorization": auth,
        "x-user-email": "<EMAIL>"
    }
    request_details = requests.get(endpoint, headers=headers)
    request_details.raise_for_status()
    latency = request_details.elapsed.total_seconds()

    return latency

if __name__ == "__main__":

    endpoint, auth, n_times, log_name, *_ = sys.argv[1:]
    logging.basicConfig(filename=f"logs/{log_name}.log", level=logging.INFO)

    for _ in range(int(n_times)):
        latency = get_http_latency(endpoint, auth)
        logging.info(latency)

