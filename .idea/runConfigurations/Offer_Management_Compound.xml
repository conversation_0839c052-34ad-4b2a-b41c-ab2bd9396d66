<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="Offer Management Compound" type="CompoundRunConfigurationType">
    <toRun name="Postgres" type="docker-deploy" />
    <toRun name="Local Event Bus DynamoDB Server" type="JetRunConfigurationType" />
    <toRun name="Local WireMock Server" type="JetRunConfigurationType" />
    <toRun name="Offer Management Server" type="JetRunConfigurationType" />
    <method v="2" />
  </configuration>
</component>