<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="Offer Management Server H2" type="JetRunConfigurationType">
    <envs>
      <env name="URL_PREFIX" value="offer-management-api" />
      <env name="CGS_ENDPOINT" value="http://localhost:9090/content-generation-service" />
      <env name="OFFER_API_V2_ENDPOINT" value="http://localhost:9090/offer-management" />
      <env name="SPRING_DATASOURCE_URL" value="*****************************************" />
      <env name="NOVA_BUS_TABLE_NAME" value="local-nova-event-bus-offer-event-store" />
      <env name="NOVA_BUS_ENDPOINT" value="http://localhost:8000" />
      <env name="NOVA_BUS_REGION" value="us-east-1" />
      <env name="AWS_ACCESS_KEY_ID" value="applmgr" />
      <env name="AWS_DEFAULT_REGION" value="us-east-1" />
      <env name="AWS_SECRET_ACCESS_KEY" value="applmgr" />
      <env name="FEATURE_TOGGLE_STREAM_EVENT_V3" value="true" />
      <env name="SPRING_PROFILES_ACTIVE" value="local" />
      <env name="OFFER_PROMOTIONS_ENDPOINT" value="http://localhost:9090/promotion-service" />
      <env name="OFFER_FACETS_ENDPOINT" value="http://localhost:9090/offer-facets" />
      <env name="SPRING_DATASOURCE_PASSWORD" value="docker" />
      <env name="SPRING_DATASOURCE_USERNAME" value="postgres" />
      <env name="OFFER_DEFAULT_IMAGE_BASE" value="https://dev-l1-amrpwl-post-images.s3.amazonaws.com/default-images/" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.loyalty.offermanagement.OfferApplicationKt" />
    <module name="offer-management-api.offer-management-api-service.main" />
    <shortenClasspath name="NONE" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
</component>