<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="Local Event Bus DynamoDB Server" type="JetRunConfigurationType">
    <envs>
      <env name="AWS_ACCESS_KEY_ID" value="applmgr" />
      <env name="AWS_DEFAULT_REGION" value="us-east-1" />
      <env name="AWS_SECRET_ACCESS_KEY" value="applmgr" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.loyalty.offermanagement.testutils.OfferManagementDynamoDBLocalFixtureKt" />
    <module name="offer-management-api.offer-management-api-test-utils.main" />
    <shortenClasspath name="ARGS_FILE" />
    <option name="VM_PARAMETERS" value="-Djava.library.path=build/libs" />
    <option name="WORKING_DIRECTORY" value="file://$MODULE_WORKING_DIR$" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
</component>