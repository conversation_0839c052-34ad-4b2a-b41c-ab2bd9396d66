<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="Offer Management Integration Tests" type="JUnit" factoryName="JUnit">
    <module name="offer-management-api.offer-management-api-it.main" />
    <shortenClasspath name="ARGS_FILE" />
    <option name="PACKAGE_NAME" value="com.loyalty.offermanagement.integration" />
    <option name="MAIN_CLASS_NAME" value="com.loyalty.offermanagement.integration.OfferManagementCucumberIT" />
    <option name="METHOD_NAME" value="" />
    <option name="TEST_OBJECT" value="class" />
    <option name="VM_PARAMETERS" value="-ea -Djava.library.path=build/libs -Dcucumber.options=&quot;--tags @local&quot; -Djmeter.home=src/main/resources/jmeter-home" />
    <option name="PARAMETERS" value="" />
    <envs>
      <env name="AWS_ACCESS_KEY_ID" value="applmgr" />
      <env name="AWS_DEFAULT_REGION" value="us-east-1" />
      <env name="AWS_SECRET_ACCESS_KEY" value="applmgr" />
      <env name="URL_PREFIX" value="offer-management-api" />
      <env name="SPRING_DATASOURCE_USERNAME" value="postgres" />
      <env name="SPRING_DATASOURCE_PASSWORD" value="docker" />
      <env name="SPRING_DATASOURCE_URL" value="*****************************************" />
      <env name="NOVA_BUS_TABLE_NAME" value="local-nova-event-bus-offer-event-store" />
      <env name="NOVA_BUS_ENDPOINT" value="http://localhost:8000" />
      <env name="NOVA_BUS_REGION" value="us-east-1" />
    </envs>
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
</component>