<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="Local WireMock Server" type="JetRunConfigurationType">
    <option name="MAIN_CLASS_NAME" value="com.loyalty.offermanagement.testutils.mocks.LocalWireMockServerKt" />
    <module name="offer-management-api.offer-management-api-test-utils.main" />
    <shortenClasspath name="ARGS_FILE" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
</component>