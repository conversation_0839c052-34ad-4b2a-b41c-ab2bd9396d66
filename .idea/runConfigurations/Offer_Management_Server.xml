<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="Offer Management Server" type="JetRunConfigurationType">
    <envs>
      <env name="AWS_ACCESS_KEY_ID" value="applmgr" />
      <env name="AWS_DEFAULT_REGION" value="us-east-1" />
      <env name="AWS_SECRET_ACCESS_KEY" value="applmgr" />
      <env name="SPRING_PROFILES_ACTIVE" value="local" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.loyalty.offermanagement.OfferApplicationKt" />
    <module name="offer-management-api.offer-management-api-service.main" />
    <shortenClasspath name="NONE" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
</component>