<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="Offer Management Unit Tests" type="JUnit" factoryName="JUnit">
    <module name="offer-management-api.offer-management-api-service.test" />
    <shortenClasspath name="ARGS_FILE" />
    <useClassPathOnly />
    <extension name="coverage">
      <pattern>
        <option name="PATTERN" value="com.loyalty.nova.*" />
        <option name="ENABLED" value="true" />
      </pattern>
    </extension>
    <extension name="net.ashald.envfile">
      <option name="IS_ENABLED" value="false" />
      <option name="IS_SUBST" value="false" />
      <option name="IS_PATH_MACRO_SUPPORTED" value="false" />
      <option name="IS_IGNORE_MISSING_FILES" value="false" />
      <ENTRIES>
        <ENTRY IS_ENABLED="true" PARSER="runconfig" />
      </ENTRIES>
    </extension>
    <option name="PACKAGE_NAME" value="com.loyalty.offermanagement.functional" />
    <option name="MAIN_CLASS_NAME" value="" />
    <option name="METHOD_NAME" value="" />
    <option name="TEST_OBJECT" value="tags" />
    <option name="VM_PARAMETERS" value="-ea -Djava.library.path=build/libs" />
    <option name="PARAMETERS" value="" />
    <envs>
      <env name="AWS_ACCESS_KEY_ID" value="applmgr" />
      <env name="AWS_DEFAULT_REGION" value="us-east-1" />
      <env name="AWS_SECRET_ACCESS_KEY" value="applmgr" />
      <env name="URL_PREFIX" value="offer-management-api" />
      <env name="CGS_ENDPOINT" value="http://localhost:2345/content-generation-service" />
      <env name="OFFER_API_V2_ENDPOINT" value="http://localhost:8081/offer-management" />
      <env name="H2_PORT" value="5432" />
      <env name="SPRING_DATASOURCE_USERNAME" value="sa" />
      <env name="SPRING_DATASOURCE_PASSWORD" value="" />
      <env name="SPRING_DATASOURCE_URL" value="jdbc:h2:mem:memorydb" />
      <env name="NOVA_BUS_TABLE_NAME" value="local-nova-event-bus-offer-event-store" />
      <env name="NOVA_BUS_ENDPOINT" value="http://localhost:8000" />
      <env name="NOVA_BUS_REGION" value="us-east-1" />
      <env name="FEATURE_TOGGLE_STREAM_EVENT_V3" value="true" />
      <env name="OFFER_FACETS_ENDPOINT" value="http://localhost:9090/offer-facets" />
      <env name="OFFER_PROMOTIONS_ENDPOINT" value="http://localhost:9090/promotion-service" />
      <env name="OFFER_DEFAULT_IMAGE_BASE" value="https://dev-l1-amrpwl-post-images.s3.amazonaws.com/default-images/" />
      <env name="environment" value="local" />
      <env name="FEATURE_TOGGLE_STREAM_EVENT_V2" value="false" />
      <env name="FEATURE_TOGGLE_CLO" value="false" />
      <env name="FEATURE_TOGGLE_AM_RECEIPTS" value="false" />

    </envs>
    <tag value="unit" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
</component>