<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="Offer Management Server PostgreSQL/Real services" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot">
    <envs>
      <env name="URL_PREFIX" value="offer-management-api" />
      <env name="CGS_ENDPOINT" value="http://localhost:2345/content-generation-service" />
      <env name="OFFER_API_V2_ENDPOINT" value="http://localhost:8081/offer-management" />
      <env name="SPRING_DATASOURCE_USERNAME" value="postgres" />
      <env name="SPRING_DATASOURCE_PASSWORD" value="docker" />
      <env name="SPRING_DATASOURCE_URL" value="*****************************************" />
      <env name="NOVA_BUS_TABLE_NAME" value="local-nova-event-bus-offer-event-store" />
      <env name="NOVA_BUS_ENDPOINT" value="http://localhost:8000" />
      <env name="NOVA_BUS_REGION" value="us-east-1" />
      <env name="AWS_ACCESS_KEY_ID" value="applmgr" />
      <env name="AWS_DEFAULT_REGION" value="us-east-1" />
      <env name="AWS_SECRET_ACCESS_KEY" value="applmgr" />
      <env name="FEATURE_TOGGLE_STREAM_EVENT_V3" value="true" />
      <env name="OFFER_PROMOTIONS_ENDPOINT" value="http://localhost:9090/promotion-service" />
      <env name="OFFER_FACETS_ENDPOINT" value="http://localhost:9090/offer-facets" />
      <env name="OFFER_DEFAULT_IMAGE_BASE" value="https://dev-l1-amrpwl-post-images.s3.amazonaws.com/default-images/" />
    </envs>
    <module name="offer-management-api.offer-management-api-service.main" />
    <option name="SHORTEN_COMMAND_LINE" value="CLASSPATH_FILE" />
    <option name="SPRING_BOOT_MAIN_CLASS" value="com.loyalty.offermanagement.OfferApplication" />
    <option name="VM_PARAMETERS" value="-Dspring.profiles.active=local -Xmx1024m -Xms512m" />
    <extension name="net.ashald.envfile">
      <option name="IS_ENABLED" value="false" />
      <option name="IS_SUBST" value="false" />
      <option name="IS_PATH_MACRO_SUPPORTED" value="false" />
      <option name="IS_IGNORE_MISSING_FILES" value="false" />
      <ENTRIES>
        <ENTRY IS_ENABLED="true" PARSER="runconfig" />
      </ENTRIES>
    </extension>
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
</component>