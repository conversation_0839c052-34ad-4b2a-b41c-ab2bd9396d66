# offer-management-api 
Service to create and publish Offer 

## Important

Before working with this project, you **must** run:  

    git submodule update --init --recursive
    git config core.hooksPath ./nova-githooks

This command configures Git to use the curated hooks. This command must be run **in the root project repository, not the nova-githook submodule**. For more information, see https://github.com/LoyaltyOne/nova-githooks.

## Build and Run

### Local
H2 database is used so no need to configure anything!

To login into the console, go to http://localhost:8080/offer-management-api/h2-console
```bash
JDBC URL=jdbc:h2:mem:memorydb
User Name=sa
Password                            // leave password blank
``` 

To access local DynamoDB shell:

http://localhost:8000/shell/

```
var dynamodb = new AWS.DynamoDB({
region: 'us-east-1',
endpoint: "http://localhost:8000"
});
var tableName = "local-nova-event-bus-offer-event-store";

var params = {
TableName: tableName,
Select: "ALL_ATTRIBUTES"
};

function doScan(response) {
if (response.error) ppJson(response.error); // an error occurred
else {
    ppJson(response.data); // successful response

    // More data.  Keep calling scan.
    if ('LastEvaluatedKey' in response.data) {
        response.request.params.ExclusiveStartKey = response.data.LastEvaluatedKey;
        dynamodb.scan(response.request.params)
            .on('complete', doScan)
            .send();
    }
}
}
console.log("Starting a Scan of the table");
dynamodb.scan(params)
.on('complete', doScan)
.send();
```

### Dev, Sole, Prod
Postgres is hosted on AWS so, it needs to be connected to that. This is done by using AWS secrets manager.
Username, Password, and other configurations are stored inside the secrets manager and they are retrieved
by AWS API inside the code

For configurations, following enviroment variables needs to be set/modified:
```bash
AWS_REGION                          // AWS Region ex: us-east-1
AWS_SECRET_ID                       // Secret ID from the Secret Manager
``` 

Datasource configurations can be overridden by defining environment variables
```bash
SPRING_DATASOURCE_URL               // JDBC url
SPRING_DATASOURCE_USERNAME          // DB Username
SPRING_DATASOURCE_PASSWORD          // DB Password
```

## Docker Compose
Authorize yourself for ECR (Note: make sure AWS saml login is done).
```bash
eval $(aws ecr get-login --no-include-email --region us-east-1  --registry-ids 824691084780)
```

Start and execute docker containers.
```bash
docker-compose up
```

## Deploy sole-offer-management-api 
aws s3 mb s3://sole-offer-management-api
aws cloudformation package --template-file ./cfn/sam-templates/template.yaml --s3-bucket sole-offer-management-api --s3-prefix s3-prefix --output-template-file ./build/packaged-template.yaml
aws cloudformation deploy --template-file ./build/packaged-template.yaml --stack-name sole-offer-management-api --capabilities CAPABILITY_IAM

## Scorecard

[Scorecard - level 2 - API](scorecard.md)

## Integration Test
Steps
1) Run the OfferManagement Compound, it should include (Docker 'Postgres'
   Kotlin 'Local Event Bus DynamoDB Server'
   Kotlin 'Local WireMock Server'
   Kotlin 'Offer Management Server')
2) OfferManagement Compound should include all the three Server Local Event Bus DynamoDB Server, Local WireMock Server and Offer Management Server.
3) All the above Server should be up and running.
4) Docker should be installed and running.
5) To run Specific test case it should have the following settings
   Glue:
     com.loyalty.offermanagement.integration
     cucumber.api.spring
     com.loyalty.nova.common.test.performance
     com.loyalty.nova.common.test.integration
6) To run all the test case it should have the following settings
    Glue:
   -ea
   -Djava.library.path=build/libs
   -Dcucumber.options="--tags @local"
   -Djmeter.home=src/main/resources/jmeter-home
7) Comment the following code Under OfferStepDefs
     @Before and @After
     wireMockServer.start()